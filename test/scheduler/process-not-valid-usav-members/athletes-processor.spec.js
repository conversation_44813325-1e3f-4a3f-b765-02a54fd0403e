'use strict';

const AthletesProcessor = require('../../../scheduler/tasks/process-not-valid-usav-members/athletes-processor');
const historyActions = require('../../../scheduler/tasks/process-not-valid-usav-members/history-actions');
const { CANCELED_MEMBER_STATUS } = require('../../../api/lib/SEUtilsService');

// Test fixtures
const fixtures = {
    memberWithAAU: require('./fixture/member-with-aau-and-events.json'),
    memberWithoutAAU: require('./fixture/member-without-aau-no-events.json'),
    memberWithTeam: require('./fixture/member-without-aau-with-team.json'),
    multipleMembers: require('./fixture/multiple-members.json')
};

describe('AthletesProcessor', function () {

    let stubs = {};
    const mockTransaction = {
        query: sinon.stub(),
        commit: sinon.stub(),
        rollback: sinon.stub(),
        isCommited: false
    };

    before(() => {
        stubs.dbQuery = sinon.stub(Db, 'query');
        stubs.dbBegin = sinon.stub(Db, 'begin').resolves(mockTransaction);

        // Stub history actions
        stubs.eventRosterAction = sinon.stub(historyActions, 'saveAthleteRemovedFromEventRosterAction').resolves();
        stubs.clubRosterAction = sinon.stub(historyActions, 'saveAthleteRemovedFromClubRosterAction').resolves();
        stubs.deletedAction = sinon.stub(historyActions, 'saveAthleteDeletedAction').resolves();
        stubs.usavDataAction = sinon.stub(historyActions, 'saveAthleteUSAVDataRemovedAction').resolves();
    });

    after(() => {
        Object.values(stubs).forEach(stub => stub.restore());
    });

    afterEach(() => {
        sinon.resetHistory();
        mockTransaction.isCommited = false;
    });

    describe('getMembers()', () => {

        it('should return empty array when no members found', async () => {
            stubs.dbQuery.resolves({ rows: [] });

            const result = await AthletesProcessor.getMembers(global.sails.config.sw_season.current);

            expect(result).to.be.an('array').that.is.empty;
            expect(stubs.dbQuery.calledOnce).to.be.true;
        });

        it('should return members with correct structure', async () => {
            stubs.dbQuery.resolves({ rows: fixtures.multipleMembers });

            const result = await AthletesProcessor.getMembers(global.sails.config.sw_season.current);

            expect(result).to.have.length(3);
            expect(result[0]).to.have.all.keys([
                'master_athlete_id', 'master_club_id', 'master_team_id',
                'has_aau_membership', 'event_roster_teams'
            ]);
        });

        it('should use correct query parameters', async () => {
            stubs.dbQuery.resolves({ rows: [] });

            await AthletesProcessor.getMembers(global.sails.config.sw_season.current);

            const [query, params] = stubs.dbQuery.firstCall.args;
            expect(query).to.include('SELECT').and.include('LIMIT $3');
            expect(params).to.deep.equal([CANCELED_MEMBER_STATUS, global.sails.config.sw_season.current, 100]);
        });

        it('should handle database errors', async () => {
            const dbError = new Error('Database error');
            stubs.dbQuery.rejects(dbError);

            await expect(AthletesProcessor.getMembers(global.sails.config.sw_season.current)).to.be.rejectedWith(dbError);
        });

    });

    describe('processMember()', () => {

        describe('validation', () => {

            it('should validate required fields', async () => {
                const testCases = [
                    { data: {}, expectedError: 'master_athlete_id is required' },
                    { data: { master_athlete_id: 123 }, expectedError: 'master_club_id is required' },
                    { data: { master_athlete_id: 123, master_club_id: 456, has_aau_membership: 'invalid' },
                      expectedError: 'has_aau_membership should be boolean' }
                ];

                for (const testCase of testCases) {
                    await expect(AthletesProcessor.processMember(testCase.data))
                        .to.be.rejectedWith(testCase.expectedError);
                }
            });

        });

        describe('transaction handling', () => {

            it('should commit transaction on success', async () => {
                mockTransaction.query.resolves();
                mockTransaction.commit.resolves();

                await AthletesProcessor.processMember(fixtures.memberWithoutAAU);

                expect(stubs.dbBegin.calledOnce).to.be.true;
                expect(mockTransaction.commit.calledOnce).to.be.true;
            });

            it('should rollback transaction on error', async () => {
                const testError = new Error('Database error');
                mockTransaction.query.rejects(testError);
                mockTransaction.rollback.resolves();

                await expect(AthletesProcessor.processMember(fixtures.memberWithoutAAU))
                    .to.be.rejectedWith(testError);
                expect(mockTransaction.rollback.calledOnce).to.be.true;
            });

        });

        describe('member processing workflows', () => {

            beforeEach(() => {
                mockTransaction.query.resolves();
                mockTransaction.commit.resolves();
            });

            it('should process member with AAU membership correctly', async () => {
                await AthletesProcessor.processMember(fixtures.memberWithAAU);

                expect(mockTransaction.query.calledTwice).to.be.true; // roster + athlete update
                expect(stubs.eventRosterAction.callCount).to.equal(fixtures.memberWithAAU.event_roster_teams.length);
                expect(stubs.usavDataAction.calledOnce).to.be.true;
                expect(stubs.deletedAction.called).to.be.false;
            });

            it('should process member without AAU membership correctly', async () => {
                await AthletesProcessor.processMember(fixtures.memberWithoutAAU);

                expect(mockTransaction.query.calledOnce).to.be.true; // only athlete update
                expect(stubs.eventRosterAction.called).to.be.false;
                expect(stubs.deletedAction.calledOnce).to.be.true;
                expect(stubs.usavDataAction.called).to.be.false;
            });

            it('should process member with team correctly', async () => {
                await AthletesProcessor.processMember(fixtures.memberWithTeam);

                expect(mockTransaction.query.calledTwice).to.be.true; // roster + athlete update
                expect(stubs.eventRosterAction.calledOnce).to.be.true;
                expect(stubs.clubRosterAction.called).to.be.true;
                expect(stubs.deletedAction.calledOnce).to.be.true;
            });

        });

    });

    describe('history actions integration', () => {

        beforeEach(() => {
            mockTransaction.query.resolves();
            mockTransaction.commit.resolves();
        });

        it('should save correct history actions for member with AAU membership', async () => {
            await AthletesProcessor.processMember(fixtures.memberWithAAU);

            expect(stubs.eventRosterAction.callCount).to.equal(fixtures.memberWithAAU.event_roster_teams.length);
            expect(stubs.usavDataAction.calledOnce).to.be.true;
            expect(stubs.deletedAction.called).to.be.false;
            expect(stubs.clubRosterAction.called).to.be.true;
        });

        it('should save correct history actions for member without AAU membership', async () => {
            await AthletesProcessor.processMember(fixtures.memberWithoutAAU);

            expect(stubs.deletedAction.calledOnce).to.be.true;
            expect(stubs.eventRosterAction.called).to.be.false;
            expect(stubs.usavDataAction.called).to.be.false;
            expect(stubs.clubRosterAction.called).to.be.false;
        });

        it('should save correct history actions for member with team', async () => {
            await AthletesProcessor.processMember(fixtures.memberWithTeam);

            expect(stubs.eventRosterAction.calledOnce).to.be.true;
            expect(stubs.deletedAction.calledOnce).to.be.true;
            expect(stubs.clubRosterAction.called).to.be.true;
            expect(stubs.usavDataAction.called).to.be.false;
        });

        it('should rollback transaction when history action fails', async () => {
            const historyError = new Error('History failed');
            stubs.deletedAction.rejects(historyError);
            mockTransaction.rollback.resolves();

            await expect(AthletesProcessor.processMember(fixtures.memberWithoutAAU))
                .to.be.rejectedWith(historyError);
            expect(mockTransaction.rollback.calledOnce).to.be.true;
        });

    });

});
