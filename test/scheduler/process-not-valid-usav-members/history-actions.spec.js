'use strict';

const historyActions = require('../../../scheduler/tasks/process-not-valid-usav-members/history-actions');
const {
    CLUB_TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
    CLUB_ATHLETE_SANCTIONING_USAV_REMOVED_BY_SYSTEM,
    CLUB_ATHLETE_DELETED_BY_SYSTEM,
    TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
} = require('../../../api/constants/notification-actions');

describe('HistoryActions', function () {
    
    let stubs = {};
    const mockTransaction = {
        query: sinon.stub()
    };

    const mockMemberData = {
        master_athlete_id: 123,
        master_club_id: 456,
        master_team_id: 789,
        event_id: 101,
        roster_team_id: 202,
        roster_club_id: 456,
        roster_athlete_id: 303
    };

    beforeEach(() => {
        mockTransaction.query.resolves();
    });

    afterEach(() => {
        sinon.resetHistory();
    });

    describe('club history actions', () => {

        it('should save club roster removal action with correct data', async () => {
            await historyActions.saveAthleteRemovedFromClubRosterAction(mockTransaction, mockMemberData);

            expect(mockTransaction.query.calledOnce).to.be.true;
            const query = mockTransaction.query.firstCall.args[0];

            expect(query.toString()).to.include('club_history');
            expect(query._single.insert).to.deep.include({
                master_club_id: mockMemberData.master_club_id,
                master_athlete_id: mockMemberData.master_athlete_id,
                action: CLUB_TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM
            });
        });

        it('should save USAV data removal action with correct action type', async () => {
            await historyActions.saveAthleteUSAVDataRemovedAction(mockTransaction, mockMemberData);

            const query = mockTransaction.query.firstCall.args[0];
            expect(query._single.insert.action).to.equal(CLUB_ATHLETE_SANCTIONING_USAV_REMOVED_BY_SYSTEM);
        });

        it('should save athlete deletion action with correct action type', async () => {
            await historyActions.saveAthleteDeletedAction(mockTransaction, mockMemberData);

            const query = mockTransaction.query.firstCall.args[0];
            expect(query._single.insert.action).to.equal(CLUB_ATHLETE_DELETED_BY_SYSTEM);
        });

        it('should validate required fields', async () => {
            await expect(historyActions.saveAthleteRemovedFromClubRosterAction(null, mockMemberData))
                .to.be.rejectedWith('Transaction should be defined');

            const invalidData = { ...mockMemberData };
            delete invalidData.master_club_id;
            await expect(historyActions.saveAthleteRemovedFromClubRosterAction(mockTransaction, invalidData))
                .to.be.rejectedWith('Member should have master_club_id');
        });

    });

    describe('event history actions', () => {

        it('should save event roster removal action with correct data', async () => {
            await historyActions.saveAthleteRemovedFromEventRosterAction(mockTransaction, mockMemberData);

            expect(mockTransaction.query.calledOnce).to.be.true;
            const query = mockTransaction.query.firstCall.args[0];

            expect(query.toString()).to.include('event_change');
            expect(query._single.insert).to.deep.include({
                event_id: mockMemberData.event_id,
                roster_team_id: mockMemberData.roster_team_id,
                action: TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM
            });
        });

        it('should validate required event fields', async () => {
            const testCases = [
                { field: 'roster_team_id', error: 'Member should have roster_team_id' },
                { field: 'event_id', error: 'Member should have event_id' },
                { field: 'roster_club_id', error: 'Member should have roster_club_id' }
            ];

            for (const testCase of testCases) {
                const invalidData = { ...mockMemberData };
                delete invalidData[testCase.field];

                await expect(historyActions.saveAthleteRemovedFromEventRosterAction(mockTransaction, invalidData))
                    .to.be.rejectedWith(testCase.error);
            }
        });

    });

    describe('error handling', () => {

        it('should handle database errors gracefully', async () => {
            const dbError = new Error('Database connection failed');
            mockTransaction.query.rejects(dbError);

            await expect(historyActions.saveAthleteDeletedAction(mockTransaction, mockMemberData))
                .to.be.rejectedWith(dbError);
        });

    });

});
