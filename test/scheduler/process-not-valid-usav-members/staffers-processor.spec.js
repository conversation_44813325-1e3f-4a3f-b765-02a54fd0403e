'use strict';

const StaffersProcessor = require('../../../scheduler/tasks/process-not-valid-usav-members/staffers-processor');
const historyActions = require('../../../scheduler/tasks/process-not-valid-usav-members/history-actions');
const { CANCELED_MEMBER_STATUS, MEMBER_TYPE } = require('../../../api/lib/SEUtilsService');

// Test fixtures
const fixtures = {
    staffWithAAU: require('./fixture/staff-member-with-aau-and-events.json'),
    staffWithoutAAU: require('./fixture/staff-member-without-aau-no-events.json'),
    staffWithTeam: require('./fixture/staff-member-without-aau-with-team.json'),
    multipleStaff: require('./fixture/multiple-staff-members.json')
};

describe('StaffersProcessor', function () {

    let stubs = {};
    // const global.sails.config.sw_season.current = global.sails.config.sw_season.current;
    const mockTransaction = {
        query: sinon.stub(),
        commit: sinon.stub(),
        rollback: sinon.stub(),
        isCommited: false
    };

    before(() => {
        stubs.dbQuery = sinon.stub(Db, 'query');
        stubs.dbBegin = sinon.stub(Db, 'begin').resolves(mockTransaction);

        // Stub history actions
        stubs.eventRosterAction = sinon.stub(historyActions, 'saveStaffRemovedFromEventRosterAction').resolves();
        stubs.clubRosterAction = sinon.stub(historyActions, 'saveStaffRemovedFromClubRosterAction').resolves();
        stubs.deletedAction = sinon.stub(historyActions, 'saveStaffDeletedAction').resolves();
        stubs.usavDataAction = sinon.stub(historyActions, 'saveStaffUSAVDataRemovedAction').resolves();
    });

    after(() => {
        Object.values(stubs).forEach(stub => stub.restore());
    });

    afterEach(() => {
        sinon.resetHistory();
        mockTransaction.isCommited = false;
    });

    describe('getMembers()', () => {

        it('should return empty array when no members found', async () => {
            stubs.dbQuery.resolves({ rows: [] });

            const result = await StaffersProcessor.getMembers(global.sails.config.sw_season.current);

            expect(result).to.be.an('array').that.is.empty;
            expect(stubs.dbQuery.calledOnce).to.be.true;
        });

        it('should return members with correct structure', async () => {
            stubs.dbQuery.resolves({ rows: fixtures.multipleStaff });

            const result = await StaffersProcessor.getMembers(global.sails.config.sw_season.current);

            expect(result).to.have.length(3);
            expect(result[0]).to.have.all.keys([
                'master_staff_id', 'master_club_id', 'has_aau_membership',
                'event_roster_teams', 'club_roster_teams'
            ]);
        });

        it('should use correct query parameters', async () => {
            stubs.dbQuery.resolves({ rows: [] });

            await StaffersProcessor.getMembers(global.sails.config.sw_season.current);

            const [query, params] = stubs.dbQuery.firstCall.args;
            expect(query).to.include('SELECT').and.include('LIMIT $3');
            expect(params).to.deep.equal([CANCELED_MEMBER_STATUS, global.sails.config.sw_season.current, 100]);
        });

        it('should handle database errors', async () => {
            const dbError = new Error('Database error');
            stubs.dbQuery.rejects(dbError);

            await expect(StaffersProcessor.getMembers(global.sails.config.sw_season.current)).to.be.rejectedWith(dbError);
        });

    });

    describe('processMember()', () => {

        describe('validation', () => {

            it('should validate required fields', async () => {
                const testCases = [
                    { data: {}, expectedError: 'master_staff_id is required' },
                    { data: { master_staff_id: 123 }, expectedError: 'master_club_id is required' },
                    { data: { master_staff_id: 123, master_club_id: 456, has_aau_membership: 'invalid' },
                      expectedError: 'has_aau_membership should be boolean' }
                ];

                for (const testCase of testCases) {
                    await expect(StaffersProcessor.processMember(testCase.data))
                        .to.be.rejectedWith(testCase.expectedError);
                }
            });

            it('should validate event roster teams data', async () => {
                const invalidEventRosterData = {
                    master_staff_id: 123,
                    master_club_id: 456,
                    has_aau_membership: true,
                    event_roster_teams: [
                        {
                            roster_club_id: 456,
                            roster_team_id: 789
                            // missing roster_staff_role_id and event_id
                        }
                    ],
                    club_roster_teams: []
                };

                await expect(StaffersProcessor.processMember(invalidEventRosterData))
                    .to.be.rejectedWith('roster staff role id is required');
            });

            it('should validate club roster teams data', async () => {
                const invalidClubRosterData = {
                    master_staff_id: 123,
                    master_club_id: 456,
                    has_aau_membership: false,
                    event_roster_teams: [],
                    club_roster_teams: [
                        {
                            master_team_id: 789
                            // missing master_staff_role_id
                        }
                    ]
                };

                await expect(StaffersProcessor.processMember(invalidClubRosterData))
                    .to.be.rejectedWith('master staff role id is required');
            });

            it('should validate that event_roster_teams is an array', async () => {
                const invalidData = {
                    master_staff_id: 123,
                    master_club_id: 456,
                    has_aau_membership: true,
                    event_roster_teams: 'not an array',
                    club_roster_teams: []
                };

                await expect(StaffersProcessor.processMember(invalidData))
                    .to.be.rejectedWith('eventRosterTeams should be an array');
            });

            it('should validate that club_roster_teams is an array', async () => {
                const invalidData = {
                    master_staff_id: 123,
                    master_club_id: 456,
                    has_aau_membership: false,
                    event_roster_teams: [],
                    club_roster_teams: 'not an array'
                };

                await expect(StaffersProcessor.processMember(invalidData))
                    .to.be.rejectedWith('clubRosterTeams should be an array');
            });

        });

        describe('transaction handling', () => {

            it('should commit transaction on success', async () => {
                mockTransaction.query.resolves();
                mockTransaction.commit.resolves();

                await StaffersProcessor.processMember(fixtures.staffWithoutAAU);

                expect(stubs.dbBegin.calledOnce).to.be.true;
                expect(mockTransaction.commit.calledOnce).to.be.true;
            });

            it('should rollback transaction on error', async () => {
                const testError = new Error('Database error');
                mockTransaction.query.rejects(testError);
                mockTransaction.rollback.resolves();

                await expect(StaffersProcessor.processMember(fixtures.staffWithoutAAU))
                    .to.be.rejectedWith(testError);
                expect(mockTransaction.rollback.calledOnce).to.be.true;
            });

        });

        describe('member processing workflows', () => {

            beforeEach(() => {
                mockTransaction.query.resolves();
                mockTransaction.commit.resolves();
            });

            it('should process staff with AAU membership correctly', async () => {
                await StaffersProcessor.processMember(fixtures.staffWithAAU);

                expect(mockTransaction.query.calledTwice).to.be.true; // roster + staff update
                expect(stubs.eventRosterAction.callCount).to.equal(fixtures.staffWithAAU.event_roster_teams.length);
                expect(stubs.usavDataAction.calledOnce).to.be.true;
                expect(stubs.deletedAction.called).to.be.false;
            });

            it('should process staff without AAU membership correctly', async () => {
                await StaffersProcessor.processMember(fixtures.staffWithoutAAU);

                expect(mockTransaction.query.calledOnce).to.be.true; // only staff update
                expect(stubs.eventRosterAction.called).to.be.false;
                expect(stubs.deletedAction.calledOnce).to.be.true;
                expect(stubs.usavDataAction.called).to.be.false;
            });

            it('should process staff with team correctly', async () => {
                await StaffersProcessor.processMember(fixtures.staffWithTeam);

                expect(mockTransaction.query.callCount).to.equal(3); // event roster + club roster + staff update
                expect(stubs.eventRosterAction.calledOnce).to.be.true;
                expect(stubs.clubRosterAction.calledOnce).to.be.true;
                expect(stubs.deletedAction.calledOnce).to.be.true;
            });

            it('should not process club roster for staff with AAU membership', async () => {
                const staffWithAAUAndClubTeams = {
                    ...fixtures.staffWithAAU,
                    club_roster_teams: [
                        {
                            master_staff_role_id: 1,
                            master_team_id: 789
                        }
                    ]
                };

                await StaffersProcessor.processMember(staffWithAAUAndClubTeams);

                // Should not remove from club roster because has_aau_membership is true
                expect(stubs.clubRosterAction.called).to.be.false;
                expect(stubs.usavDataAction.calledOnce).to.be.true;
                expect(stubs.deletedAction.called).to.be.false;
            });

            it('should handle staff with empty event and club rosters', async () => {
                const staffWithEmptyRosters = {
                    master_staff_id: 999,
                    master_club_id: 999,
                    has_aau_membership: true,
                    event_roster_teams: [],
                    club_roster_teams: []
                };

                await StaffersProcessor.processMember(staffWithEmptyRosters);

                expect(mockTransaction.query.calledOnce).to.be.true; // only staff update
                expect(stubs.eventRosterAction.called).to.be.false;
                expect(stubs.clubRosterAction.called).to.be.false;
                expect(stubs.usavDataAction.calledOnce).to.be.true;
                expect(stubs.deletedAction.called).to.be.false;
            });

        });

    });

    describe('history actions integration', () => {

        beforeEach(() => {
            mockTransaction.query.resolves();
            mockTransaction.commit.resolves();
        });

        it('should save correct history actions for staff with AAU membership', async () => {
            await StaffersProcessor.processMember(fixtures.staffWithAAU);

            expect(stubs.eventRosterAction.callCount).to.equal(fixtures.staffWithAAU.event_roster_teams.length);
            expect(stubs.usavDataAction.calledOnce).to.be.true;
            expect(stubs.deletedAction.called).to.be.false;
            expect(stubs.clubRosterAction.called).to.be.false;
        });

        it('should save correct history actions for staff without AAU membership', async () => {
            await StaffersProcessor.processMember(fixtures.staffWithoutAAU);

            expect(stubs.deletedAction.calledOnce).to.be.true;
            expect(stubs.eventRosterAction.called).to.be.false;
            expect(stubs.usavDataAction.called).to.be.false;
            expect(stubs.clubRosterAction.called).to.be.false;
        });

        it('should save correct history actions for staff with team', async () => {
            await StaffersProcessor.processMember(fixtures.staffWithTeam);

            expect(stubs.eventRosterAction.calledOnce).to.be.true;
            expect(stubs.clubRosterAction.calledOnce).to.be.true;
            expect(stubs.deletedAction.calledOnce).to.be.true;
            expect(stubs.usavDataAction.called).to.be.false;
        });

        it('should rollback transaction when history action fails', async () => {
            const historyError = new Error('History failed');
            stubs.deletedAction.rejects(historyError);
            mockTransaction.rollback.resolves();

            await expect(StaffersProcessor.processMember(fixtures.staffWithoutAAU))
                .to.be.rejectedWith(historyError);
            expect(mockTransaction.rollback.calledOnce).to.be.true;
        });

    });

    describe('error handling and edge cases', () => {

        beforeEach(() => {
            mockTransaction.query.resolves();
            mockTransaction.commit.resolves();
        });

        it('should handle SportEngineMemberService dependency for AAU members', async () => {
            // Mock SportEngineMemberService for AAU member processing
            const mockEmptyFields = {
                organization_code: null,
                usav_number: null,
                membership_status: null,
                safesport_statusid: null,
                safesport_start_date: null,
                safesport_end_date: null,
                sportengine_sync: null,
                is_impact: null,
                bg_screening: null
            };

            // Create a stub for the global SportEngineMemberService
            const originalService = global.SportEngineMemberService;
            global.SportEngineMemberService = {
                import: {
                    process: {
                        memberImport: {
                            prepareEmptyUsavFields: sinon.stub().withArgs(MEMBER_TYPE.STAFF).returns(mockEmptyFields)
                        }
                    }
                }
            };

            await StaffersProcessor.processMember(fixtures.staffWithAAU);

            expect(global.SportEngineMemberService.import.process.memberImport.prepareEmptyUsavFields.calledWith(MEMBER_TYPE.STAFF)).to.be.true;
            expect(stubs.usavDataAction.calledOnce).to.be.true;
            expect(stubs.deletedAction.called).to.be.false;

            // Restore original service
            global.SportEngineMemberService = originalService;
        });

        it('should handle transaction rollback when commit fails', async () => {
            const commitError = new Error('Commit failed');
            mockTransaction.query.resolves();
            mockTransaction.commit.rejects(commitError);
            mockTransaction.rollback.resolves();

            // Make sure history actions resolve so commit is reached
            stubs.deletedAction.resolves();

            await expect(StaffersProcessor.processMember(fixtures.staffWithoutAAU))
                .to.be.rejectedWith(commitError);
            expect(mockTransaction.rollback.calledOnce).to.be.true;
        });

        it('should handle multiple event roster teams correctly', async () => {
            const staffWithMultipleEvents = {
                master_staff_id: 999,
                master_club_id: 999,
                has_aau_membership: true,
                event_roster_teams: [
                    {
                        roster_staff_role_id: 1,
                        roster_club_id: 999,
                        roster_team_id: 1001,
                        event_id: 201
                    },
                    {
                        roster_staff_role_id: 2,
                        roster_club_id: 999,
                        roster_team_id: 1002,
                        event_id: 202
                    },
                    {
                        roster_staff_role_id: 3,
                        roster_club_id: 999,
                        roster_team_id: 1003,
                        event_id: 203
                    }
                ],
                club_roster_teams: []
            };

            await StaffersProcessor.processMember(staffWithMultipleEvents);

            expect(stubs.eventRosterAction.callCount).to.equal(3);
            expect(stubs.usavDataAction.calledOnce).to.be.true;
        });

        it('should handle multiple club roster teams correctly', async () => {
            // Reset stubs to ensure they resolve
            stubs.clubRosterAction.resolves();
            stubs.deletedAction.resolves();

            const staffWithMultipleClubTeams = {
                master_staff_id: 999,
                master_club_id: 999,
                has_aau_membership: false,
                event_roster_teams: [],
                club_roster_teams: [
                    {
                        master_staff_role_id: 1,
                        master_team_id: 1001
                    },
                    {
                        master_staff_role_id: 2,
                        master_team_id: 1002
                    }
                ]
            };

            await StaffersProcessor.processMember(staffWithMultipleClubTeams);

            expect(stubs.clubRosterAction.callCount).to.equal(2);
            expect(stubs.deletedAction.calledOnce).to.be.true;
        });

    });

});
