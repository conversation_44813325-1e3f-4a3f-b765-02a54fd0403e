{"id": "evt_3Ky8ylBXhnF9ioPp1m6bKZZ9", "object": "event", "api_version": "2019-09-09", "created": 1652250378, "data": {"object": {"id": "pi_3KyJ3lBXhnF9ioPp0a3S3kv1", "object": "payment_intent", "amount": 1077700, "amount_capturable": 0, "amount_details": {"tip": {}}, "amount_received": 0, "application": null, "application_fee_amount": 10849, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"id": "py_3KyJ3lBXhnF9ioPp0Bhbg9mH", "object": "charge", "amount": 1077700, "amount_captured": 1077700, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": 10849, "balance_transaction": null, "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": null, "captured": true, "created": **********, "currency": "usd", "customer": null, "description": null, "destination": "acct_1KgsHkLDYnTA1fGW", "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": "payment_method_microdeposit_failed", "failure_message": "Microdeposit transfers failed. Please check the account, institution and transit numbers.", "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"project": "sw", "email": "<EMAIL>", "phone": "**********", "event_name": "ACH testing", "event_id": "22012", "total": "$10668.51", "stripe_fee": "$103.49", "additional_fee": "$0.00", "sw_fee": "$5.00"}, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "order": null, "outcome": {"network_status": "declined_by_network", "reason": "generic_decline", "risk_level": "not_assessed", "seller_message": "The bank did not return any further details with this decline.", "type": "issuer_declined"}, "paid": false, "payment_intent": "pi_3KyJ3lBXhnF9ioPp0a3S3kv1", "payment_method": "pm_1KyJ8bBXhnF9ioPphvVcccol", "payment_method_details": {"type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "zkqJ5nPpFIShoLkZ", "last4": "6661", "routing_number": "*********"}}, "receipt_email": null, "receipt_number": null, "receipt_url": null, "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/py_3KyJ3lBXhnF9ioPp0Bhbg9mH/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "failed", "transfer_data": {"amount": null, "destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": "group_py_3KyJ3lBXhnF9ioPp0Bhbg9mH"}], "has_more": false, "total_count": 1, "url": "/v1/charges?payment_intent=pi_3KyJ3lBXhnF9ioPp0a3S3kv1"}, "client_secret": "pi_3KyJ3lBXhnF9ioPp0a3S3kv1_secret_pxBu4WRBN6vUaQpjBIBFUw24t", "confirmation_method": "automatic", "created": **********, "currency": "usd", "customer": null, "description": null, "invoice": null, "last_payment_error": {"code": "pm_usBankAccount_insufficientFunds", "decline_code": "generic_decline", "doc_url": "https://stripe.com/docs/error-codes/payment-method-microdeposit-failed", "message": "The payment fails due to insufficient funds.", "payment_method": {"id": "pm_1KyJ8bBXhnF9ioPphvVcccol", "object": "payment_method", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "created": **********, "customer": null, "livemode": false, "metadata": {}, "type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "OTjoC6ZpSUcp9nsW", "last4": "1116", "routing_number": "*********"}}, "type": "card_error"}, "livemode": false, "metadata": {"project": "sw", "email": "<EMAIL>", "phone": "**********", "event_name": "ACH testing", "event_id": "22012", "total": "$10668.51", "stripe_fee": "$103.49", "additional_fee": "$0.00", "sw_fee": "$5.00"}, "next_action": null, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "payment_method": null, "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}, "us_bank_account": {"verification_method": "automatic"}}, "payment_method_types": ["card", "us_bank_account"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": "off_session", "shipping": null, "source": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "requires_payment_method", "transfer_data": {"destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": null}}, "livemode": false, "pending_webhooks": 2, "request": {"id": null, "idempotency_key": null}, "type": "payment_intent.payment_failed"}