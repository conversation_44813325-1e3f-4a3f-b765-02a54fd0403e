{"id": "py_3KyJ3lBXhnF9ioPp0Bhbg9mH", "object": "charge", "amount": 1077700, "amount_captured": 1077700, "amount_refunded": 0, "application": null, "application_fee": "fee_1KyJ8dLDYnTA1fGWJwD6hKGF", "application_fee_amount": 10849, "balance_transaction": {"id": "txn_3KyJ3lBXhnF9ioPp0M6LdixD", "object": "balance_transaction", "amount": 1077700, "available_on": 1652289349, "created": 1652289349, "currency": "usd", "description": null, "exchange_rate": null, "fee": 500, "fee_details": [{"amount": 500, "application": null, "currency": "usd", "description": "Stripe processing fees", "type": "stripe_fee"}], "net": 1077200, "reporting_category": "charge", "source": "py_3KyJ3lBXhnF9ioPp0Bhbg9mH", "status": "available", "type": "payment"}, "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": null, "captured": true, "created": 1652289349, "currency": "usd", "customer": null, "description": null, "destination": "acct_1KgsHkLDYnTA1fGW", "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"project": "sw", "email": "<EMAIL>", "phone": "**********", "event_name": "ACH testing", "event_id": "22012", "total": "$10668.51", "stripe_fee": "$103.49", "additional_fee": "$0.00", "sw_fee": "$5.00"}, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "order": null, "outcome": null, "paid": false, "payment_intent": "pi_3KyJ3lBXhnF9ioPp0a3S3kv1", "payment_method": "pm_1KyJ8bBXhnF9ioPphvVcccol", "payment_method_details": {"type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "OTjoC6ZpSUcp9nsW", "last4": "1116", "routing_number": "*********"}}, "receipt_email": null, "receipt_number": null, "receipt_url": null, "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/py_3KyJ3lBXhnF9ioPp0Bhbg9mH/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "failed", "transfer": "tr_3KyJ3lBXhnF9ioPp0xW0xWRg", "transfer_data": {"amount": null, "destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": null}