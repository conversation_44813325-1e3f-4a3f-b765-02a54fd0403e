{"id": "py_3Kw0LsBXhnF9ioPp0e0e7Dsp", "object": "charge", "amount": 1077700, "amount_captured": 1077700, "amount_refunded": 0, "application": null, "application_fee": "fee_1Kw0MlLDYnTA1fGWwmAF5LSk", "application_fee_amount": 10849, "balance_transaction": {"id": "txn_3Kw0LsBXhnF9ioPp0Ef0rXGT", "object": "balance_transaction", "amount": 1077700, "available_on": 1651740524, "created": 1651740524, "currency": "usd", "description": null, "exchange_rate": null, "fee": 500, "fee_details": [{"amount": 500, "application": null, "currency": "usd", "description": "Stripe processing fees", "type": "stripe_fee"}], "net": 1077200, "reporting_category": "charge", "source": "py_3Kw0LsBXhnF9ioPp0e0e7Dsp", "status": "available", "type": "payment"}, "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": null, "captured": true, "created": 1651740524, "currency": "usd", "customer": null, "description": null, "destination": "acct_1KgsHkLDYnTA1fGW", "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"total": "$10668.51", "stripe_fee": "$103.49", "phone": "0000000000", "sw_fee": "$5.00", "event_name": "ACH testing", "email": "<EMAIL>", "event_id": "22012", "additional_fee": "$0.00", "project": "sw"}, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "not_assessed", "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_3Kw0LsBXhnF9ioPp0K56SCxW", "payment_method": "pm_1Kw0MZBXhnF9ioPplZNoMuCq", "payment_method_details": {"type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "hJtXanQIKIwyiRum", "last4": "6789", "routing_number": "*********"}}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/acct_1FMZ07BXhnF9ioPp/py_3Kw0LsBXhnF9ioPp0e0e7Dsp/rcpt_LdGuTrQ39cojeESPHkt1hfZ6U5xRFG4", "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/py_3Kw0LsBXhnF9ioPp0e0e7Dsp/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "succeeded", "transfer": "tr_3Kw0LsBXhnF9ioPp0CtG864G", "transfer_data": {"amount": null, "destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": "group_pi_3Kw0LsBXhnF9ioPp0K56SCxW"}