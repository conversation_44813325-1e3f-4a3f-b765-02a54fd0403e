{"id": "pm_1KyJGxBXhnF9ioPpvLLvrxT5", "object": "payment_method", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "created": **********, "customer": null, "livemode": false, "metadata": {}, "type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "financial_connections_account": null, "fingerprint": "hJtXanQIKIwyiRum", "last4": "6789", "routing_number": "*********"}}