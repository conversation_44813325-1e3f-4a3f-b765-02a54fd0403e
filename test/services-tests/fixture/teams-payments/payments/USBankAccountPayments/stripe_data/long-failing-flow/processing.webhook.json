{"id": "evt_3KyJDgBXhnF9ioPp0YRRrgd7", "object": "event", "api_version": "2019-09-09", "created": 1652289979, "data": {"object": {"id": "pi_3KyJDgBXhnF9ioPp0Wgw4SyD", "object": "payment_intent", "amount": 1077700, "amount_capturable": 0, "amount_details": {"tip": {}}, "amount_received": 0, "application": null, "application_fee_amount": 10849, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"id": "py_3KyJDgBXhnF9ioPp0HLYBcgc", "object": "charge", "amount": 1077700, "amount_captured": 1077700, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": 10849, "balance_transaction": null, "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "<PERSON>", "phone": null}, "calculated_statement_descriptor": null, "captured": true, "created": 1652289979, "currency": "usd", "customer": null, "description": null, "destination": "acct_1KgsHkLDYnTA1fGW", "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"total": "$10668.51", "stripe_fee": "$103.49", "phone": "**********", "sw_fee": "$5.00", "event_name": "ACH testing", "event_id": "22012", "email": "<EMAIL>", "additional_fee": "$0.00", "project": "sw"}, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "order": null, "outcome": null, "paid": false, "payment_intent": "pi_3KyJDgBXhnF9ioPp0Wgw4SyD", "payment_method": "pm_1KyJGxBXhnF9ioPpvLLvrxT5", "payment_method_details": {"type": "us_bank_account", "us_bank_account": {"account_holder_type": "individual", "account_type": "checking", "bank_name": "STRIPE TEST BANK", "fingerprint": "hJtXanQIKIwyiRum", "last4": "6789", "routing_number": "*********"}}, "receipt_email": null, "receipt_number": null, "receipt_url": null, "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/py_3KyJDgBXhnF9ioPp0HLYBcgc/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "pending", "transfer_data": {"amount": null, "destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": "group_py_3KyJDgBXhnF9ioPp0HLYBcgc"}], "has_more": false, "total_count": 1, "url": "/v1/charges?payment_intent=pi_3KyJDgBXhnF9ioPp0Wgw4SyD"}, "client_secret": "pi_3KyJDgBXhnF9ioPp0Wgw4SyD_secret_nkmuHZlAUgDkKdqCHW0sUsVu5", "confirmation_method": "automatic", "created": **********, "currency": "usd", "customer": null, "description": null, "invoice": null, "last_payment_error": null, "livemode": false, "metadata": {"total": "$10668.51", "stripe_fee": "$103.49", "phone": "**********", "sw_fee": "$5.00", "event_name": "ACH testing", "event_id": "22012", "email": "<EMAIL>", "additional_fee": "$0.00", "project": "sw", "purchase_id": "123091"}, "next_action": null, "on_behalf_of": "acct_1KgsHkLDYnTA1fGW", "payment_method": "pm_1KyJGxBXhnF9ioPpvLLvrxT5", "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}, "us_bank_account": {"verification_method": "automatic"}}, "payment_method_types": ["card", "us_bank_account"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": "off_session", "shipping": null, "source": null, "statement_descriptor": "lol", "statement_descriptor_suffix": null, "status": "processing", "transfer_data": {"destination": "acct_1KgsHkLDYnTA1fGW"}, "transfer_group": null}}, "livemode": false, "pending_webhooks": 2, "request": {"id": "req_kw8BaeIedf0AY6", "idempotency_key": "e0a0ef60-1e1c-4c7e-b888-174ebe80e008"}, "type": "payment_intent.processing"}