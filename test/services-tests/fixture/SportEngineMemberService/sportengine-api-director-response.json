{"metadata": {"trace_id": "4e6d2fbf-d9b8-489d-953e-2ae531d7ccff", "current_user": {"id": -1, "uuid": "00000000-0000-0000-000000000000", "client_name": "Sport Wrench Prod", "client_identifier": "cd04e8893a0b03decabd87c7d22e24ac"}, "pagination": {"total": 1, "total_pages": 1, "first_page": true, "first_page_href": "https://se-api.sportsengine.com/v3/eligibility/50/22518?date_of_birth=1982-06-11&membership_number=1095759&page=1", "next_page_href": null, "previous_page_href": null, "last_page": true, "last_page_href": "https://se-api.sportsengine.com/v3/eligibility/50/22518?date_of_birth=1982-06-11&membership_number=1095759&page=1", "current_page": 1, "limit": 25, "offset": 0, "current_page_total_count": 1}}, "result": [{"persona_id": 25571174, "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "middle_name": "", "membership_number": "1095759", "suffix": "", "gender": "female", "date_of_birth": "1982-06-11", "graduation_year": null, "owning_org_id": 22518, "preferred_name": "", "eligibility_status": "eligible", "affiliation_org_id": 13303, "start_date": null, "end_date": "2021-09-01T05:59:59.000Z", "membership_id": "11eb02ad-c6c4-0aa0-bc60-0a0544c66ffb", "membership_name": "20-21 USAV Adult - Coach+", "membership_definition_id": "11eab63f-0fdf-9706-a87d-12a71b65f4d3", "tags": [{"boss_organization_id": 22518, "name": "Season", "tag_option_type": "Seasonality", "tag_value_type": "Seasonality", "tag_value": {"value": "Season"}, "description": ""}, {"boss_organization_id": 22518, "name": "Coach", "tag_option_type": "Role", "tag_value_type": "Role", "tag_value": {"value": "Coach"}, "description": "Coach affiliated with junior club"}, {"boss_organization_id": 22518, "name": "Adult", "tag_option_type": "Age", "tag_value_type": "Age", "tag_value": {"start_date": "1965-07-01T00:00:00.000Z", "end_date": "2001-06-30T00:00:00.000Z"}, "description": ""}, {"boss_organization_id": 22518, "name": "Adult Senior", "tag_option_type": "Age", "tag_value_type": "Age", "tag_value": {"start_date": null, "end_date": "1965-06-30T00:00:00.000Z"}, "description": ""}, {"boss_organization_id": 22518, "name": "Adult", "tag_option_type": "Age Group", "tag_value_type": "Age Group", "tag_value": {"value": "Adult"}, "description": ""}], "credential_definition_id": 50, "alternate_code": "WREVO", "affiliation_code": "WREVO", "parent_affiliation_code": "LS"}]}