{"id": "pi_3KqZ0d2Yt0RbUG0q0IdSGWJY", "object": "payment_intent", "amount": 134, "amount_capturable": 0, "amount_details": {"tip": {"amount": null}}, "amount_received": 134, "application": null, "application_fee_amount": null, "automatic_payment_methods": null, "canceled_at": null, "cancellation_reason": null, "capture_method": "automatic", "charges": {"object": "list", "data": [{"id": "ch_3KqZ702Yt0RbUG0q1VWRWqP8", "object": "charge", "amount": 134, "amount_captured": 134, "amount_refunded": 0, "application": null, "application_fee": null, "application_fee_amount": null, "balance_transaction": "txn_3KqZ702Yt0RbUG0q1ZeIrB4c", "billing_details": {"address": {"city": null, "country": null, "line1": null, "line2": null, "postal_code": "67676", "state": null}, "email": null, "name": null, "phone": null}, "calculated_statement_descriptor": "EVENTVOLLE* TEAMS SW F", "captured": true, "created": 1650444131, "currency": "usd", "customer": "cus_IN7GPpqdDinsVj", "description": null, "destination": null, "dispute": null, "disputed": false, "failure_balance_transaction": null, "failure_code": null, "failure_message": null, "fraud_details": {}, "invoice": null, "livemode": false, "metadata": {"event_id": "21122", "purchase_type": "custom", "payment_for": "uncollected_fee", "payment_for_type": "teams", "user_id": "3564", "project": "sw"}, "on_behalf_of": null, "order": null, "outcome": {"network_status": "approved_by_network", "reason": null, "risk_level": "normal", "risk_score": 17, "seller_message": "Payment complete.", "type": "authorized"}, "paid": true, "payment_intent": "pi_3KqZ702Yt0RbUG0q19X3IRFw", "payment_method": "pm_1QRmXn2Yt0RbUG0qh36eSkdZ", "payment_method_details": {"card": {"brand": "visa", "checks": [{"address_line1_check": null, "address_postal_code_check": "pass", "cvc_check": null}], "country": "US", "exp_month": 11, "exp_year": 2040, "fingerprint": "rqNYNMU8YNQvj9iA", "funding": "credit", "installments": null, "last4": "4242", "mandate": null, "network": "visa", "three_d_secure": null, "wallet": null}, "type": "card"}, "receipt_email": null, "receipt_number": null, "receipt_url": "https://pay.stripe.com/receipts/acct_102orH2Yt0RbUG0q/ch_3KqZ702Yt0RbUG0q1VWRWqP8/rcpt_LXePiDlMmi3fZ42iXD6yCCWZRQj5zBH", "refunded": false, "refunds": {"object": "list", "data": [], "has_more": false, "total_count": 0, "url": "/v1/charges/ch_3KqZ702Yt0RbUG0q1VWRWqP8/refunds"}, "review": null, "shipping": null, "source": null, "source_transfer": null, "statement_descriptor": null, "statement_descriptor_suffix": "Teams SW Fee", "status": "succeeded", "transfer_data": null, "transfer_group": null}], "has_more": false, "total_count": 1, "url": "/v1/charges?payment_intent=pi_3KqZ0d2Yt0RbUG0q0IdSGWJY"}, "client_secret": "pi_3KqZ0d2Yt0RbUG0q0IdSGWJY_secret_t9Q9KvPTZbNMbMAbWuDwG6sX4", "confirmation_method": "automatic", "created": 1650443735, "currency": "usd", "customer": "cus_IN7GPpqdDinsVj", "description": null, "invoice": null, "last_payment_error": null, "livemode": false, "metadata": {"event_id": "21122", "purchase_type": "custom", "payment_for": "uncollected_fee", "payment_for_type": "teams", "user_id": "3564", "project": "sw"}, "next_action": null, "on_behalf_of": null, "payment_method": "pm_1QRmXn2Yt0RbUG0qh36eSkdZ", "payment_method_options": {"card": {"installments": null, "mandate_options": null, "network": null, "request_three_d_secure": "automatic"}}, "payment_method_types": ["card"], "processing": null, "receipt_email": null, "review": null, "setup_future_usage": null, "shipping": null, "source": null, "statement_descriptor": null, "statement_descriptor_suffix": "Teams SW Fee", "status": "succeeded", "transfer_data": null, "transfer_group": null}