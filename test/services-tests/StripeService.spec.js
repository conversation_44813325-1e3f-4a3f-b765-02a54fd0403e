'use strict';

const nock 				= require('nock');

const settingsRow 			= require('../fixture/settings.row');
const stripeAccountRow 		= require('../fixture/stripe_account.row');
const stripeChargeRow 		= require('./fixture/StripeService/stripe_charge.row'); 
const modifiedStripeCharge 	= require('./fixture/StripeService/modified_stripe_charge'); 
const stripeChargeToInsert  = require('./fixture/StripeService/stripe_charge_to_insert'); 

function dropStripeCharge () {
	return Db.query('DELETE FROM "stripe_charge"');
}

function fillStripeCharge () {
	return Db.query(
		squel.insert().into('stripe_charge').setFields(stripeChargeRow).toString()
	)
}

function findCharge (stripeChargeID) {
	return Db.query('SELECT * FROM "stripe_charge" WHERE "stripe_charge_id" = $1', [stripeChargeID])
	.then(res => res.rows[0])
}


describe('StripeService', function () {
	let stripeService;

	before(() => {
		stripeService = global.sails.services.stripeservice;
	});

	context('Fee Counters', () => {
		context('customerStripeFee', () => {
			it('should count fee for amount', () => {
				let result = stripeService.customerStripeFee(10);

				result.should.be.equal(0.61);
			});

			it('should count fee for amount and custom percent', () => {
				let result = stripeService.customerStripeFee(10, 0.05);

				result.should.be.equal(0.84);
			});

			it('should count fee for amount and custom fixed value', () => {
				let result = stripeService.customerStripeFee(10, null, 0.7);

				result.should.be.equal(1.02);
			});

			it('should count fee for amount and custom percent and fixed value', () => {
				let result = stripeService.customerStripeFee(10, 0.05, 0.7);

				result.should.be.equal(1.26);
			});

			it('should return 0 when amount is 0', () => {
				let result = stripeService.customerStripeFee(0);

				result.should.be.equal(0);
			})

			it('should parse strings for 2nd and 3rd parameters', () => {
				let result = stripeService.customerStripeFee(10, '0.05', '0.7');

				result.should.be.equal(1.26);
			})

			it('should throw exception when amount < 0', () => {
				try {
					stripeService.customerStripeFee(-1);
					throw new Error('Negative amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be greater than 0')
				}
			});

			it('should throw exception when amount is not number', () => {
				try {
					stripeService.customerStripeFee('ooops');
					throw new Error('Not numeric amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be a number');
				}
			});
		});

		context('customerACHStripeFee', () => {
			it('should count fee for amount not using max. fee value', () => {
				let result = stripeService.customerACHStripeFee(500);

				result.should.be.equal(4.03);
			});

			it('should count fee for huge amount', () => {
				let result = stripeService.customerACHStripeFee(1000);

				result.should.be.equal(8.06);
			});

			it('should count fee with custom percent', () => {
				let result = stripeService.customerACHStripeFee(200, 0.01);

				result.should.be.equal(2.02);
			});

			it('should parse strings for 2nd parameter', () => {
				let result = stripeService.customerACHStripeFee(700, '0.01');

				result.should.be.equal(7.07);
			})

			it('should return 0 when amount is 0', () => {
				let result = stripeService.customerACHStripeFee(0);

				result.should.be.equal(0);
			});

			it('should throw exception when amount < 0', () => {
				try {
					stripeService.customerACHStripeFee(-1);
					throw new Error('Negative amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be greater than 0')
				}
			});

			it('should throw exception when amount is not number', () => {
				try {
					stripeService.customerACHStripeFee('ooops');
					throw new Error('Not numeric amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be a number');
				}
			});
		});

		context('defaultStripeFee', () => {
			it('should count fee for amount', () => {
				let result = stripeService.defaultStripeFee(10);

				result.should.be.equal(0.59);
			});

			it('should count fee for amount and custom percent', () => {
				let result = stripeService.defaultStripeFee(10, 0.4);

				result.should.be.equal(4.3);
			});

			it('should count fee for amount and custom fixed value', () => {
				let result = stripeService.defaultStripeFee(10, null, 0.5);

				result.should.be.equal(0.79);
			});

			it('should count fee for amount and custom percent and fixed value', () => {
				let result = stripeService.defaultStripeFee(10, 0.4, 0.5);

				result.should.be.equal(4.5);
			});

			it('should return 0 when amount is 0', () => {
				let result = stripeService.defaultStripeFee(0);

				result.should.be.equal(0);
			});

			it('should parse strings for 2nd and 3rd parameters', () => {
				let result = stripeService.defaultStripeFee(10, '0.4', '0.5');

				result.should.be.equal(4.5);
			})

			it('should throw exception when amount < 0', () => {
				try {
					stripeService.defaultStripeFee(-1);
					throw new Error('Negative amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be greater than 0')
				}
			});

			it('should throw exception when amount is not number', () => {
				try {
					stripeService.defaultStripeFee('ooops');
					throw new Error('Not numeric amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be a number');
				}
			});
		})

		context('defaultACHStripeFee', () => {
			it('should count fee for amount not using max. fee value', () => {
				let result = stripeService.defaultACHStripeFee(500);

				result.should.be.equal(4);
			});

			it('should count fee for huge amount', () => {
				let result = stripeService.defaultACHStripeFee(700);

				result.should.be.equal(5.6);
			});

			it('should count fee with custom percent', () => {
				let result = stripeService.defaultACHStripeFee(200, 0.01);

				result.should.be.equal(2);
			});

			it('should parse strings for 2nd parameters', () => {
				let result = stripeService.defaultACHStripeFee(800, '0.01');

				result.should.be.equal(8);
			})

			it('should return 0 when amount is 0', () => {
				let result = stripeService.defaultACHStripeFee(0);

				result.should.be.equal(0);
			});

			it('should throw exception when amount < 0', () => {
				try {
					stripeService.defaultACHStripeFee(-1);
					throw new Error('Negative amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be greater than 0')
				}
			});

			it('should throw exception when amount is not number', () => {
				try {
					stripeService.defaultACHStripeFee('ooops');
					throw new Error('Not numeric amount test passed!');
				} catch (e) {
					e.should.be.instanceof(Object);
					e.message.should.be.equal('Amount should be a number');
				}
			});
		});
	});

	context('Account', () => {

		let accountService, testStripeAccount

		before(function () {
			accountService = stripeService.account;

			let _testAccountRow = _.find(settingsRow, { 'key': 'stripe_connect_dev' })

			testStripeAccount = JSON.parse(_testAccountRow.value);
		})

		context('_isKeyTest()', () => {

			it('should return TRUE for a test key', () => {
				let result = accountService._isKeyTest('pk_test_2QPxTdeaJxTwzlDnmVhd2Z3i');

				expect(result).to.be.true;
			})

			it('should return FALSE for a non-test key', () => {
				let result = accountService._isKeyTest('********************************');

				expect(result).to.be.false;
			})

			it('should return FALSE for an empty argument', () => {
				let result = accountService._isKeyTest();

				expect(result).to.be.false;
			})

		})

		context('getAccount()', () => {

			it('should throw exception if secret key missing', () => {
				return accountService.getAccount().should.be.rejectedWith({ validation: 'Secret Key required' })
			})

			it('should return stripe account', () => {
				return accountService.getAccount(testStripeAccount.secret_key, sails.config.stripe_api.version)
				.then(account => {
					expect(account).to.be.a('object');
					expect(account).to.contain.all.keys([
					    'business_logo',
                        'business_logo_large',
                        'business_primary_color',
                        'capabilities',
                        'charges_enabled',
                        'country',
                        'default_currency',
                        'details_submitted',
                        'display_name',
                        'id',
                        'object',
                        'payouts_enabled',
                        'statement_descriptor',
                        'statement_descriptor_kana',
                        'statement_descriptor_kanji',
                        'timezone',
                        'type'
                    ]);
				})
			})

		})

		context('upsertAccount()', () => {

			let _stripeAccountRow;

			after(function () {
				if (_stripeAccountRow) {
					return Db.query(
						`DELETE FROM "stripe_account" 
						 WHERE "account_name" = $1 
						 	AND "account_statement" = $2 
						 	AND "public_key" = $3`,
						[
							_stripeAccountRow.account_name, 
							_stripeAccountRow.account_statement, 
							_stripeAccountRow.public_key
						]
					)
				}
			})

			it('should throw exception if secret key missing', () => {
				return accountService.upsertAccount().should.be.rejectedWith({ validation: 'Secret Key required' })
			})

			it('should throw exception if public key missing', () => {
				return accountService.upsertAccount(testStripeAccount.secret_key)
				.should.be.rejectedWith({ validation: 'Publishable Key required' })
			})

			it('should reject if live & test keys passed', () => {
				return accountService.upsertAccount(testStripeAccount.secret_key, 'pk_live_JHZca69ZVGlalmBMX7OQoI4T')
				.should.be.rejectedWith({ validation: 'Live & Test keys passed' })
			})

			it('should upsert "stripe_account" row', () => {
				return accountService.upsertAccount(testStripeAccount.secret_key, testStripeAccount.public_key)
				.then(account => {
					expect(account).to.be.a('object');
					expect(account).to.contain.all.keys([
						'connected', 'account_name', 'account_statement', 
						'account_email', 'private_encoded_key', 'public_key'
					]);

					_stripeAccountRow = account;
				})
			})

			it('should upsert "stripe_account" row with "event_owner_id', () => {
				return accountService.upsertAccount(
					stripeAccountRow.secret_key, 
					stripeAccountRow.public_key,
					sails.config.stripe_api.version,
					stripeAccountRow.event_owner_id
				).then(account => {
					expect(account).to.be.a('object');

					expect(account).to.contain.all.keys([
						'connected', 'account_name', 'account_statement', 
						'account_email', 'private_encoded_key', 'public_key'
					]);
				})
			})

		})

		context('getStripeAccountRow()', () => {

			it('should return "stripe_account" row', () => {
				return accountService.getStripeAccountRow(testStripeAccount.secret_key)
				.then(account => {
					expect(account).to.be.a('object');

					expect(account).not.to.have.keys('secret_key');
					expect(account).to.include.keys('private_encoded_key');
				});
			})

			it('should throw exception if no secret key passed', () => {
				return accountService.getStripeAccountRow()
				.should.be.rejectedWith({ validation: 'Secret Key required' })
			})

		})

		context('getStripeKeys()', () => {

			it('should find keys for all accounts', () => {
				return accountService.getStripeKeys()
				.then(accounts => {
					expect(accounts).to.be.a('array');
					expect(accounts.length).not.to.be.equal(0);
				})
			})

			it('should find keys for specified arguments', () => {
				return accountService.getStripeKeys({
					secret_key 	: stripeAccountRow.secret_key,
					is_test 	: true
				}).then(accounts => {
					expect(accounts).to.be.a('array');
					expect(accounts.length).to.be.equal(1);

					expect(accounts[0]).to.contain.all.keys(['secret_key', 'public_key']);
					expect(accounts[0].secret_key).to.be.equal(stripeAccountRow.secret_key);
				});
			})

			it('should ignore invalid-type argument', () => {
				return accountService.getStripeKeys([])
				.then(accounts => {
					expect(accounts).to.be.a('array');
					expect(accounts.length).not.to.be.equal(0);
				})
			})

			it('should reject if not-existing table field passed', () => {
				return accountService.getStripeKeys({ not_existing_field: 'Test' })
				.should.be.rejectedWith(Object)
				.then(err => {
					expect(err.code).to.equal('42703');
					expect(err.severity).to.equal('ERROR');
					expect(err.routine).to.equal('errorMissingColumn');
				})
			})

		})

		context('__getEOStripeAccountRows()', () => {

			it('should reject if no "event_owner_id" passed', () => {
				return accountService.__getEOStripeAccountRows()
				.should.be.rejectedWith({ validation: 'Event Owner ID required' })
			})

			it('should find accounts for specified "event_owner_id"', () => {
				return accountService.__getEOStripeAccountRows(stripeAccountRow.event_owner_id)
				.then(accounts => {
					expect(accounts).to.be.a('array');
					expect(accounts.length).to.be.equal(2);
				})
			})

		})

		context('getPlatformKeys()', () => {
			let testData, liveData;

			before(function () {
				testData = JSON.parse(_.find(settingsRow, { 'key': 'stripe_connect_dev' }).value);
				liveData = JSON.parse(_.find(settingsRow, { 'key': 'stripe_connect' }).value);
			})

			it('should find "test" keys', () => {
				return accountService.getPlatformKeys()
				.then(account => {
					expect(account).to.be.a('object');

					expect(account.secret_key).to.be.equal(testData.secret_key);
					expect(account.public_key).to.be.equal(testData.public_key);
					expect(account.client_id).to.be.equal(testData.client_id);
				});
			})

			it('should find "live" keys', () => {
				let retrieveLiveKeys = true;

				return accountService.getPlatformKeys(retrieveLiveKeys)
				.then(account => {
					expect(account).to.be.a('object');

					expect(account.secret_key).to.be.equal(liveData.secret_key);
					expect(account.public_key).to.be.equal(liveData.public_key);
					expect(account.client_id).to.be.equal(liveData.client_id);
				});
			})

		})

		context('setConnectData()', () => {
			let connectData;

			before(function () {
				connectData = JSON.parse(stripeAccountRow.stripe_connect);
			})

			it('should reject if empty connect data passed', () => {
				return accountService.setConnectData()
				.should.be.rejectedWith({ validation: 'Stripe Connect data required.' })
			})

			it('should reject if account row not found', () => {
                expect(accountService.setConnectData(
					_.defaults({ stripe_user_id: 'non_existing' }, connectData)
				)).to.be.rejected.and.eventually.deep.equal({ validation: 'Stripe Account not found in system.' })
			})

			it('should set connect data', () => {
				return accountService.setConnectData(connectData)
				.then(() => {
					return Db.query(
						`SELECT "stripe_connect" FROM "stripe_account"
						 WHERE "stripe_account_id" = $1 
						 	AND "is_test" = $2`,
						[stripeAccountRow.stripe_account_id, !!stripeAccountRow.is_test]
					).then(result => result.rows[0])
				})
				.then(account => {
					expect(account.stripe_connect).to.be.eql(connectData);
				})
			})

		})

		context('connectAccount()', () => {

			const AUTH_CODE_MOCK = '123';

			before(function () {
				let defaultConnect = JSON.parse(stripeAccountRow.stripe_connect);

				nock(accountService._STRIPE_TOKEN_URL)
				.post('', () => true) /* Any request body */
				.delay(300)
				.times(2)
				.reply(200, defaultConnect);
			})

			let getPlatformKeysSpy, getStripeConnectAccessTokenSpy, setConnectDataSpy

			beforeEach(function () {
				getPlatformKeysSpy 				= sinon.spy(accountService, 'getPlatformKeys');
				getStripeConnectAccessTokenSpy 	= sinon.spy(accountService, 'getStripeConnectAccessToken');
				setConnectDataSpy 				= sinon.spy(accountService, 'setConnectData');
			})

			afterEach(function () {
				getPlatformKeysSpy.restore();
				getStripeConnectAccessTokenSpy.restore();
				setConnectDataSpy.restore();
			})

			it('should set connect credentials for account', () => {
				return accountService.connectAccount(AUTH_CODE_MOCK, true)
				.then(() => {
					expect(getPlatformKeysSpy.calledOnce).to.be.true;
					expect(getStripeConnectAccessTokenSpy.calledOnce).to.be.true;
					expect(setConnectDataSpy.calledOnce).to.be.true;
				})
			})

			it('should reject if passed mode does not match Stripe mode', () => {
				return accountService.connectAccount(AUTH_CODE_MOCK, false)
				.should.be.rejectedWith({ validation: `Account's mode does not match Stripe Connect mode` })
				.then(() => {
					expect(getPlatformKeysSpy.calledOnce).to.be.true;
					expect(getStripeConnectAccessTokenSpy.calledOnce).to.be.true;
					expect(setConnectDataSpy.callCount).to.be.equal(0);
				})
			})

		})

	})

	context('getSWClientID()', () => {

		let liveClientID, testClientID

		before(function () {
			liveClientID = JSON.parse(_.find(settingsRow, { 'key': 'stripe_connect' }).value).client_id;
			testClientID = JSON.parse(_.find(settingsRow, { 'key': 'stripe_connect_dev' }).value).client_id;
		})

		it(`should return live and test accounts' client id`, () => {
			return stripeService.getSWClientID()
			.then(data => {
				expect(data).to.be.a('object');

				expect(data.live).to.be.equal(liveClientID);
				expect(data.test).to.be.equal(testClientID);
			})
		})

	})

	context('charge', () => {

        async function checkStripeChargeRow (initCharge) {
            const charge = await findCharge(initCharge.stripe_charge_id);
            expect(charge).to.be.a('object');

            expect(parseFloat(charge.amount)).to.be.equal(initCharge.amount);
            expect(parseFloat(charge.fee)).to.be.equal(initCharge.fee);
            expect(parseFloat(charge.collected_fee)).to.be.equal(initCharge.collected_fee);
            expect(charge.stripe_payment_id).to.be.equal(initCharge.stripe_payment_id);
            expect(charge.stripe_account_id).to.be.equal(initCharge.stripe_account_id);
            expect(charge.type).to.be.equal('connect');
        }

		let chargeService;

		before(() => {
			chargeService = stripeService.webhook.charge;

			return fillStripeCharge();
		})

		after(() => dropStripeCharge())

		context('__prepareObject__()', () => {

			it('should prepare object for sql query', () => {
				let res = chargeService.__prepareObject__({
					obj 	: { test: 'test', test1: `test'test'test` },
					str 	: `test'test`,
					str2 	: 'test',
					num 	: 1
				});

				expect(res).to.eql({
					obj 	: { test: 'test', test1: `test''test''test` },
					str 	: `test''test`,
					str2 	: 'test',
					num 	: 1
				})
			})

		})

        context('__saveStripeChargeDBRow__()', () => {

            it('should update existing "stripe_charge" row', async () => {
                const res = await chargeService.chargeRow.__saveStripeChargeDBRow__(modifiedStripeCharge, 'update');
                expect(res).to.be.true;

                return checkStripeChargeRow(modifiedStripeCharge);
            })

            it('should insert "stripe_charge" row', async () => {
                const res = await chargeService.chargeRow.__saveStripeChargeDBRow__(stripeChargeToInsert, 'insert');
                expect(res).to.be.true;

                return checkStripeChargeRow(stripeChargeToInsert)
            })
        })
    })


    context('processStripePayout() manual', () => {
        const paidPayoutManual = require('../stripe/fixture/payout.manual.paid.json');

        function getStripeTransferFromDb (stripeTransferID) {
            return Db.query(
                squel.select().from('stripe_transfer').where('stripe_transfer_id = ?', stripeTransferID)
            ).then(res => res.rows[0] || null)
        }

        after(() => {
            return Db.query('DELETE FROM "stripe_transfer"');
        });

        it('should insert "stripe_transfer" row', async () => {
            let accID           = paidPayoutManual.account;
            let transferData    = paidPayoutManual.data.object;
            let apiVersion      = paidPayoutManual.api_version;

            await StripeService.payouts.processStripePayout(accID, transferData, apiVersion);

            let transferRow = await getStripeTransferFromDb(transferData.id);

            expect(transferRow).to.be.an('object');
            expect(transferRow.event_id).to.be.null;
            expect(Number(transferRow.amount)).to.equal(transferData.amount / 100);
            expect(transferRow.stripe_account_id).to.equal(accID);
            expect(transferRow.stripe_transfer_id).to.equal(transferData.id);
            expect(transferRow.description).to.equal(transferData.description);
            expect(transferRow.payment_for).to.be.null;
            expect(transferRow.transfer_type).to.equal('pay_out');
            expect(transferRow.date_sent).to.be.instanceof(Date);
        })
    });
});
