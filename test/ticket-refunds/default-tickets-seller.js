'use strict';

const request       = require('request-promise');

const UserSignin = require('./user-signin');
const TicketRefundsService = require('./ticket-refunds.service');

let eventTicketRow        = require('./fixture/default-tickets/event_ticket');
let purchaseRow           = require('./fixture/default-tickets/purchase');
let purchaseTicketRow     = require('./fixture/default-tickets/purchase_ticket');
let eventRow              = require('./fixture/event');
let userRows              = require('./fixture/user');
let eventOwnerRows        = require('./fixture/event_owner');
let purchaseHistoryRow    = require('./fixture/purchase_history');

describe('Default Tickets refunds seller/seller', () => {
    let eventID;
    /**
     * @type {UserSignin}
     */
    let user;
    let cookies;
    let ticket;
    let purchaseID;
    let partialRefundDiscount = 0;
    let ticketsQuantity = purchaseTicketRow.quantity;
    let stripeConnectStub;
    let stripeConnectMetadataUpdateStub;
    let applePassServiceStub;
    let eventTicket;
    const tickets_sw_fee = 1;

    before(async () => {
        stripeConnectStub = sinon
            .stub(sails.services.paymentservice.tickets.refunds.StripeConnect, 'refundClientPayment')
            .resolves();

        stripeConnectMetadataUpdateStub = sinon
            .stub(sails.services.stripeservice, 'updateChargeMetadataAfterRefund')
            .resolves();

        applePassServiceStub = sinon.stub(sails.services.applepassservice, 'updateApplePassForTicket').resolves();

        eventID = await TicketRefundsService.addEvent(eventRow, {clearSettings: true});
        user = await UserSignin.create(userRows[0]);
        await user.assignToEvent(eventID);
        cookies = await user.signIn();

        eventTicket = await TicketRefundsService.addEventTicket(eventTicketRow, eventID);
        purchaseID = await TicketRefundsService.addPayment(purchaseRow, eventID);
        ticket = await addPurchaseTickets(purchaseID, eventTicket);

        await TicketRefundsService.addEventOwner(eventOwnerRows[0]);
        await TicketRefundsService.addPurchaseHistory(purchaseHistoryRow, purchaseID);
    });

    after(async () => {
        await user.del();
        await TicketRefundsService.clearData(eventID);
        stripeConnectStub.restore();
        stripeConnectMetadataUpdateStub.restore();
        applePassServiceStub.restore();
    });

    context('[Partial Refund (1 ticket)] post /api/event/:event/ticket/:ticket/refund/partial', () => {
        it('should make successful partial refund', () => {

            let { barcode, event_ticket_id, purchase_ticket_id, price } = ticket;

            // discount here is ticket price
            let discount = Number(price);

            // reduce tickets quantity
            ticketsQuantity -= 1;

            // amount before refund - all not canceled tickets sum
            let total = utils.normalizeNumber(price * ticketsQuantity);

            return request({
                method 	: 'POST',
                uri 	: `http://${HOST}/api/event/${eventID}/ticket/${barcode}/refund/partial`,
                body 	: {
                    tickets: [
                        {
                            id                : event_ticket_id,
                            camp_id           : 0,
                            discount          : 0,
                            cancellation      : 0,
                            quantity          : ticketsQuantity,
                            purchase_ticket_id
                        }
                    ],
                    total: total
                },
                headers: { 'Cookie': cookies },
                json: true
            }).then(() => {
                return Promise.all([
                    TicketRefundsService.getPaymentData({purchase_id: purchaseID}),
                    TicketRefundsService.getPurchaseTicketsValues(purchaseID)
                ]).then(data => {
                    let purchase        = data[0];
                    let purchaseTickets = data[1];

                    let {
                        amount, amount_refunded, stripe_fee, collected_sw_fee, net_profit
                    } = TicketRefundsService.calculateResults(
                        {
                            products: [
                                {
                                    product_price: purchaseRow.amount,
                                    discount,
                                    quantity: ticketsQuantity,
                                    ticket_sw_fee: tickets_sw_fee,
                                }
                            ],
                            stripe_tickets_percent: purchase.stripe_percent,
                        }
                    );

                    // check purchase values
                    purchase.amount.should.be.equal(amount);
                    purchase.amount_refunded.should.be.equal(amount_refunded);
                    purchase.stripe_fee.should.be.equal(stripe_fee);
                    purchase.collected_sw_fee.should.be.equal(collected_sw_fee);
                    purchase.net_profit.should.be.equal(net_profit);

                    // check purchase tickets values
                    purchaseTickets.quantity.should.be.equal(2);
                    purchaseTickets.available.should.be.equal(2);
                    purchaseTickets.discount.should.be.equal(0);

                    // check if #refundClientPayment() data.amount is equal discount
                    // if data.amount is defined - it is partial refund without all fees refund
                    stripeConnectStub.lastCall.args[0].amount.should.be.equal(discount);
                });
            })
        });
    });

    context('[Partial Refund (discount)] post /api/event/:event/ticket/:ticket/refund/partial', () => {
        it('should make successful discount', () => {

            let { barcode, event_ticket_id, purchase_ticket_id, price, quantity } = ticket;

            // set discount number
            let discount        = partialRefundDiscount = 11;

            // amount before refund - all not canceled tickets sum
            let currentAmount   = utils.normalizeNumber(price * ticketsQuantity);

            // canceled tickets price
            let refundedTickets = utils.normalizeNumber((quantity - ticketsQuantity) * price);

            // amount after refund
            let total = currentAmount - discount;

            // total purchase discount
            let totalDiscount = discount + refundedTickets;

            return request({
                method 	: 'POST',
                uri 	: `http://${HOST}/api/event/${eventID}/ticket/${barcode}/refund/partial`,
                body 	: {
                    tickets: [
                        {
                            id                : event_ticket_id,
                            camp_id           : 0,
                            discount          : discount,
                            cancellation      : 0,
                            quantity          : ticketsQuantity,
                            purchase_ticket_id
                        }
                    ],
                    total: total
                },
                headers: { 'Cookie': cookies },
                json: true
            }).then(() => {
                return Promise.all([
                    TicketRefundsService.getPaymentData({purchase_id: purchaseID}),
                    TicketRefundsService.getPurchaseTicketsValues(purchaseID)
                ]).then(data => {
                    let purchase        = data[0];
                    let purchaseTickets = data[1];

                    let {
                        amount, amount_refunded, stripe_fee, collected_sw_fee, net_profit
                    } = TicketRefundsService.calculateResults(
                        {
                            products: [
                                {
                                    product_price: purchaseRow.amount,
                                    discount: totalDiscount,
                                    quantity: ticketsQuantity,
                                    ticket_sw_fee: tickets_sw_fee,
                                }
                            ],
                            stripe_tickets_percent: purchase.stripe_percent,
                        }
                    );

                    // check purchase values
                    purchase.amount.should.be.equal(amount);
                    purchase.amount_refunded.should.be.equal(amount_refunded);
                    purchase.stripe_fee.should.be.equal(stripe_fee);
                    purchase.collected_sw_fee.should.be.equal(collected_sw_fee);
                    purchase.net_profit.should.be.equal(net_profit);

                    // check purchase tickets values
                    purchaseTickets.quantity.should.be.equal(ticketsQuantity);
                    purchaseTickets.available.should.be.equal(ticketsQuantity);
                    purchaseTickets.discount.should.be.equal(partialRefundDiscount);

                    // check if #refundClientPayment() data.amount is equal discount
                    // if data.amount is defined - it is partial refund without all fees refund
                    stripeConnectStub.lastCall.args[0].amount.should.be.equal(discount);
                });
            })
        });
    });

    context('[Full Refund] post /api/event/:event/ticket/:ticket/refund', () => {
        it('should make successful full refund', () => {
            let { barcode, price } = ticket;

            return TicketRefundsService.fullRefundRequest(barcode, eventID, cookies).then(() => {
                return Promise.all([
                    TicketRefundsService.getPaymentData({purchase_id: purchaseID, canceled: true}),
                    TicketRefundsService.getPurchaseTicketsValues(purchaseID)
                ]).then(data => {
                    let purchase        = data[0];
                    let purchaseTickets = data[1];

                    // check purchase values
                    purchase.amount.should.be.equal(0);
                    purchase.amount_refunded.should.be.equal(purchaseRow.amount);
                    purchase.status.should.be.equal('canceled');
                    purchase.canceled_date.should.be.equal(true);

                    // check purchase tickets values
                    purchaseTickets.quantity.should.be.equal(ticketsQuantity);
                    purchaseTickets.available.should.be.equal(ticketsQuantity);
                    purchaseTickets.discount.should.be.equal(partialRefundDiscount);
                    purchaseTickets.canceled.should.be.equal(true);

                    // check if #refundClientPayment() data.amount is undefined
                    // if data.amount is undefined is undefined - it is full refund with all fees refund
                    expect(stripeConnectStub.lastCall.args[0].amount).to.be.undefined;
                })
            })
        });
    });

});

function addPurchaseTickets(purchaseID, eventTicket) {
    purchaseTicketRow.event_ticket_id   = eventTicket.event_ticket_id;
    purchaseTicketRow.amount            = eventTicket.current_price;
    purchaseTicketRow.ticket_price      = eventTicket.current_price;
    purchaseTicketRow.purchase_id       = purchaseID;

    let query = squel.insert().into('purchase_ticket')
        .setFields(purchaseTicketRow)
        .returning('purchase_ticket_id');

    return Db.query(query).then(result => result.rows[0] && result.rows[0].purchase_ticket_id)
        .then(purchase_ticket_id => {
            return {
                barcode             : purchaseRow.ticket_barcode,
                purchase_ticket_id  : purchase_ticket_id,
                event_ticket_id     : eventTicket.event_ticket_id,
                price               : eventTicket.current_price,
                quantity            : purchaseTicketRow.quantity
            }
        })
}
