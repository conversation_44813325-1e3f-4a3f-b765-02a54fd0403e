const assert = require('assert');
const Permissions = require('../../api/services/event/operations');

describe('Event Settings Granular Permissions Integration', function() {

    describe('Permission Constants Integration', function() {
        it('should have all required granular permissions defined', function() {
            const requiredPermissions = [
                'EDIT_EVENT_GENERAL',
                'EDIT_EVENT_LOCATION',
                'EDIT_EVENT_USERS',
                'EDIT_EVENT_TRANSACTIONAL_EMAILS'
            ];

            requiredPermissions.forEach(permission => {
                assert.ok(Permissions[permission], `Permission ${permission} should be defined`);
            });

            // Verify the actual values
            assert.equal(Permissions.EDIT_EVENT_GENERAL, 'edit_event_general');
            assert.equal(Permissions.EDIT_EVENT_LOCATION, 'edit_event_location');
            assert.equal(Permissions.EDIT_EVENT_USERS, 'edit_event_users');
            assert.equal(Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS, 'edit_event_transactional_emails');
        });
    });

    describe('Policy Configuration', function() {
        it('should have updated policies for granular permissions', function() {
            const eventAccessPolicies = require('../../config/policies/event_access');

            // Check that Event/SettingsController has granular permissions
            const settingsControllerPolicies = eventAccessPolicies.policies['Event/SettingsController'];
            assert.ok(settingsControllerPolicies, 'Event/SettingsController policies should exist');

            // Check that Event/EventUserController has the users permission
            const eventUserControllerPolicies = eventAccessPolicies.policies['Event/EventUserController'];
            assert.ok(eventUserControllerPolicies, 'Event/EventUserController policies should exist');
        });
    });

    describe('Migration Structure', function() {
        it('should have migration file for adding granular permissions', function() {
            const fs = require('fs');
            const path = require('path');

            const migrationDir = path.join(__dirname, '../../db/migrations/main');
            const files = fs.readdirSync(migrationDir);

            const granularPermissionsMigration = files.find(file =>
                file.includes('add-event-settings-sub-permissions')
            );

            assert.ok(granularPermissionsMigration, 'Migration file for granular permissions should exist');
        });
    });

    describe('ACL Refresh API', function() {
        it('should have ACL refresh endpoint configured', function() {
            const userRoutes = require('../../userconfig/routes/event/users');

            const aclRefreshRoute = userRoutes['GET /api/event/:event/acl-refresh-needed'];
            assert.ok(aclRefreshRoute, 'ACL refresh route should be configured');
            assert.equal(aclRefreshRoute, 'Event/EventUserController.checkAclRefreshNeeded');
        });
    });
});
