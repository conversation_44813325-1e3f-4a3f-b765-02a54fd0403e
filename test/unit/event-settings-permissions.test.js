const assert = require('assert');
const EventUserService = require('../../api/services/EventUserService');
const Permissions = require('../../api/services/event/operations');

describe('Event Settings Granular Permissions', function() {
    
    describe('Permission Constants', function() {
        it('should have the new event settings sub-permissions defined', function() {
            assert.ok(Permissions.EDIT_EVENT_GENERAL);
            assert.ok(Permissions.EDIT_EVENT_LOCATION);
            assert.ok(Permissions.EDIT_EVENT_USERS);
            assert.ok(Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS);
            
            assert.equal(Permissions.EDIT_EVENT_GENERAL, 'edit_event_general');
            assert.equal(Permissions.EDIT_EVENT_LOCATION, 'edit_event_location');
            assert.equal(Permissions.EDIT_EVENT_USERS, 'edit_event_users');
            assert.equal(Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS, 'edit_event_transactional_emails');
        });
    });

    describe('EventUserService', function() {
        it('should be able to handle granular permissions in user addition', function() {
            const granularPermissions = {
                [Permissions.EDIT_EVENT_GENERAL]: true,
                [Permissions.EDIT_EVENT_LOCATION]: true,
                [Permissions.EDIT_EVENT_USERS]: false,
                [Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS]: true
            };

            // Test that the service can process granular permissions
            const query = EventUserService.__getUserAdditionQuery(1, 1, 1, granularPermissions);
            assert.ok(query.query);
            assert.ok(query.values);
            
            // Should create entries for the permissions that are true
            const truePermissions = Object.keys(granularPermissions).filter(p => granularPermissions[p]);
            assert.equal(query.values.length, truePermissions.length * 4); // 4 fields per permission
        });
    });
});
