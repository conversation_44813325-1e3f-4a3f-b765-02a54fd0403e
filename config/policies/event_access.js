'use strict';

const or          = require('../../api/lib/or');
const Permissions = require('../../api/services/event/operations');
const eventAccess = require('../../api/policies/eventAccess');
const isGodMode   = require('../../api/policies/isGodMode');
const isSWOwner   = require('../../api/policies/isSWOwner');
const isEventOwner= require('../../api/policies/isEventOwner');
const isOwnEvent  = require('../../api/policies/isOwnEvent');
const getEventOwnerIDForHeadOfficial = require('../../api/policies/getEventOwnerIDForHeadOfficial');
const isHeadOfficial = require('../../api/policies/isHeadOfficial');
const isEventSalesManager = require('../../api/policies/isEventSalesManager');
const isSalesManager        = require('../../api/policies/isSalesManager');

module.exports.policies = {
    EventController: {
        '*'                     : ['isAuthenticated', or([isEventOwner, isSWOwner, isGodMode])],
        'find'                  : ['isAuthenticated', eventAccess(Object.values(Permissions))],
        'historyNew'            : ['isAuthenticated', eventAccess(Permissions.HISTORY_TAB)],
        'info'                  : [
            'isAuthenticated', eventAccess([Permissions.EVENT_INFO_TAB, Permissions.EDIT_EVENT])
        ],
        'destroy'               : ['isAuthenticated', or([isGodMode, isOwnEvent])]
    },
    'Event/ClubMembersController': {
        '*'                     : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)]
    },
    'Event/SettingsController': {
        '*'                     : ['isAuthenticated', or([isEventOwner, isGodMode])],
        'create'                : ['isAuthenticated', 'hasEventOwnerRole'],
        'info'                  : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_GENERAL])],
        'update'                : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_GENERAL])],
        'getLocationsList'      : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_LOCATION])],
        'getLocationInfo'       : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_LOCATION])],
        'updateLocation'        : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_LOCATION])],
        'createLocation'        : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_LOCATION])],
        'removeLocation'        : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_LOCATION])],
        'copyEvent'             : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_GENERAL])],
        'getTemplateTypes'      : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS])],
        // 'assignTmplToTrigger'   : ['isAuthenticated', eventAccess([Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS])],
        'getOfficialAdditionalRoles' : ['isAuthenticated']
    },
    'Event/BoothController': {
        '*'                     : [
            'isAuthenticated',
            or([
                eventAccess(Permissions.EXHIBITORS_TAB),
                isEventSalesManager,
            ]),
        ],
    },
    'Event/DivisionController': {
        '*'                     : ['isAuthenticated', eventAccess(Permissions.DIVISIONS_TAB)],
        'all'                   : [
            'isAuthenticated', eventAccess([
                Permissions.CHECKIN_TAB, Permissions.TEAMS_TAB, Permissions.DIVISIONS_TAB
            ])
        ]
    },
    'Event/TicketsController': {
        '*'                     : ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'saveStripeStatement'   : ['isAuthenticated', or([isGodMode, isOwnEvent])],
        'saveStripeTicketsKey'  : ['isAuthenticated', or([isGodMode, isOwnEvent])],
        'getPass': []
    },
    'Event/Tickets/PaymentOperationsController': {
        '*'                     : ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)]
    },
    'Event/Tickets/ParticipationController': {
        '*'                     : ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)]
    },
    'API/Tickets/ReceiptController': {
        'viewBarcodeReceipt'    : ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)]
    },
    'Roster_teamController': {
        '*'                 : ['isAuthenticated'], // 'isClubDirector', , 'isEventOwner'
        update              : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        teams_to_pay        : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        team_history        : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        booking             : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        update_housing      : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        send_email          : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB), 'isEventActive'],
        checkInAll          : ['isAuthenticated', eventAccess(Permissions.CHECKIN_TAB)],
        saveCheckInStatus   : [
            'isAuthenticated', eventAccess([Permissions.TEAMS_TAB, Permissions.CHECKIN_TAB])
        ],
        getLatestTeamsData  : ['isAuthenticated', eventAccess(Permissions.CHECKIN_TAB)],
        updateQualification: ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
    },
    'Event/PaymentController': {
        '*'                 : ['isAuthenticated', or([isEventOwner, isGodMode])],
        'cancel'            : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'index'             : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'refund'            : ['isAuthenticated', 'event/hasPaymentAccess'], //Permissions.TEAMS_TAB)
        'partial_refund'    : ['isAuthenticated', 'event/hasPaymentAccess'], //Permissions.TEAMS_TAB)
        'fingerprintCharges': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB), isGodMode],
        'save'              : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'getPaymentState'   : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)]
    },

    'Event/OfficialsController': {
        '*'                 : ['isAuthenticated', or([isEventOwner, isGodMode])],
        'index'             : ['isAuthenticated', eventAccess(Permissions.OFFICIALS_TAB)],
        'find'              : ['isAuthenticated', eventAccess(Permissions.OFFICIALS_TAB)],
        'make_head'         : [
            'isAuthenticated',
            or([
                eventAccess(Permissions.OFFICIALS_TAB),
                isHeadOfficial,
            ]),
        ],
        'remove_head'       : ['isAuthenticated', eventAccess(Permissions.OFFICIALS_TAB)],
        'updateRegInfo'     : ['isAuthenticated', eventAccess(Permissions.OFFICIALS_TAB)],
        'updateRegInfoGroup': ['isAuthenticated', eventAccess(Permissions.OFFICIALS_TAB)],
        'makePayout'        : ['isAuthenticated', eventAccess(Permissions.OFFICIALS_TAB)]
    },
    'Event/StaffController': {
        '*'                 : ['isAuthenticated', eventAccess(Permissions.STAFF_TAB)],
        'getClothingList'   : [
            'isAuthenticated',
            or([
                eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
                isHeadOfficial
            ])
        ],
        'getTravelList'     : [
            'isAuthenticated',
            or([
                eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
                isHeadOfficial
            ])
        ],
        'exportClothingList': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
                isHeadOfficial
            ])
        ],
        'exportTravelList': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
                isHeadOfficial
            ])
        ],
        'getExportedClothingList': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
                isHeadOfficial
            ])
        ],
        'getExportedTravelList': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
                isHeadOfficial
            ])
        ],
        'updateRegInfoGroup': ['isAuthenticated', eventAccess(Permissions.STAFF_TAB)],
        'exportToExcel': ['isAuthenticated', eventAccess(Permissions.STAFF_TAB)],
    },
    'Event/CampsController' : {
        '*'                 : [
            'isAuthenticated', or([isGodMode, eventAccess(Permissions.TICKETS_TAB)])
        ]
    },
    'Event/TicketsAdditionalController': {
        '*'                 : [
            'isAuthenticated', or([isGodMode, eventAccess(Permissions.TICKETS_TAB)])
        ]
    },
    'Event/EventOwnerController': {
        '*'                 : ['isAuthenticated', 'event/isEO'],
        'getStripeAccounts' : ['isAuthenticated'],
        'toggleStripeAccountVisibility': ['isAuthenticated']
    },

    'Event/TransferController': {
        '*'                         : ['isAuthenticated', or([eventAccess(Permissions.ACCOUNTING_TAB), isEventSalesManager])],
    },
    'Event/EventUserController' : {
        '*'                         : [
            'isAuthenticated',
            or([isGodMode, isOwnEvent]),
            eventAccess([Permissions.EDIT_EVENT_USERS]),
        ],
        'getUserPermissions'        : ['isAuthenticated'],
        'getUserPermissionsTree'    : ['isAuthenticated'],
        'checkAclRefreshNeeded'     : ['isAuthenticated']
    },
    'Event/ESWAdminController': {
        '*'                         : ['isAuthenticated', or([isEventOwner, isGodMode])]
    },
    'Event/RosterTeamController': {
        '*'                 : ['isAuthenticated'],
        'change_entry'      : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB), 'isEventActive'],
        'srvaReport'        : ['isAuthenticated', eventAccess(Permissions.EVENT_INFO_TAB)],
        'usavFinalReport'   : ['isAuthenticated', eventAccess(Permissions.EVENT_INFO_TAB)],
        'gevaExport'        : ['isAuthenticated', eventAccess(Permissions.EVENT_INFO_TAB)],
        'finishersReport'   : ['isAuthenticated', eventAccess(Permissions.EVENT_INFO_TAB)],
        'headToHeadReport'  : ['isAuthenticated', eventAccess(Permissions.EVENT_INFO_TAB)],
        'exportMembers'     : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'all'               : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'xlsx_export'       : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'printRosters'      : ['isAuthenticated', eventAccess([Permissions.TEAMS_TAB, Permissions.CHECKIN_TAB])],
        'exportFileDownload': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'changeDivision'    : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'find'              : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'updatePaymentStatus' : [eventAccess(Permissions.TEAMS_TAB)],
        'addTeamsManually'  : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'addRosterManually' : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)]
    },
    'Event/RosterClubController': {
        '*'                 : ['isAuthenticated'],
        'availableTeams'    : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'assignTeam'        : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
        'teams_members'     : ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)]
    },
    'Event/InvoiceController': {
        'renderInvoice'     : ['isAuthenticated', 'event/hasPaymentAccess'] // Permissions.TEAMS_TAB
    },
    'Event/AEMController': {
        '*'                 : [
            'isAuthenticated',
            or([eventAccess([Permissions.EMAIL_MODULE_TAB, Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS]), getEventOwnerIDForHeadOfficial]),
        ],
        'teamsTemplates'    : [
            'isAuthenticated',
            or([eventAccess([
                Permissions.TEAMS_TAB,
                Permissions.CHECKIN_TAB,
                Permissions.STAFF_TAB,
                Permissions.OFFICIALS_TAB,
                Permissions.TICKETS_TAB,
                Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS
            ]), isHeadOfficial])
        ],
        'generalSend'       : [
            'isAuthenticated',
            or([eventAccess([
                Permissions.TEAMS_TAB,
                Permissions.CHECKIN_TAB,
                Permissions.STAFF_TAB,
                Permissions.OFFICIALS_TAB,
                Permissions.TICKETS_TAB,
                Permissions.EDIT_EVENT_TRANSACTIONAL_EMAILS
            ]), isHeadOfficial])
        ]
    },
    /* === Accounting === */

    'Event/Accounting/StripeDetailsController' : {
        '*'                 : ['isAuthenticated']
    },

    'Event/Tickets/SettingsController': {
        '*': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
    },

    'Event/FavoriteEventController': {
        '*': ['isAuthenticated', eventAccess(Object.values(Permissions))]
    },

    'Event/AEM/CustomRecipientsController': {
        '*': ['isAuthenticated', eventAccess(Permissions.EMAIL_MODULE_TAB)]
    },

    'Event/OfficialRateController': {
        '*': ['isAuthenticated', or([isEventOwner, isHeadOfficial, eventAccess(Permissions.OFFICIALS_TAB)])],
    },
    'Event/OfficialPayoutController': {
        '*': ['isAuthenticated', or([isEventOwner, isHeadOfficial, eventAccess(Permissions.OFFICIALS_TAB)])],
    },
    'Event/EventOfficialsAdditionalPaymentsController': {
        '*': ['isAuthenticated', or([isEventOwner, isHeadOfficial, eventAccess(Permissions.OFFICIALS_TAB)])],
    },

    'Event/Tickets/FreeTicketsController': {
        '*': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
    },

    'Event/Tickets/ExhibitorTicketsController': {
        '*': ['isAuthenticated', or([isGodMode, isEventOwner, isSalesManager, eventAccess(Permissions.TICKETS_TAB)])]
    },

    'Event/CouponController': {
        '*': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB), eventAccess(Permissions.TEAMS_TAB)],
        'getCouponsList': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'updateCoupons': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'sendCoupons': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'createCoupon': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)]
    },

    'Event/ImportController': {
        '*': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
    },

    'Event/CustomPaymentController': {
        '*': ['isAuthenticated', or([isEventOwner, isGodMode])]
    },

    'Event/Tickets/TicketOperationsController': {
        '*': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
    },

    'v2/event/club-invoice/invoice/create-invoice': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],

    'v2/event/club-invoice/clubs-list': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],

    'v2/event/club-invoice/invoice/list': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],

    'v2/event/club-invoice/invoice/details': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],

    'v2/event/club-invoice/invoice/full-refund': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],

    'v2/event/club-invoice/invoice/cancel-invoice': ['isAuthenticated', eventAccess(Permissions.TEAMS_TAB)],
};
