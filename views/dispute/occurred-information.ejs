<div style="margin: 0 auto">
    <div>
        <h2 style="text-align: center">General Information</h2>
        <table style="border-collapse: collapse; border: 1px solid #333; width: 100%">
            <tr>
                <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Event ID</td>
                <td style="border: 1px solid #333; text-align: center; font-size: 14px"><%= event_id %></td>
            </tr>
            <tr>
                <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Event Name</td>
                <td style="border: 1px solid #333; text-align: center; font-size: 14px"><%= event_name %></td>
            </tr>
            <% if (!required_tickets_names) { %>
                <tr>
                    <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Ticket Code</td>
                    <td style="border: 1px solid #333; text-align: center; font-size: 14px"><%= ticket_barcode %></td>
                </tr>
            <% } %>
            <tr>
                <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Amount</td>
                <td style="border: 1px solid #333; text-align: center; font-size: 14px">$<%= amount %></td>
            </tr>
            <tr>
                <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Dispute Status</td>
                <td style="border: 1px solid #333; text-align: center; font-size: 14px"><%= dispute_status %></td>
            </tr>
            <tr>
                <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Dispute Reason</td>
                <td style="border: 1px solid #333; text-align: center; font-size: 14px"><%= dispute_reason %></td>
            </tr>
            <tr>
                <td style="font-weight: bold; border: 1px solid #333; text-align: center; padding: 10px 0">Payment Type</td>
                <td style="border: 1px solid #333; text-align: center; font-size: 14px"><%= payment_for %></td>
            </tr>
        </table>
    </div>
    <% if (teams && teams.length) { %>
        <%- include('./club-director-info.ejs') %>
    <% } else if (booths && booths.length) { %>
        <%- include('./exhibitor-info.ejs') %>
    <% } else { %>
        <%- include('./purchaser-info.ejs') %>
    <% } %>
    <% if (teams && teams.length) { %>
        <%- include('./teams-info.ejs') %>
    <% } %>
    <% if (booths && booths.length) { %>
        <%- include('./booths-info.ejs') %>
    <% } %>
    <% if (tickets && tickets.length) { %>
        <div style="margin-top: 50px">
            <% if (is_camp) { %>
                <%- include('./camps-info.ejs') %>
            <% } else if (payment_for === 'tickets') { %>
                    <% if (required_tickets_names) { %>
                        <%- include('./assigned-tickets-info.ejs') %>
                    <% } else {%>
                        <%- include('./basic-tickets-info.ejs') %>
                    <% } %>
            <% } %>
        </div>
    <% } %>
</div>
