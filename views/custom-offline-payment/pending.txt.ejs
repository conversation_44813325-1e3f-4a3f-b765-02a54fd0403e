<% const renderTicketsBalanceData = (balance_details) => { %>
  Cash Tickets Count: <%- balance_details.cash_count %>
  Check Tickets Count: <%- balance_details.check_count %>
<% } %>

<% const renderBoothsBalanceData = (balance_details) => { %>
  Approved and Paid Exhibitors: <%- balance_details.approved_and_paid %>
  Approved not Paid Exhibitors: <%- balance_details.approved_not_paid %>
  Paid not Approved Exhibitors: <%- balance_details.paid_not_approved %>
<% } %>

<% const renderTeamsBalanceData = (balance_details) => { %>
  Accepted Or Paid Teams: <%- balance_details.accepted_or_paid %>
  Paid Only Teams: <%- balance_details.paid_only %>
  Accepted Only Teams: <%- balance_details.accepted_only %>
  Accepted And Paid Teams: <%- balance_details.accepted_and_paid %>
<% } %>

<%- event_name %>

Payment pending! <%= pending_message %>

Payment details:
Amount: $<%= totals.amount %>
Merchant Fee: $<%= totals.merchant_fee %>
Total: $<%= totals.total %>

Accounting details:
SW Fee on Event: $<%- balance.sw_fee %>
Uncollected SW Fee before Payment: $<%- balance.current_balance_sum %>
Uncollected SW Fee after Payment: $<%- balance.new_balance_sum %>
<% if(payment_type_for === 'teams') { %>
<%- renderTeamsBalanceData(balance.balance_details); %>
<% } %>
<% if(payment_type_for === 'tickets') { %>
<%- renderTicketsBalanceData(balance.balance_details); %>
<% } %>
<% if(payment_type_for === 'booths') { %>
<%- renderBoothsBalanceData(balance.balance_details); %>
<% } %>
