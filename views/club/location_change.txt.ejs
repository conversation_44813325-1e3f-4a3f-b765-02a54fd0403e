Changes:
<% if (changedData['country']) { %>
    Country: <%- changedData['country'].old %> =&gt; <%- changedData['country'].new %>.
<% } %>
<% if (changedData['region']) { %>
    Region: <%- changedData['region'].old %> =&gt; <%- changedData['region'].new %>.
<% } %>
<% if (changedData['state']) { %>
    State: <%- changedData['state'].old %> =&gt; <%- changedData['state'].new %>.
<% } %>
<% if (changedData['city']) { %>
    City: <%- changedData['city'].old %> =&gt; <%- changedData['city'].new %>.
<% } %>
<% if (changedData['address']) { %>
    Address: <%- changedData['address'].old %> =&gt; <%- changedData['address'].new %>.
<% } %>
<% if (changedData['zip']) { %>
    Zip: <%= changedData['zip'].old %> =&gt <%- changedData['zip'].new %>.
<% } %>

List of upcoming events the club is registered to:
<% for (let i = 0; i < events.length; i++) { %>
    <%- events[i].name %>: <%- events[i].link_to_teams_list %>
<% } %>
