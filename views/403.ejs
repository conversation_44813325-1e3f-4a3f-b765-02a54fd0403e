<!DOCTYPE html>
<!--


       444444444       000000000      333333333333333
      4::::::::4     00:::::::::00   3:::::::::::::::33
     4:::::::::4   00:::::::::::::00 3::::::33333::::::3
    4::::44::::4  0:::::::000:::::::03333333     3:::::3
   4::::4 4::::4  0::::::0   0::::::0            3:::::3
  4::::4  4::::4  0:::::0     0:::::0            3:::::3
 4::::4   4::::4  0:::::0     0:::::0    33333333:::::3
4::::444444::::4440:::::0 000 0:::::0    3:::::::::::3
4::::::::::::::::40:::::0 000 0:::::0    33333333:::::3
4444444444:::::4440:::::0     0:::::0            3:::::3
          4::::4  0:::::0     0:::::0            3:::::3
          4::::4  0::::::0   0::::::0            3:::::3
          4::::4  0:::::::000:::::::03333333     3:::::3
        44::::::44 00:::::::::::::00 3::::::33333::::::3
        4::::::::4   00:::::::::00   3:::::::::::::::33
        4444444444     000000000      333333333333333



  This is the default "403: Forbidden" page.
  User agents that don't "Accept" HTML will see a JSON version instead.
  You can customize the control logic for your needs in `config/403.js`

  You can trigger this response from one of your controllers or policies with:
  `return res.forbidden( msg );`
  (where `msg` is an optional error message to include in the response)




-->
<html>
  <head>
    <title>Forbidden</title>
    <link href='http://sailsjs.org/styles/fonts.css' rel='stylesheet'/>
    <style>
      /* Styles included inline since you'll probably be deleting or replacing this page anyway */
      html,body{text-align:left;font-size:1em}html,body,img,form,textarea,input,fieldset,div,p,div,ul,li,ol,dl,dt,dd,h1,h2,h3,h4,h5,h6,pre,code{margin:0;padding:0}ul,li{list-style:none}img{display:block}a img{border:0}a{text-decoration:none;font-weight:normal;font-family:inherit}*:active,*:focus{outline:0;-moz-outline-style:none}h1,h2,h3,h4,h5,h6,h7{font-weight:normal;font-size:1em}.clearfix:after{clear:both;content:".";display:block;font-size:0;height:0;line-height:0;visibility:hidden}.page .ocean{background:url('http://sailsjs.com/images/waves.png') #0c8da0 no-repeat center 0;height:315px}.page .ocean img{margin-right:auto;margin-left:auto}.page .waves{display:block;padding-top:25px;margin-right:auto;margin-left:auto}.page .main{display:block;margin-top:90px}.page .logo{width:150px;margin-top:3.5em;margin-left:auto;margin-right:auto}.page .fishy{display:block;padding-top:100px}.page .help{padding-top:2em}.page h1{font-family:"Open Sans","Myriad Pro",Arial,sans-serif;font-weight:bold;font-size:1.7em;color:#001c20;text-align:center}.page h2{font-family:"Open Sans","Myriad Pro",Arial,sans-serif;font-weight:300;font-size:1.5em;color:#001c20;text-align:center}.page p{font-family:"Open Sans","Myriad Pro",Arial,sans-serif;font-size:1.25em;color:#001c20;text-align:center}.page a{color:#118798}.page a:hover{color:#b1eef7}
    </style>
  </head>
  <body>

    <div class="page">
      <div class="ocean">
        <img class="fishy" src="http://sailsjs.com/images/image_devInTub.png">
      </div>

      <div class="main">
        <h1>
          Forbidden
        </h1>
        <h2>
          <% if (typeof error !== 'undefined') { %>
          <%= error %>
          <% } else { %>
          You don't have permission to see the page you're trying to reach.
          <% } %>
        </h2>
        <p class="help">
          <a href="http://en.wikipedia.org/wiki/HTTP_403">Why</a> might this be happening?
        </p>
      </div>

      <div class="logo">
        <a href="http://sailsjs.org">
          <img src="http://sailsjs.org/images/logo.png">
        </a>
      </div>
    </div>

  </body>
</html>
