<!DOCTYPE html>
<html lang="en" style="box-sizing: border-box;font-family: sans-serif;line-height: 1.15;-webkit-text-size-adjust: 100%;-webkit-tap-highlight-color: rgba(0,0,0,0);">
<head style="box-sizing: border-box;">
    <meta charset="UTF-8" style="box-sizing: border-box;">
    <meta name="viewport" content="width=device-width, initial-scale=1" style="box-sizing: border-box;">
    <title style="box-sizing: border-box;"><%= event_name %></title>
    <style style="box-sizing: border-box;">
        @media (max-width: 450px) {
            .receipt_price {
                margin-top: 0 !important;
            }
        }

        @media (max-width: 700px) {
            .receipt_letter_wrapper {
                margin: 30px 5px 10px !important;
            }
        }

        @media (max-width: 998px) {
            .receipt_wrapper {
                margin: 30px 15px 10px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_event_info {
                margin: 15px 0 !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_event_info-text {
                text-align: center !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_event_info-text_event-name {
                font-size: 21px !important;
            }
        }

        @media (max-width: 325px) {
            .receipt_event_info-text_event-name {
                font-size: 18px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_event_info-text_event-data {
                display: none !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_event_info-logo img {
                max-width: 100px !important;
            }
        }

        @media (max-width: 325px) {
            .receipt_event_info-logo img {
                max-width: 75px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_present-qr-code {
                font-size: 13px !important;
                line-height: 1.62 !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_participant {
                margin-top: 20px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_participant_information {
                margin-top: 20px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_participant_information-body {
                margin-top: 20px !important;
            }
        }

        @media (max-width: 720px) {
            .receipt_participant_information-body_item-label_letter {
                width: 30% !important;
            }
        }

        @media (max-width: 450px) {
            .receipt_participant_information-body_item-label_letter {
                font-size: 15px !important;
            }
        }

        @media (max-width: 325px) {
            .receipt_participant_information-body_item-label_letter {
                font-size: 13px !important;
            }
        }

        @media (max-width: 720px) {

            .receipt_participant_information-body_item-value_letter {
                width: 70% !important;
            }
        }

        @media (max-width: 450px) {
            .receipt_participant_information-body_item-value_letter {
                font-size: 18px !important;
            }
        }

        @media (max-width: 325px) {
            .receipt_participant_information-body_item-value_letter {
                font-size: 15px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_ticket-holders {
                margin-top: 20px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_ticket-holders_item {
                padding-left: 0 !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_important-information {
                margin-top: 30px !important;
            }
        }

        @media (max-width: 540px) {
            .receipt_important-information_body {
                margin-top: 20px !important;
            }
        }
    </style>
</head>
<body style="box-sizing: border-box;">
<div class="receipt_letter_wrapper" style="box-sizing: border-box;max-width: 700px;font-family: 'Helvetica', 'Arial', sans-serif;margin: 50px auto;color: #474747;">
    <div class="receipt_event_info" style="box-sizing: border-box;display: table;width: 100%;margin: 35px 0 40px;">
        <div class="receipt_event_info-logo" style="box-sizing: border-box;display: table-cell;vertical-align: middle;">
            <img src="<%= event_logo %>" alt="Event Logo" style="box-sizing: border-box;vertical-align: middle;border-style: none;max-width: 300px;"/>
        </div>
        <div class="receipt_event_info-text" style="box-sizing: border-box;display: table-cell;vertical-align: middle;text-align: center;width: 300px;">
            <span class="receipt_event_info-text_event-name" style="box-sizing: border-box;font-size: 26px;font-weight: bold;"><%= event_name %></span><br style="box-sizing: border-box;"/>
            <span class="receipt_event_info-text_event-data" style="box-sizing: border-box;font-size: 15px;"><%= event_dates_info %></span>
        </div>
    </div>
    <% if (ticket_type === 'daily') { %>
        <div style="box-sizing: border-box;display: table;width: 100%;margin: 5px 0;color: #9b9b9b;font-size: 11px">
            Daily pass good for entire day on available date listed below
        </div>
    <% } %>
    <div style="box-sizing: border-box;border: solid 1px #d4d4d4;"></div>
    <div class="receipt_present-qr-code" style="box-sizing: border-box;margin: 23px 0 10px;font-size: 16px;line-height: 1.63;">
        Present this QR code on a mobile device along with a <span class="receipt_blue-bold" style="box-sizing: border-box;font-weight: bold;color: #337ab7;">Government photo ID</span> to gain entry.
        Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.
    </div>
    <div class="receipt_participant" style="box-sizing: border-box;margin-top: 40px;">
        <div style="box-sizing: border-box;width: 100%;text-align: center;">
            <div style="box-sizing: border-box;font-size: 22px;font-weight: bold;"><%= first %> <%= last %></div>
            <div style="box-sizing: border-box;margin-top: 20px;">
                <% if(border_colour) { %>
                    <img style="border: 15px #a5a4a4 solid !important;vertical-align: middle;box-sizing: border-box;outline: 10px solid <%= border_colour %>" src="<%= qr_url %>" alt="qr code"/>
                <% } else{ %>
                    <img src="<%= qr_url %>" alt="qr code" style="box-sizing: border-box;border-style: none;"/>
                <% } %>
            </div>
            <% if (isAppleDevice) { %>
                <div style="box-sizing: border-box;margin-top: 35px;">
                    <a href="<%= apple_wallet_url %>" target="_blank" style="box-sizing: border-box;color: #007bff;text-decoration: none;background-color: transparent;">
                        <img src="<%= apple_wallet_icon %>" alt="" style="box-sizing: border-box;vertical-align: middle;border-style: none;"/>
                    </a>
                </div>
            <% } %>
        </div>
        <div class="receipt_participant_information" style="box-sizing: border-box;width: 100%;margin-top: 40px;">
            <div class="receipt_participant_information-title" style="box-sizing: border-box;">
                <div class="receipt_participant_information-label" style="box-sizing: border-box;font-size: 18px;color: #9b9b9b;min-width: 185px;">
                    Participant Information:
                </div>
            </div>
            <div class="receipt_participant_information-body" style="box-sizing: border-box;margin-top: 40px;">
                <% if (ticket_type === 'daily') { %>
                    <div class="receipt_participant_information-body_item" style="box-sizing: border-box;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                        <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">Available Dates:</div>
                        <div class="receipt_participant_information-body_item-value_letter" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;">
                            <% for(const date of valid_dates || ['All Days']) { %>
                                <div><%= date %></div>
                            <% } %>
                        </div>
                    </div>
                <% } %>
                <div class="receipt_participant_information-body_item" style="box-sizing: border-box;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                    <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">Pass barcode:</div>
                    <div class="receipt_participant_information-body_item-value_letter" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;"><%= barcode %></div>
                </div>
                <div class="receipt_participant_information-body_item" style="box-sizing: border-box;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                    <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">Email:</div>
                    <div class="receipt_participant_information-body_item-value_letter" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;"><%= email || '-' %></div>
                </div>
                <div class="receipt_participant_information-body_item" style="box-sizing: border-box;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                    <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">Phone:</div>
                    <div class="receipt_participant_information-body_item-value_letter" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;"><%= phone || '-' %></div>
                </div>
                <div class="receipt_participant_information-body_item" style="box-sizing: border-box;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                    <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">Zip Code:</div>
                    <div class="receipt_participant_information-body_item-value_letter" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;"><%= user_zip || '-' %></div>
                </div>
                <div class="receipt_participant_information-body_item" style="box-sizing: border-box;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                    <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">Purchased:</div>
                    <div class="receipt_participant_information-body_item-value_letter" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;"><%= purchased_at || '-' %></div>
                </div>
                <div class="receipt_participant_information-body_item receipt_price" style="box-sizing: border-box;margin-top: 60px;display: table;table-layout: fixed;width: 100%;margin-bottom: 20px;">
                    <div class="receipt_participant_information-body_item-label_letter" style="box-sizing: border-box;width: 35%;text-align: right;font-size: 16px;display: table-cell;">
                        <div style="box-sizing: border-box;">Price:</div>
                    </div>
                    <div class="receipt_participant_information-body_item-value_letter text-bold" style="box-sizing: border-box;width: 65%;font-size: 20px;display: table-cell;overflow: hidden;text-overflow: ellipsis;padding-left: 20px;">$<%= amount %></div>
                </div>
            </div>
        </div>
    </div>
    <% if (allPurchases && allPurchases.length) { %>
        <div class="receipt_ticket-holders" style="box-sizing: border-box;margin-top: 40px;">
            <div style="box-sizing: border-box;">
                <div style="box-sizing: border-box;font-size: 18px;color: #9b9b9b;min-width: 275px;">
                    All ticket holders on this purchase:
                </div>
            </div>
            <div style="box-sizing: border-box;font-size: 12px;font-style: oblique;color: #9b9b9b;">
                (Click on a name to access each ticket if you do not receive the individual QR code ticket email)
            </div>
            <div style="box-sizing: border-box;margin-top: 30px;">
                <% for(var i = 0; i < allPurchases.length; ++i) { %>
                    <a href="<%= allPurchases[i].hash %>" target="_blank" class="btn receipt_ticket-holders_item" style="overflow: hidden; text-overflow: ellipsis; box-sizing: border-box;color: #337ab7;text-decoration: none;background-color: transparent;display: block;margin-bottom: 0;font-weight: bold;text-align: center;vertical-align: middle;cursor: pointer;background-image: none;border: solid 2px #337ab7 !important;white-space: nowrap;padding: 9px 0;font-size: 18px;line-height: 1.428571429;border-radius: 4px;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;max-width: 600px;margin: 0 auto 26px;">
                        <span style="box-sizing: border-box;"><%= allPurchases[i].first %> <%= allPurchases[i].last %></span>
                    </a>
                <%}%>
            </div>
        </div>
    <% } %>
    <% if (tickets_receipt_descr) { %>
        <div class="receipt_important-information" style="box-sizing: border-box;margin-top: 45px;">
            <div class="receipt_important-information_title" style="box-sizing: border-box;">
                <div class="receipt_important-information_title-label" style="box-sizing: border-box;font-size: 18px;color: #9b9b9b;min-width: 180px;">
                    Important Information:
                </div>
            </div>
            <div class="receipt_important-information_body" style="box-sizing: border-box;margin-top: 25px;">
                <%- tickets_receipt_descr %>
            </div>
        </div>
    <% } %>
    <% if (social_links && social_links.length) { %>
        <div style="box-sizing: border-box;margin-top: 55px;">
            <div style="box-sizing: border-box;text-align: center;padding: 0 20px;">
                <% for(var i = 0; i < social_links.length; ++i) { %>
                    <% if ((social_links[i].name === 'Snapchat' || social_links[i].title === 'Snapchat') && social_links[i].value) { %>
                        <% social_links[i].value = `https://www.snapchat.com/add/${social_links[i].value}` %>
                        <span style="box-sizing: border-box;margin-right: 5px;">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;box-sizing: border-box;color: #007bff;background-color: transparent;" target="_blank">
                                    <img src="<%= app_domain  + '/images/social_networks/snapchat.png'%>" style="width: 30px;height: 30px;box-sizing: border-box;vertical-align: middle;border-style: none;" title="<%= social_links[i].title %>" alt="<%= social_links[i].title %>">
                                </a>
                            </span>
                    <% } %>

                    <% if ((social_links[i].name === 'Twitter' || social_links[i].title === 'Twitter') && social_links[i].value) { %>
                        <span style="box-sizing: border-box;margin-right: 5px;">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;box-sizing: border-box;color: #007bff;background-color: transparent;" target="_blank">
                                    <img src="<%= app_domain  + '/images/social_networks/twitter.png'%>" style="width: 30px;height: 30px;box-sizing: border-box;vertical-align: middle;border-style: none;" title="<%= social_links[i].title %>" alt="<%= social_links[i].title %>">
                                </a>
                            </span>
                    <% } %>

                    <% if ((social_links[i].name === 'Facebook' || social_links[i].title === 'Facebook') && social_links[i].value) { %>
                        <span style="box-sizing: border-box;margin-right: 5px;">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;box-sizing: border-box;color: #007bff;background-color: transparent;" target="_blank">
                                    <img src="<%= app_domain  + '/images/social_networks/facebook.png'%>" style="width: 30px;height: 30px;box-sizing: border-box;vertical-align: middle;border-style: none;" title="<%= social_links[i].title %>" alt="<%= social_links[i].title %>">
                                </a>
                            </span>
                    <% } %>

                    <% if ((social_links[i].name === 'Instagram' || social_links[i].title === 'Instagram') && social_links[i].value) { %>
                        <span style="box-sizing: border-box;margin-right: 0;">
                                <a href="<%= social_links[i].value %>" style="text-decoration: none;box-sizing: border-box;color: #007bff;background-color: transparent;" target="_blank">
                                    <img src="<%= app_domain  + '/images/social_networks/instagram.png'%>" style="width: 30px;height: 30px;box-sizing: border-box;vertical-align: middle;border-style: none;" title="<%= social_links[i].title %>" alt="<%= social_links[i].title %>">
                                </a>
                            </span>
                    <% } %>
                <% } %>
            </div>
        </div>
    <% } %>
</div>
</body>
</html>
