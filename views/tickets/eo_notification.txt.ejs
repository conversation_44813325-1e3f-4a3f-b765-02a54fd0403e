Purchase created
Payment Type: <%- method %>
Barcode: <%- barcode %>			
<% if(method === 'card') { %>Cardholder: <%- cardholder %><% } else { %>Payer Name: <%- user_name %><% } %>
Email: <%- email %>
Event Name: <%- event_name %>
<% if(payment_method === 'waitlist' && receipt[0]) { %>
Camp name: <%- receipt[0].camp_name %>
<% } %>
Purchase Id: <%= purchase_id %>
<% if(payment_method !== 'waitlist') { %>
Receipt Url: <%- receipt_url %>
Purchase List: <%- tickets %>
<% } %>
Total: <%= total %>
SportWrench Inc. <%= new Date().getUTCFullYear() %>
