<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title><%= event_name %> Officials Assignments</title>
    <link rel="stylesheet" href="<%= sails.config.urls.main_app.baseUrl %>/../styles/main.css"/>
</head>
<body>
	<div class="container">
		<h3 class="text-center"><%= event_name %><br/><small class="text-grey">Officials Assignments at <%= event_day %></small></h3>
        <% if (officials.length) { %>
            <% for(var i = 0, l = officials.length, official; i < l; ++i) { %>
                <% official = officials[i]; %>
                <p class="lead"><%= official.name %></p>
                <% if (official.assignments.length) { %>
                    <table class="table table-condensed">
                        <thead>
                            <tr>
                                <th>Match Time</th>
                                <th>Court Name</th>
                                <th>Match Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% for(var j = 0, assignmentsLen = official.assignments.length; j < assignmentsLen; ++j) { %>
                                <tr>
                                    <td><%= official.assignments[j].start_hour %></td>
                                    <td><%= official.assignments[j].court_name %></td>
                                    <td><%= official.assignments[j].match_name %></td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                <% } else { %>
                    <div class="alert alert-warning text-center">No matches assigned to officiate yet.</div>
                <% } %>
            <% } %>
        <% } else { %>
            <div class="alert alert-warning text-center">No officials assigned yet.</div>
        <% } %>
	</div>
	<script>window.print();</script>
</body>
</html>
