-- --- obtain rows (testing)
-- SELECT pt.roster_team_id, p.date_paid
-- FROM purchase p
-- LEFT JOIN purchase_team pt ON pt.purchase_id = p.purchase_id
-- WHERE p.status = 'paid'
-- AND p.date_paid IS NOT NULL

--- Update roster team rows with missing date_paid
UPDATE roster_team rt
SET date_paid =
(
SELECT p.date_paid
FROM purchase p
LEFT JOIN purchase_team pt ON pt.purchase_id = p.purchase_id
WHERE p.status = 'paid'
AND p.date_paid IS NOT NULL
AND pt.roster_team_id = rt.roster_team_id
)
WHERE rt.status_paid = 22
AND rt.date_paid IS NULL