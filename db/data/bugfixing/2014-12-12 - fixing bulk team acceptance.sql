
SELECT *
FROM event_change
WHERE created > '2014-12-12 00:10:00'
AND "action" = 'Team accepted'
ORDER BY created DESC
LIMIT 300;

SELECT mc.director_email, rto.team_name
FROM event_change ec
INNER JOIN _roster_team_old_ok rto ON rto.roster_team_id = ec.roster_team_id
LEFT JOIN roster_club rc ON rto.roster_club_id = rc.roster_club_id
LEFT JOIN master_club mc ON mc.master_club_id = rc.master_club_id
WHERE ec.created > '2014-12-12 00:10:00'
AND ec.created < '2014-12-12 00:20:00'
AND ec."action" = 'Team accepted'
AND rto.status_entry = 11
GROUP BY mc.director_email


SELECT mc.director_email, mc.director_first, mc.director_last, mc.club_name, string_agg(rto.team_name, ', ') teams
FROM event_change ec
INNER JOIN _roster_team_old_ok rto ON rto.roster_team_id = ec.roster_team_id
LEFT JOIN roster_club rc ON rto.roster_club_id = rc.roster_club_id
LEFT JOIN master_club mc ON mc.master_club_id = rc.master_club_id
WHERE ec.created > '2014-12-12 00:10:00'
AND ec.created < '2014-12-12 00:20:00'
AND ec."action" = 'Team accepted'
AND rto.status_entry = 13
GROUP BY mc.director_email, mc.director_first, mc.director_last, mc.club_name;


SELECT rt.*
FROM roster_team rt
WHERE rt.roster_team_id IN
(SELECT rto.roster_team_id
FROM event_change ec
INNER JOIN _roster_team_old_ok rto ON rto.roster_team_id = ec.roster_team_id
LEFT JOIN roster_club rc ON rto.roster_club_id = rc.roster_club_id
LEFT JOIN master_club mc ON mc.master_club_id = rc.master_club_id
WHERE ec.created > '2014-12-12 00:10:00'
AND ec.created < '2014-12-12 00:20:00'
AND ec."action" = 'Team accepted'
);


UPDATE roster_team
SET status_entry = 14
WHERE roster_team_id IN
(SELECT rto.roster_team_id
FROM event_change ec
INNER JOIN _roster_team_old_ok rto ON rto.roster_team_id = ec.roster_team_id
LEFT JOIN roster_club rc ON rto.roster_club_id = rc.roster_club_id
LEFT JOIN master_club mc ON mc.master_club_id = rc.master_club_id
WHERE ec.created > '2014-12-12 00:10:00'
AND ec.created < '2014-12-12 00:20:00'
AND ec."action" = 'Team accepted'
);