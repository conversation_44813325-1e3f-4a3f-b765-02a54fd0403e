--- Cleaning up clubs with wrong staff data from webpoint: http://take.ms/IPNmf
DELETE FROM master_staff
WHERE master_club_id IN
(
SELECT DISTINCT master_club_id
FROM master_staff ms
WHERE ms.organization_code IN
(
SELECT organization_code
FROM master_staff
WHERE organization_code IS NOT NULL
GROUP BY organization_code
HAVING count(master_staff_id) > 1
)
AND master_team_id IS NULL
) AND master_team_id IS NULL
