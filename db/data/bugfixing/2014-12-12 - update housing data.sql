BEGIN;

---
--- Query to update housing values
---
UPDATE roster_team rt
SET
	total_tentative =
  COALESCE( (SELECT SUM( CASE
			WHEN ths.ths_hotel_status = 'Tentative' THEN ths.ths_tentative_nights
			WHEN ths.ths_hotel_status = 'Accepted' THEN ths.ths_tentative_nights
			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
			ELSE 0
			END)
  FROM ths_booking ths
  WHERE ths.roster_team_id = rt.roster_team_id
    )
  , 0),
	total_accepted =
  COALESCE( (SELECT SUM( CASE
			WHEN ths.ths_hotel_status = 'Accepted' THEN ths.ths_tentative_nights
			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
			ELSE 0
			END)
  FROM ths_booking ths
  WHERE ths.roster_team_id = rt.roster_team_id
    )
  , 0),
	total_confirmed =
 COALESCE( (SELECT SUM( CASE
			WHEN ths.ths_hotel_status = 'Confirmed' THEN ths.ths_confirmed_nights
			ELSE 0
			END)
  FROM ths_booking ths
  WHERE ths.roster_team_id = rt.roster_team_id
    )
  , 0)
---WHERE rt.roster_team_id IN (1)
;

UPDATE roster_team rt
SET max_total_accepted = total_accepted
WHERE  COALESCE(max_total_accepted,0) < total_accepted
;


--- Update roster_club rows
UPDATE roster_club
SET
total_tentative =
(SELECT SUM(rt.total_tentative)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL),
total_accepted =
(SELECT SUM(rt.total_accepted)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL),
total_confirmed =
(SELECT SUM(rt.total_confirmed)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL),
max_total_accepted =
(SELECT SUM(rt.max_total_accepted)
FROM roster_team rt
WHERE rt.roster_club_id = roster_club.roster_club_id
  AND rt.deleted IS NULL)

WHERE deleted IS NULL;


--- Update status_housing for roster_team

UPDATE roster_team _rt
SET status_housing = 31 -- red X

FROM roster_team rt
LEFT JOIN "event" e ON e.event_id = rt.event_id

WHERE COALESCE(rt.is_local, FALSE) = FALSE
AND rt.total_accepted < e.housing_nights_required
AND _rt.roster_team_id = rt.roster_team_id
;


-- UPDATE roster_team _rt
-- SET status_housing = 34 -- yellow (close)
--
-- FROM roster_team rt
-- LEFT JOIN "event" e ON e.event_id = rt.event_id
--
-- WHERE COALESCE(rt.is_local, FALSE) = FALSE
-- AND rt.total_accepted >= e.housing_nights_required
-- AND rt.total_accepted < e.housing_nights_required + e.housing_nights_threshold
-- AND _rt.roster_team_id = rt.roster_team_id
-- ;

UPDATE roster_team _rt
SET status_housing = 32 -- green (accepted)

FROM roster_team rt
LEFT JOIN "event" e ON e.event_id = rt.event_id

WHERE COALESCE(rt.is_local, FALSE) = FALSE
AND rt.total_accepted >= e.housing_nights_required -- + e.housing_nights_threshold
AND _rt.roster_team_id = rt.roster_team_id
;

COMMIT;
