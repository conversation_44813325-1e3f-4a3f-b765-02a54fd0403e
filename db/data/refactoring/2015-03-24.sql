BEGIN;


-- CREATE FIELD "isDeleted" ------------------------------------
ALTER TABLE "public"."master_team" ADD COLUMN "isDeleted" Timestamp Without Time Zone;
-- -------------------------------------------------------------;

UPDATE master_team SET "isDeleted" = now() WHERE deleted IS TRUE;

COMMIT;

BEGIN;


-- DROP FIELD "deleted" ----------------------------------------
ALTER TABLE "public"."master_team" DROP COLUMN "deleted";
-- -------------------------------------------------------------;

COMMIT;

BEGIN;


-- CHANGE "NAME" OF "FIELD "isDeleted" -------------------------
ALTER TABLE "public"."master_team" RENAME COLUMN "isDeleted" TO "deleted";
-- -------------------------------------------------------------;

COMMIT;

