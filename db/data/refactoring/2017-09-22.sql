-- create table for webpoint certname
DROP TABLE IF EXISTS webpoint_cert_name;

CREATE TABLE webpoint_cert_name (
        id SERIAL NOT NULL PRIMARY KEY,
        rang INT,
        wp_name TEXT,
        cert_name TEXT,
        cert_short_name TEXT
);

INSERT INTO webpoint_cert_name (rang, wp_name, cert_name, cert_short_name)
VALUES
-- REFCERTNAME
    (10, 'REFCERTNAME', 'National Referee', 'NR'),
    (20, 'REFCERTNAME', 'Regional Referee', 'RR'),
    (30, 'REFCERTNAME', 'Junior National Referee',   'JNR'),
    (40, 'REFCERTNAME', 'Junior Referee', 'J'),
    (50, 'REFCERTNAME', 'Region-Specific Referee 1', 'RSR1'),
    (60, 'REFCERTNAME', 'Region-Specific Referee 2', 'RSR2'),
    (70, 'REFCERTNAME', 'Region-Specific Referee 3', 'RSR3'),
    (80, 'REFCERTNAME', 'Provisional Referee', 'PR'),
-- SCORECERTNAME
    (10, 'SCORECERTNAME', 'National Scorer', 'NS'),
    (20, 'SCORECERTNAME', 'Regional Scorer', 'RS'),
    (30, 'SCORECERTNAME', 'Junior Scorer', 'JS'),
    (40, 'SCORECERTNAME', 'Region-Specific Scorer 1', 'RSS1'),
    (50, 'SCORECERTNAME', 'Region-Specific Scorer 2', 'RSS2'),
    (60, 'SCORECERTNAME', 'Provisional Scorer', 'PS')
;


-- add certificate related columns to master_athlete

ALTER TABLE master_athlete
    ADD COLUMN ref_cert_name TEXT,
    ADD COLUMN ref_end_date TEXT,
    ADD COLUMN score_cert_name TEXT,
    ADD COLUMN score_end_date TEXT,
    ADD COLUMN webpoint_sync TIMESTAMP WITHOUT TIME ZONE;


