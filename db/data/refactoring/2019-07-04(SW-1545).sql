-- Changes after INSERT:
--  * add new variable card_last_4
--  * set "custom_action": true to tickets_returned
--  * change field '{ticket_code}' to 'ticket_code'

BEGIN;
 UPDATE email_template_group SET variables = '[
  {
    "field": "event_name",
    "title": "Event Name",
    "pattern": "{event_name}"
  },
  {
    "field": "amount_refunded",
    "title": "Amount Refunded",
    "pattern": "{amount_refunded}"
  },
  {
    "field": "date_refunded",
    "title": "Date Issued",
    "pattern": "{date_refunded}"
  },
  {
    "field": "ticket_code",
    "title": "Ticket Code",
    "pattern": "{ticket_code}"
  },
  {
    "field": "payer",
    "title": "Payer''s Name",
    "pattern": "{payer}"
  },
  {
    "field": "eo_name",
    "title": "Event Owner First and Last Name",
    "pattern": "{eo_name}"
  },
  {
    "field": "eo_email",
    "title": "Event Owner Email",
    "pattern": "{eo_email}"
  },
  {
    "field": "eo_phone",
    "title": "Event Owner Phone",
    "pattern": "{eo_phone}"
  },
 {
    "field": "tickets_returned",
    "title": "Returned Tickets List",
    "pattern": "{tickets_returned}",
    "custom_action": true
  },
 {
    "field": "tickets_names_list",
    "title": "Bought Ticket Types List",
    "pattern": "{tickets}"
  },
 {
    "field": "camps_names_list",
    "title": "Bought Camps List",
    "pattern": "{camps}"
  },
 {
    "field": "receipt_link",
    "title": "List to Receipt",
    "pattern": "{receipt_link}",
    "custom_action": true
  },
  {
    "field": "purchase_date",
    "title": "Purchase Date",
    "pattern": "{purchase_date}"
  },
  {
    "field": "total_amount",
    "title": "Total Purchase Amount",
    "pattern": "{total_amount}"
  },
  {
    "field": "adjusted_total_amount",
    "title": "Adjusted Total Amount",
    "pattern": "{adjusted_total_amount}"
  },
  {
    "field": "payments_list_link",
    "title": "Link to Event Owner Purchases List",
    "pattern": "{payments_list_link}",
    "custom_action": true
  },
  {
    "field": "social_icons",
    "title": "Social Icons",
    "pattern": "{social_icons}",
    "custom_action": true
  },
  {
    "field": "facebook_icon",
    "title": "Facebook Icon",
    "pattern": "{facebook_icon}",
    "custom_action": true
  },
  {
    "field": "twitter_icon",
    "title": "Twitter Icon",
    "pattern": "{twitter_icon}",
    "custom_action": true
  },
  {
    "field": "instagram_icon",
    "title": "Instagram Icon",
    "pattern": "{instagram_icon}",
    "custom_action": true
  },
  {
    "field": "snapchat_icon",
    "title": "Snapchat Icon",
    "pattern": "{snapchat_icon}",
    "custom_action": true
  },
  {
     "field": "card_last_4",
     "title": "Last four digits card",
     "pattern": "{card_last_4}"
  }
]'
WHERE "group" = 'tickets.payments';
COMMIT;
