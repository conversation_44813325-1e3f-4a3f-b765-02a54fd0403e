-- CREATE TYPE "stripe_payment_type" ---------------------------
CREATE TYPE "public"."stripe_payment_type" AS Enum( 'default', 'connect' );

COMMENT ON TYPE  "public"."stripe_payment_type" IS 'default - usual stripe payment
connect - payment to standalone acc (via platform)';
-- -------------------------------------------------------------


BEGIN;

-- CREATE FIELD "stripe_payment_type" --------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "stripe_payment_type" "public"."stripe_payment_type";
-- -------------------------------------------------------------

COMMIT;
