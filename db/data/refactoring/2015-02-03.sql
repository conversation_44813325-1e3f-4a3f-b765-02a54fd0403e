BEGIN;

-- CREATE FIELDs for "master_athlete" -----------------------------------------
ALTER TABLE "public"."master_athlete" ADD COLUMN "nick" Character Varying( 20 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "address2" Character Varying( 100 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "other_phone" Character Varying( 50 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "parent1_first" Character Varying( 20 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "parent1_last" Character Varying( 20 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "parent1_email" Character Varying( 100 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "parent2_first" Character Varying( 20 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "parent2_last" Character Varying( 2044 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "parent2_email" Character Varying( 100 );
ALTER TABLE "public"."master_athlete" ADD COLUMN "membership_status" Character Varying( 20 );
-- -------------------------------------------------------------;


-- CREATE FIELDs for "master_staff" -----------------------------------------
ALTER TABLE "public"."master_staff" ADD COLUMN "nick" Character Varying( 20 );
ALTER TABLE "public"."master_staff" ADD COLUMN "address2" Character Varying( 100 );
ALTER TABLE "public"."master_staff" ADD COLUMN "phonew" Character Varying( 50 );
ALTER TABLE "public"."master_staff" ADD COLUMN "phoneo" Character Varying( 50 );
ALTER TABLE "public"."master_staff" ADD COLUMN "membership_status" Character Varying( 20 );
ALTER TABLE "public"."master_staff" ADD COLUMN "bg_screening" Character Varying( 20 );
ALTER TABLE "public"."master_staff" ADD COLUMN "bg_expire_date" Timestamp Without Time Zone;
ALTER TABLE "public"."master_staff" ADD COLUMN "chaperone_status" Character Varying( 20 );
ALTER TABLE "public"."master_staff" ADD COLUMN "coach_status" Character Varying( 20 );
-- -------------------------------------------------------------;

COMMIT;



BEGIN;


-- CREATE TABLE "bracketmatch" ---------------------------------
CREATE TABLE "public"."bracketmatch" (
	"bracketmatch_id" Serial NOT NULL UNIQUE,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"display_name" CHARACTER VARYING( 50 ),
	"division_id" INTEGER,
	"division_short_name" CHARACTER VARYING( 50 ),
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"name" CHARACTER VARYING( 50 ),
	"poolbracket_uuid" CHARACTER VARYING( 50 ),
	"short_name" CHARACTER VARYING( 50 ),
	"match_number" INTEGER,
	"team1_roster_id" INTEGER,
	"team2_roster_id" INTEGER,
	"event_id" INTEGER
, CONSTRAINT "unique_bracketmatch_id" UNIQUE( "bracketmatch_id" ) );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;

-- CHANGE "COMMENT" OF "FIELD "poolbracket_uuid" ---------------
COMMENT ON COLUMN "public"."bracketmatch"."poolbracket_uuid" IS 'Bracket UUID';
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "poolbracket_uuid" ------------------
ALTER TABLE "public"."bracketmatch" RENAME COLUMN "poolbracket_uuid" TO "uuid";
-- -------------------------------------------------------------;

COMMIT;


--- Fixing responded=NULL bug
update webpoint_queue
set responded = requested
where responded is NULL;

-- Not sure if we even need that - Marc said no
--
-- BEGIN;
--
--
-- -- CREATE FIELD "winning_roster_id" ----------------------------
-- ALTER TABLE "public"."bracketmatch" ADD COLUMN "winning_roster_id" INTEGER;
-- COMMENT ON COLUMN "public"."bracketmatch"."winning_roster_id" IS 'Winning Team roster ID';
-- -- -------------------------------------------------------------
--
-- -- CREATE FIELD "score" ----------------------------------------
-- ALTER TABLE "public"."bracketmatch" ADD COLUMN "score" CHARACTER VARYING( 100 );
-- COMMENT ON COLUMN "public"."bracketmatch"."score" IS 'Match Score';
-- -- -------------------------------------------------------------;
--
-- COMMIT;
