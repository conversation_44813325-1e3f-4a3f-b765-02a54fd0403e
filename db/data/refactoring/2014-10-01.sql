---
--- Adding Club Director fields to master_club table.
---

BEGIN;


-- CREATE FIELD "director_first" -------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_first" CHARACTER VARYING( 100 );
COMMENT ON COLUMN "public"."master_club"."director_first" IS 'Club Director First Name';
-- -------------------------------------------------------------

-- CREATE FIELD "director_last" --------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_last" CHARACTER VARYING( 100 );
COMMENT ON COLUMN "public"."master_club"."director_last" IS 'Club Director Last Name';
-- -------------------------------------------------------------

-- CREATE FIELD "director_email" -------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_email" CHARACTER VARYING( 100 );
COMMENT ON COLUMN "public"."master_club"."director_email" IS 'Club Director Email';
-- -------------------------------------------------------------

-- CREATE FIELD "director_phone" -------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_phone" CHARACTER VARYING( 20 );
COMMENT ON COLUMN "public"."master_club"."director_phone" IS 'Club Director Phone';
-- -------------------------------------------------------------

-- CREATE FIELD "director_birthdate" ---------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_birthdate" Date;
COMMENT ON COLUMN "public"."master_club"."director_birthdate" IS 'Club Director Birthdate';
-- -------------------------------------------------------------

-- CREATE FIELD "director_gender" ------------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_gender" "public"."gender";
COMMENT ON COLUMN "public"."master_club"."director_gender" IS 'Club Director Gender';
-- -------------------------------------------------------------

-- CREATE FIELD "director_modified" ----------------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_modified" TIMESTAMP WITHOUT TIME ZONE;
COMMENT ON COLUMN "public"."master_club"."director_modified" IS 'Timestamp when director info last updated';
-- -------------------------------------------------------------;

COMMIT;


---
--- Adding Per Team Credit Surcharge column to event table.
---
BEGIN;


-- CREATE FIELD "credit_surcharge" -----------------------------
ALTER TABLE "public"."event" ADD COLUMN "credit_surcharge" REAL;
COMMENT ON COLUMN "public"."event"."credit_surcharge" IS 'Per Team Credit Surcharge (This amount will be added to every team entry for credit card payments)';
-- -------------------------------------------------------------;

COMMIT;


---
--- Adding director_club_staff_id to master_club to link Club table to Club Director row in Staff table.
---

BEGIN;


-- CREATE FIELD "director_club_staff_id" -----------------------
ALTER TABLE "public"."master_club" ADD COLUMN "director_club_staff_id" INTEGER;
COMMENT ON COLUMN "public"."master_club"."director_club_staff_id" IS 'Club Director staff ID';
-- -------------------------------------------------------------;

COMMIT;
--- Rename column name
BEGIN;


-- CHANGE "NAME" OF "FIELD "director_club_staff_id" ------------
ALTER TABLE "public"."master_club" RENAME COLUMN "director_club_staff_id" TO "director_master_staff_id";
-- -------------------------------------------------------------;

COMMIT;
