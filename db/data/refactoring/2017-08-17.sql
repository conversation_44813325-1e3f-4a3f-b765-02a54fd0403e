/*
* PAYOUTS
*/

/*
* TODO
* On prod DB:
*            1. Apply this script
*            2. Set account_type for every stripe account
*            3. Set account_type to NOT NULL
*/

-- adding columns and constraints to stripe_account
BEGIN;

CREATE TYPE stripe_account_type AS ENUM('standard','express','custom');

ALTER TABLE stripe_account 
        ADD COLUMN "is_platform"                BOOLEAN NOT NULL DEFAULT FALSE,
        ADD COLUMN "user_id"                    INT,
        ADD COLUMN "connected_to_account_id"    TEXT,
        ADD COLUMN "account_type"               stripe_account_type,
        ADD COLUMN "platform_client_id"         TEXT,
        -- only a STANDARD account can be a Platform
        ADD CONSTRAINT CK_is_platform 
                CHECK (account_type = 'standard' OR is_platform = FALSE),
        -- user_id is NULL if event_owner_id is NOT NULL
        ADD CONSTRAINT CK_user_id     
                CHECK (event_owner_id IS NULL OR user_id IS NULL),
        -- connected_to_account_id filled for EXPRESS or CUSTOM accounts
        ADD CONSTRAINT CK_connected_to_account_id
                CHECK (account_type IN ('express','custom') OR connected_to_account_id IS NULL),
        -- platform_client_id can be NOT NULL only if is_platform = TRUE
        ADD CONSTRAINT CK_platform_client_id
                CHECK (is_platform = TRUE OR platform_client_id IS NULL);


COMMENT ON COLUMN stripe_account.is_platform IS 'Shows whether an account has a created platform on it. Only a STANDARD account can be a Platform.';
COMMENT ON COLUMN stripe_account.user_id IS 'Shows if an account is tied to a specific user. This field is empty if "event_owner_id" is filled.';
COMMENT ON COLUMN stripe_account.connected_to_account_id IS 'filled for EXPRESS or CUSTOM accounts. Represents a "stripe_account_id" of a STANDARD account to which a certain account is connected.';
COMMENT ON COLUMN stripe_account.account_type IS '(enum) <standard, express, custom> - these are types of Stripes accounts';
COMMENT ON COLUMN stripe_account.platform_client_id IS 'Filled only for platforms. This id is used for connecting other accounts to a specific platform';

COMMIT;