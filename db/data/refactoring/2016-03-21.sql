BEGIN;

CREATE SEQUENCE "public"."event_official_group_id_seq"
INCREMENT 1
MINVALUE 1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- CREATE TABLE "event_official_group" -------------------------
CREATE TABLE "public"."event_official_group" ( 
	"event_official_group_id" Integer DEFAULT nextval('event_official_group_id_seq'::regclass) NOT NULL,
	"created" Timestamp Without Time Zone DEFAULT now(),
	"modified" Timestamp Without Time Zone DEFAULT now(),
	"pattern_id" Integer,
	"group_name" Text COLLATE "pg_catalog"."default" NOT NULL,
	CONSTRAINT "unique_event_official_group_id" UNIQUE( "event_official_group_id" ) );
 COMMENT ON TABLE  "public"."event_official_group" IS 'Officials Group for the Schedule';
-- -------------------------------------------------------------

CREATE TRIGGER update_event_official_group_modified
BEFORE UPDATE ON "public"."event_official_group" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

COMMIT;


BEGIN;

-- CREATE FIELD "event_official_group_id" ----------------------
ALTER TABLE "public"."event_official_schedule" ADD COLUMN "event_official_group_id" Integer;
-- -------------------------------------------------------------

-- CREATE INDEX "index_event_official_group_id" ----------------
CREATE INDEX "index_event_official_group_id" ON "public"."event_official_schedule" USING btree( "event_official_group_id" );
-- -------------------------------------------------------------

COMMIT;

