BEGIN;

CREATE SEQUENCE "public"."purchase_history_id_seq"
INCREMENT 1
MINVALUE 0
MAXVALUE 9223372036854775807
START 1
CACHE 1
;
COMMENT ON SEQUENCE "public"."purchase_history_id_seq" IS 'Sequence for purchase_history table';



-- CREATE TABLE "purchase_history" -----------------------------
CREATE TABLE "public"."purchase_history" ( 
	"purchase_history_id" Integer DEFAULT nextval('purchase_history_id_seq'::regclass) NOT NULL,
	"created" Timestamp Without Time Zone DEFAULT now(),
	"modified" Timestamp Without Time Zone DEFAULT now(),
	"purchase_id" Integer NOT NULL,
	"action" Text COLLATE "pg_catalog"."default" NOT NULL,
	"stripe_event_id" Integer,
	"description" Text COLLATE "pg_catalog"."default",
	"notes" Text COLLATE "pg_catalog"."default",
	"user_id" Integer,
	"amount" Numeric,
	CONSTRAINT "unique_purchase_history_id" UNIQUE( "purchase_history_id" ) );
 
CREATE INDEX "purchase_id_index_history" ON "public"."purchase_history" USING btree( "purchase_id" Asc NULLS Last );


CREATE INDEX "user_id_index_history" ON "public"."purchase_history" USING btree( "user_id" Asc NULLS Last );
-- -------------------------------------------------------------

CREATE TRIGGER update_purchase_history_modified
BEFORE UPDATE ON "public"."purchase_history" 
FOR EACH ROW EXECUTE PROCEDURE update_modified_column();


COMMIT;

BEGIN;

-- CREATE FIELD "debt" -----------------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "debt" Numeric;COMMENT ON COLUMN "public"."purchase"."debt" IS 'Debt amount: negative if seller needs to give back some purchase amount to buyer, positive if buyer owes some amount to seller';
-- -------------------------------------------------------------

-- CREATE FIELD "dispute_status" -------------------------------
ALTER TABLE "public"."purchase" ADD COLUMN "dispute_status" Text COLLATE "pg_catalog"."default";COMMENT ON COLUMN "public"."purchase"."dispute_status" IS 'status of stripe dispute: pending, won, lost';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "TYPE" OF "FIELD "stripe_event_id" -------------------
ALTER TABLE "public"."purchase_history" ALTER COLUMN "stripe_event_id" TYPE Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

COMMIT;


