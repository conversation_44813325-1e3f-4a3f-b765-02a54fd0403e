
DROP VIEW IF EXISTS v_roster_member;

CREATE VIEW v_roster_member
AS
--master_athlete
SELECT 'master_athlete' as update_table, 'athlete' as role, ra.roster_team_id, ma.master_club_id, ma.master_athlete_id as id,  ma.first, ma.last, 
        e.event_id, e.season, e.date_start, e.date_end, 
        ma.usav_number, ma.organization_code, ma.webpoint_sync,
        ma.ref_cert_name, ma.ref_end_date, ma.score_cert_name, ma.score_end_date
FROM roster_athlete ra JOIN event e ON ra.event_id = e.event_id
     join master_athlete ma ON ma.master_athlete_id = ra.master_athlete_id 
WHERE ra.deleted IS NULL
      AND ra.deleted_by_user IS NULL
      AND ra.roster_team_id IS NOT NULL
UNION
--master_staff
SELECT 'master_staff' as update_table,  'staff' as role,rsr.roster_team_id, ms.master_club_id, ms.master_staff_id as id,  ms.first, ms.last, 
        e.event_id, e.season, e.date_start, e.date_end, 
        ms.usav_number, ms.organization_code, ms.webpoint_sync,
        ms.ref_cert_name, ms.ref_end_date, ms.score_cert_name, ms.score_end_date
FROM roster_staff_role  rsr JOIN master_staff ms ON rsr.master_staff_id = ms.master_staff_id
     JOIN roster_team rt ON rt.roster_team_id = rsr.roster_team_id
     JOIN event e on e.event_id = rt.event_id
WHERE rsr.deleted IS NULL
      AND rsr.deleted_by_user IS NULL
      AND ms.deleted IS NULL
      AND rsr.roster_team_id IS NOT NULL;