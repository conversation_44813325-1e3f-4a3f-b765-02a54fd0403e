BEGIN;

-- CREATE FIELD "description" ----------------------------------
ALTER TABLE "public"."email_template_type" ADD COLUMN "description" Text;COMMENT ON COLUMN "public"."email_template_type"."description" IS 'Description of a certain template type. May contain HTML';
-- -------------------------------------------------------------

-- CREATE FIELD "long_title" -----------------------------------
ALTER TABLE "public"."email_template_type" ADD COLUMN "long_title" Text;COMMENT ON COLUMN "public"."email_template_type"."long_title" IS 'We suppose "title" field for some short name of a certain email type and "long_title" for a more verbose name';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "NULLABLE" OF "FIELD "description" -------------------
ALTER TABLE "public"."email_template_type" ALTER COLUMN "description" SET NOT NULL;
-- -------------------------------------------------------------

-- CHANGE "NULLABLE" OF "FIELD "long_title" --------------------
ALTER TABLE "public"."email_template_type" ALTER COLUMN "long_title" SET NOT NULL;
-- -------------------------------------------------------------

COMMIT;

