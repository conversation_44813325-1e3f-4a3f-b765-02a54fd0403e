BEGIN;

-- CREATE FIELD "usav_number" ----------------------------------
ALTER TABLE "public"."webpoint_parse" ADD COLUMN "usav_number" Integer;COMMENT ON COLUMN "public"."webpoint_parse"."usav_number" IS 'The integer number (7-digit) from usav code.';
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "usav_number" ----------------------------------
ALTER TABLE "public"."webpoint_athlete" ADD COLUMN "usav_number" Integer;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "usav_number" ----------------------------------
ALTER TABLE "public"."webpoint_adult" ADD COLUMN "usav_number" Integer;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "member_usav_number" ---------------------------
ALTER TABLE "public"."webpoint_team_member" ADD COLUMN "member_usav_number" Integer;
-- -------------------------------------------------------------

COMMIT;


begin;
-- Athletes
update master_athlete
set usav_number = ((regexp_matches(organization_code, '[0-9]{7,7}', 'g'))[1])::integer;
update webpoint_athlete
set usav_number = ((regexp_matches(usav_code, '[0-9]{7,7}', 'g'))[1])::integer;
-- Adults
update master_staff
set usav_number = ((regexp_matches(organization_code, '[0-9]{7,7}', 'g'))[1])::integer;
update webpoint_adult
set usav_number = ((regexp_matches(usav_code, '[0-9]{7,7}', 'g'))[1])::integer;

update webpoint_parse
set usav_number = ((regexp_matches(usav_code, '[0-9]{7,7}', 'g'))[1])::integer;
commit;

