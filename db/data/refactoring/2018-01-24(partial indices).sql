BEGIN;


-- UPDATE roster_athlete_roster_team_id_index index to partial ---------------------
DROP INDEX IF EXISTS roster_athlete_roster_team_id_index RESTRICT;

CREATE INDEX IF NOT EXISTS roster_athlete_roster_team_id_index
  ON public.roster_athlete (roster_team_id)
  WHERE deleted IS NULL AND deleted_by_user IS NULL;
-------------------------------------------------------------------------------------


-- UPDATE rsr_roster_team_id_index index to partial ---------------------------------
DROP INDEX IF EXISTS rsr_roster_team_id_index RESTRICT;

CREATE INDEX IF NOT EXISTS rsr_roster_team_id_index
  ON public.roster_staff_role (roster_team_id)
  WHER<PERSON> deleted IS NULL AND deleted_by_user IS NULL;
-------------------------------------------------------------------------------------


COMMIT;
