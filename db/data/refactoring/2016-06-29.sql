BEGIN;

-- CREATE INDEX "index_stripe_event_event_id" ------------------
CREATE INDEX "index_stripe_event_event_id" ON "public"."stripe_event" USING btree( "event_id" Asc NULLS Last );
-- -------------------------------------------------------------

COMMIT;


BEGIN;

CREATE INDEX "ticket_discount_last_index" ON "public"."ticket_discount" (LOWER(TRIM("last")));
CREATE INDEX "ticket_discount_email_index" ON "public"."ticket_discount" (LOWER(TRIM("email")));
CREATE INDEX "ticket_discount_code_index" ON "public"."ticket_discount" (LOWER(TRIM("code")));

COMMIT;

BEGIN;

-- CHANGE "TYPE" OF "FIELD "tickets_scan" ----------------------
ALTER TABLE "public"."purchase" ALTER COLUMN "tickets_scan" TYPE Text;
-- -------------------------------------------------------------

COMMIT;
