BEGIN;


-- CREATE INDEX "rt1_index" ------------------------------------
CREATE INDEX "rt1_index" ON "public"."matches" USING btree( "team1_roster_id" );
-- -------------------------------------------------------------

-- CREATE INDEX "rt2_index" ------------------------------------
CREATE INDEX "rt2_index" ON "public"."matches" USING btree( "team2_roster_id" );
-- -------------------------------------------------------------

-- CREATE INDEX "ret_ref_index" --------------------------------
CREATE INDEX "ret_ref_index" ON "public"."matches" USING btree( "ref_roster_id" );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE INDEX "poolbracketid_index" --------------------------
CREATE INDEX "poolbracketid_index" ON "public"."matches" USING btree( "pool_bracket_id" Desc NULLS Last );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE INDEX "event_change_email__id_index" -----------------
CREATE INDEX "event_change_email__id_index" ON "public"."event_change" USING btree( "event_email_id" NULLS Last );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE INDEX "event_email_id_index" -------------------------
CREATE INDEX "event_email_id_index" ON "public"."event_email" USING btree( "event_email_id" );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE INDEX "match_uuid_index" -----------------------------
CREATE INDEX "match_uuid_index" ON "public"."matches" USING btree( "match_id" );
-- -------------------------------------------------------------

-- CREATE INDEX "event_id_index" -------------------------------
CREATE INDEX "event_id_index" ON "public"."matches" USING btree( "event_id" );
-- -------------------------------------------------------------;

COMMIT;

BEGIN;


-- CREATE INDEX "organization_code_index" ----------------------
CREATE INDEX "organization_code_index" ON "public"."roster_team" USING btree( "organization_code" NULLS Last );
-- -------------------------------------------------------------;

COMMIT;

