BEGIN;


-- CREATE TABLE "webpoint_athlete" -----------------------------
CREATE TABLE "public"."webpoint_athlete" ( 
    "webpoint_athlete_id" Serial NOT NULL UNIQUE, 
    "master_club_id" Integer NOT NULL, 
    "webpoint_parse_id" Integer NOT NULL, 
    "parent1_first" Character Varying( 200 ), 
    "parent1_last" Character Varying( 200 ), 
    "parent1_email" Character Varying( 200 ), 
    "parent2_first" Character Varying( 100 ), 
    "parent2_last" Character Varying( 100 ), 
    "parent2_email" Character Varying( 200 ), 
    "level_of_play" Character Varying( 10 ), 
    "gradyear" Integer, 
    "grade_in_school" Integer
, CONSTRAINT "unique_webpoint_athlete_id" UNIQUE( "webpoint_athlete_id" ) );
CREATE UNIQUE INDEX "index_webpoint_athlete_id" ON "public"."webpoint_athlete" USING btree( "webpoint_athlete_id" );
-- -------------------------------------------------------------;

COMMIT;

BEGIN;


-- CREATE FIELD "usav_code" ------------------------------------
ALTER TABLE "public"."webpoint_athlete" ADD COLUMN "usav_code" Character Varying( 20 );
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CHANGE "TYPE" OF "FIELD "grade_in_school" -------------------
ALTER TABLE "public"."webpoint_athlete" ALTER COLUMN "grade_in_school" TYPE Character Varying;
-- -------------------------------------------------------------;

COMMIT;

BEGIN;


-- CREATE FIELD "created" --------------------------------------
ALTER TABLE "public"."webpoint_athlete" ADD COLUMN "created" Timestamp Without Time Zone DEFAULT now() NOT NULL;
-- -------------------------------------------------------------

-- CREATE FIELD "modified" -------------------------------------
ALTER TABLE "public"."webpoint_athlete" ADD COLUMN "modified" Timestamp Without Time Zone DEFAULT now() NOT NULL;
-- -------------------------------------------------------------;

COMMIT;


