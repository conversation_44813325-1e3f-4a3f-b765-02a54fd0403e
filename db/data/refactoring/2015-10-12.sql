BEGIN;

-- CREATE FIELD "age" ------------------------------------------
ALTER TABLE "public"."roster_athlete" ADD COLUMN "age" Integer;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CHANGE "NULLABLE" OF "FIELD "event_id" ----------------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "event_id" DROP NOT NULL;
-- -------------------------------------------------------------


-- CHANGE "NULLABLE" OF "FIELD "roster_team_id" ----------------
ALTER TABLE "public"."roster_athlete" ALTER COLUMN "roster_team_id" DROP NOT NULL;
-- -------------------------------------------------------------


COMMIT;


BEGIN;

-- CREATE FIELD "master_staff_id" ------------------------------
ALTER TABLE "public"."roster_staff_role" ADD COLUMN "master_staff_id" Integer NOT NULL;
-- -------------------------------------------------------------

-- CREATE INDEX "rsr_master_staff_id_index" --------------------
CREATE INDEX "rsr_master_staff_id_index" ON "public"."roster_staff_role" USING btree( "master_staff_id" Asc NULLS Last );
-- -------------------------------------------------------------

-- DROP INDEX "rsr_roster_staff_id_index" ----------------------
DROP INDEX IF EXISTS "public"."rsr_roster_staff_id_index";
-- -------------------------------------------------------------

-- DROP FIELD "roster_staff_id" --------------------------------
ALTER TABLE "public"."roster_staff_role" DROP COLUMN "roster_staff_id";
-- -------------------------------------------------------------

COMMIT;
