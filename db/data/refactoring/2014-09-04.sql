BEGIN;


-- CREATE TABLE "event_location" -------------------------------
CREATE TABLE "public"."event_location" (
	"event_location_id" Serial NOT NULL UNIQUE,
	"date_created" TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE NOT NULL,
	"date_modified" TIM<PERSON><PERSON>MP WITHOUT TIME ZONE NOT NULL,
	"event_id" INTEGER NOT NULL,
	"name" CHARACTER VARYING( 200 ) NOT NULL,
	"short_name" CHARACTER VARYING( 8 ) NOT NULL,
	"sort_order" INTEGER NOT NULL,
	"address" CHARACTER VARYING( 200 ),
	"city" CHARACTER VARYING( 200 ),
	"state" CHARACTER VARYING( 2 ),
	"zip" CHARACTER VARYING( 7 ),
	"courts_from" INTEGER,
	"courts_to" INTEGER,
 PRIMARY KEY ( "event_location_id" )
, CONSTRAINT "event_location_name_Unique" UNIQUE( "event_id","name" ), CONSTRAINT "event_location_short_name_Unique" UNIQUE( "event_id","short_name" ) );
CREATE INDEX "index_event_location_id" ON "public"."event_location" USING btree( "event_location_id" );

COMMENT ON TABLE  "public"."event_location" IS 'Event Locations data';
-- Set comments for fields
COMMENT ON COLUMN "public"."event_location"."name" IS 'Long Location Name';COMMENT ON COLUMN "public"."event_location"."short_name" IS 'Short Location Name';
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE FIELD "event_location_id" ----------------------------
ALTER TABLE "public"."schedule" ADD COLUMN "event_location_id" INTEGER;COMMENT ON COLUMN "public"."schedule"."event_location_id" IS 'Event Location id';
-- -------------------------------------------------------------;

COMMIT;




BEGIN;


-- CREATE TABLE "master_club_sanctioning" ----------------------
CREATE TABLE "public"."master_club_sanctioning" (
	"master_club_sanctioning_id" Serial NOT NULL UNIQUE,
	"date_created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"date_modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"master_club_id" INTEGER NOT NULL,
	"sport_sanctioning_id" INTEGER NOT NULL,
 PRIMARY KEY ( "master_club_sanctioning_id" )
, CONSTRAINT "unique_master_club_sanctioning_id" UNIQUE( "master_club_sanctioning_id" ) );
CREATE INDEX "index_master_club_sanctioning_id" ON "public"."master_club_sanctioning" USING btree( "master_club_sanctioning_id" );

COMMENT ON TABLE  "public"."master_club_sanctioning" IS 'List of sanctioning bodies for a club';
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CHANGE "NAME" OF "FIELD "date_created" ----------------------
ALTER TABLE "public"."master_club_sanctioning" RENAME COLUMN "date_created" TO "created";
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "date_modified" ---------------------
ALTER TABLE "public"."master_club_sanctioning" RENAME COLUMN "date_modified" TO "modified";
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CHANGE "NAME" OF "FIELD "date_created" ----------------------
ALTER TABLE "public"."event_location" RENAME COLUMN "date_created" TO "created";
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "date_created" -------------
ALTER TABLE "public"."event_location" ALTER COLUMN "created" SET DEFAULT now();
-- -------------------------------------------------------------

-- CHANGE "NAME" OF "FIELD "date_modified" ---------------------
ALTER TABLE "public"."event_location" RENAME COLUMN "date_modified" TO "modified";
-- -------------------------------------------------------------

-- CHANGE "DEFAULT VALUE" OF "FIELD "date_modified" ------------
ALTER TABLE "public"."event_location" ALTER COLUMN "modified" SET DEFAULT now();
-- -------------------------------------------------------------;

COMMIT;
