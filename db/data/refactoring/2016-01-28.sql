BEGIN;

-- CREATE FIELD "safesport_end_date" ---------------------------
ALTER TABLE "public"."webpoint_adult" ADD COLUMN "safesport_end_date" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "safesport_start_date" -------------------------
ALTER TABLE "public"."webpoint_adult" ADD COLUMN "safesport_start_date" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "safesport_statusid" ---------------------------
ALTER TABLE "public"."webpoint_adult" ADD COLUMN "safesport_statusid" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "webpoint_modified" ----------------------------
ALTER TABLE "public"."webpoint_adult" ADD COLUMN "webpoint_modified" Timestamp Without Time Zone;
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "safesport_end_date" ---------------------------
ALTER TABLE "public"."master_staff" ADD COLUMN "safesport_end_date" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "safesport_start_date" -------------------------
ALTER TABLE "public"."master_staff" ADD COLUMN "safesport_start_date" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "safesport_statusid" ---------------------------
ALTER TABLE "public"."master_staff" ADD COLUMN "safesport_statusid" Text COLLATE "pg_catalog"."default";
-- -------------------------------------------------------------

-- CREATE FIELD "webpoint_modified" ----------------------------
ALTER TABLE "public"."master_staff" ADD COLUMN "webpoint_modified" Timestamp Without Time Zone;
-- -------------------------------------------------------------

COMMIT;

