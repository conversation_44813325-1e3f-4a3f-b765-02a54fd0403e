---
--- Adding Officials table
---

BEGIN;

-- CREATE TYPE "official_rank" ---------------------------------
CREATE TYPE "public"."official_rank" AS ENUM( 'Provisional', 'Regional', 'Jr. National', 'National', 'International', 'Other' );
-- -------------------------------------------------------------

-- CREATE TABLE "official" -------------------------------------
CREATE TABLE "public"."official" (
	"official_id" Serial NOT NULL,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"user_id" INTEGER NOT NULL UNIQUE,
	"region" CHARACTER VARYING( 2 ),
	"usav_num" CHARACTER VARYING( 20 ),
	"background_screening" CHARACTER VARYING( 20 ),
	"rank" "public"."official_rank",
	"address" CHARACTER VARYING( 100 ),
	"city" CHARACTER VARYING( 100 ),
	"state" CHARACTER VARYING( 2 ),
	"zip" CHARACTER VARYING( 10 ),
	"advancement" BOOLEAN,
	"shirt_size" CHARACTER VARYING( 5 ),
	"shoe_size" CHARACTER VARYING( 5 ),
	"shirt_gender" "public"."gender",
	"shoe_gender" "public"."gender",
	"webpoint_username" CHARACTER VARYING( 100 ),
	"webpoint_password" CHARACTER VARYING( 100 ),
 PRIMARY KEY ( "official_id" )
, CONSTRAINT "unique_official_user_id" UNIQUE( "user_id" ) );
COMMENT ON TABLE  "public"."official" IS 'Officials data';
-- Set comments for fields
COMMENT ON COLUMN "public"."official"."region" IS 'Staff / Official Region';
COMMENT ON COLUMN "public"."official"."usav_num" IS 'Staff / Official USAV Number';
COMMENT ON COLUMN "public"."official"."background_screening" IS 'Staff / Official Background Screening Status';
COMMENT ON COLUMN "public"."official"."address" IS 'Staff / Official Address';
COMMENT ON COLUMN "public"."official"."city" IS 'Staff / Official City';
COMMENT ON COLUMN "public"."official"."state" IS 'Staff / Official State';
COMMENT ON COLUMN "public"."official"."zip" IS 'Staff / Official Zip';
COMMENT ON COLUMN "public"."official"."advancement" IS 'Candidate for Advancement (would you like to be evaluated at an event)';
COMMENT ON COLUMN "public"."official"."rank" IS 'Official Rank (''Provisional'', ''Regional'', ''Jr. National'', ''NATIONAL'', ''International'', ''Other'')';
-- -------------------------------------------------------------;

COMMIT;


BEGIN;


-- CREATE FIELD "is_staff" -------------------------------------
ALTER TABLE "public"."official" ADD COLUMN "is_staff" BOOLEAN;
COMMENT ON COLUMN "public"."official"."is_staff" IS 'Is Staff (Y/N)';
-- -------------------------------------------------------------

-- CREATE FIELD "is_official" ----------------------------------
ALTER TABLE "public"."official" ADD COLUMN "is_official" BOOLEAN;
COMMENT ON COLUMN "public"."official"."is_official" IS 'Is Official (Y/N)';
-- -------------------------------------------------------------;

COMMIT;

