-- Adding new fields according to <PERSON>'s email from Jan 21 2016


-- At the event level:

-- - officials_required: integer  (0, 1, 2), default 0
--   This would be the event level default of how many officials should be schedule per match, unless overridden.
--   They would leave this zero if not doing officials scheduling; use 1 for most events, and 2 for rare events like GJNC.
-- <PERSON>: we already have field "officials_required"

-- - tie_breaker_type: integer, default 0
--   <PERSON><PERSON><PERSON> would use this as the default for the event; web does not need it

ALTER TABLE public.event ADD tie_breaker_type INT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN public.event.tie_breaker_type IS 'SWB uses this as the default for the event; web does not need it';



-- At the division level:

-- - officials_required_override: integer  (0, 1, 2), default 0
--   If zero, would use the event level value.
COMMENT ON COLUMN public.division.officials_required IS 'Default number of officials required for division. If zero, would use the event level value.';
ALTER TABLE public.division RENAME COLUMN officials_required TO officials_required_override;


-- - tie_breaker_type: integer, default 0
--   <PERSON><PERSON><PERSON> would use this to override event level; web does not need it
ALTER TABLE public.division ADD tie_breaker_type INT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN public.division.tie_breaker_type IS 'SWB uses this to override event level; web does not need it';


-- At rounds level:

ALTER TABLE public.rounds RENAME COLUMN officials_required TO officials_required_override;
COMMENT ON COLUMN public.rounds.officials_required_override IS 'Default number of officials required for round. If zero, would use the division level value.';

ALTER TABLE public.rounds ADD tie_breaker_type INT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN public.rounds.tie_breaker_type IS 'SWB uses this to override division level; web does not need it';


-- At the poolbracket level:

-- - officials_required_override: integer  (0, 1, 2), default 0
--   If zero, would use the division level value.
COMMENT ON COLUMN public.poolbrackets.officials_required IS 'Default number of officials required for pool/bracket. If zero, would use the round level value.';
ALTER TABLE public.poolbrackets RENAME COLUMN officials_required TO officials_required_override;

-- - tie_breaker_type: integer, default 0
--   SWB would use this to override division level; web does not need it
ALTER TABLE public.poolbrackets ADD tie_breaker_type INT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN public.poolbrackets.tie_breaker_type IS 'SWB uses this to override round level; web does not need it';




-- At the matches level:

-- - officials_required_override: integer  (0, 1, 2), default 0
--   If zero, would use the poolbracket level value.
ALTER TABLE public.matches ADD officials_required_override INT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN public.matches.officials_required_override IS 'Number of officials required for match. If zero, would use the poolbracket level value.';

-- - officials_required: integer  (0, 1, 2), default 0
--   SWB would always populate this with the actual value to use for that match, using the various overrides.
--   This gives Eugene a single spot to look and not be concerned with the overrides.
COMMENT ON COLUMN public.matches.officials_required IS 'SWB would always populate this with the actual value to use for that match, using the various overrides.  This gives a single spot to look and not be concerned with the overrides.';

-- - tie_breaker_type: integer, default 0
--   SWB would use this to override division level; web does not need it
ALTER TABLE public.matches ADD tie_breaker_type INT DEFAULT 0 NOT NULL;
COMMENT ON COLUMN public.matches.tie_breaker_type IS 'SWB uses this to override division level; web does not need it';

-- - medal_winner: character varying; default null
-- - medal_loser: character varying; default null
--   These two fields will be used to identify if the winner and / or loser of that match gets a medal.  If not null, we will print on scoresheet so official knows to tell teams if they need to go collect their medals.
--   We may also eventually use this to add icons or flags somewhere on the website to tell the teams.  But for now just get the field there.
ALTER TABLE public.matches ADD medal_winner VARCHAR(250) NULL;
COMMENT ON COLUMN public.matches.medal_winner IS 'These two fields (medal_winner and medal_loser) will be used to identify if the winner and / or loser of that match gets a medal.  If not null, we will print on scoresheet so official knows to tell teams if they need to go collect their medals.';
ALTER TABLE public.matches ADD medal_loser VARCHAR(250) NULL;
COMMENT ON COLUMN public.matches.medal_loser IS 'These two fields (medal_winner and medal_loser) will be used to identify if the winner and / or loser of that match gets a medal.  If not null, we will print on scoresheet so official knows to tell teams if they need to go collect their medals.';


-- - secs_entered:  timestamp Without TimeZone; default null
--   This will be the value currently in sec_finished, that gets populated when scores are entered.  We still want to know exactly when the results were keyed, so we are adding this field.  But Marc also wants the official to be able to indicate when the match really got over, in case there is a time lag between when finished and entry.  Marc will go over the user interface for that later.  The problem now is that when there is a delay in entry for whatever reason, it really throws off the Pulse calculation of how far behind a court is running.
--   When the score entry UI is changed, secs_entered will get the timestamp of entry and secs_finished will default to that but be able to be changed per the UI we will talk about later.
ALTER TABLE public.matches ADD secs_entered TIMESTAMP NULL;
COMMENT ON COLUMN public.matches.secs_entered IS 'This will be the value currently in sec_finished, that gets populated when scores are entered.
We still want to know exactly when the results were keyed, so we are adding this field.
But Marc also wants the official to be able to indicate when the match really got over, in case there is a time lag between when finished and entry.
Marc will go over the user interface for that later.  The problem now is that when there is a delay in entry for whatever reason, it really throws off the Pulse calculation of how far behind a court is running.

When the score entry UI is changed, secs_entered will get the timestamp of entry and secs_finished will default to that but be able to be changed per the UI we will talk about later.
';

--  NOTE:  All these secs_.... fields should ALWAYS be given in event local time.  We NEVER want to see these in anything but event local time.

-- Then as we talked about for sync purposes, event_journal needs:

-- - skip_update:  boolean; default false
--   When true, the server journal trigger should ignore the record and just allow to pass back and forth to clients.
ALTER TABLE public.event_journal ADD skip_update BOOLEAN DEFAULT FALSE  NULL;
COMMENT ON COLUMN public.event_journal.skip_update IS 'When true, the server journal trigger should ignore the record and just allow to pass back and forth to clients.';