BEGIN;


-- DROP system_job_email_sending_id_job_type_index index -------------------------
DROP INDEX IF EXISTS public.system_job_email_sending_id_job_type_index RESTRICT;
----------------------------------------------------------------------------------


-- CREATE unique index for system_job."event_id" + system_job."recipient_type" + system_job."email_sending_id" ------
CREATE UNIQUE INDEX IF NOT EXISTS system_job_event_id_recipient_type_email_sending_id_index
  ON public.system_job (event_id, recipient_type, email_sending_id);
---------------------------------------------------------------------------------------------------------------------


COMMIT;
