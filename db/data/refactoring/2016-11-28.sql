BEGIN;

-- CREATE FIELD "deleted" --------------------------------------
ALTER TABLE "public"."email_template" ADD COLUMN "deleted" Timestamp With Time Zone;
-- -------------------------------------------------------------

COMMIT;

-- Adding column season to  table
ALTER TABLE public.event ADD season INTEGER DEFAULT NULL;
COMMENT ON COLUMN public.event.season IS 'Contains event season year';

-- Set season for 2017 events
UPDATE "event"
SET season = 2017
where long_name like '2017%';

-- Set season for 2016 events
UPDATE "event"
SET season = 2016
where long_name like '2016%';

-- Set season for 2015 events
UPDATE "event"
SET season = 2015
where long_name like '2015%';

-- Set season for old test event
UPDATE "event"
SET season = 2015
WHERE event_id = 1;

-- Checking events without season
select event_id, date_start, long_name
from "event"
where season IS NULL;

-- Set all other events to 2017
UPDATE "event"
SET season = 2017
where season IS NULL;
