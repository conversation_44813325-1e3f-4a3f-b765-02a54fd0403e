BEGIN;


-- <PERSON><PERSON><PERSON> "DEFAULT VALUE" OF "FIELD "created" ------------------
ALTER TABLE "public"."stripe_account" ALTER COLUMN "created" SET DEFAULT now();
-- -------------------------------------------------------------;

COMMIT;


BEGIN;

CREATE TRIGGER update_stripe_account_modified BEFORE UPDATE ON "public"."stripe_account" FOR EACH ROW EXECUTE PROCEDURE update_modified_column()

COMMIT;

BEGIN;


-- DROP FIELD "account_date_modified" --------------------------
ALTER TABLE "public"."stripe_account" DROP COLUMN "account_date_modified";
-- -------------------------------------------------------------;

COMMIT;

BEGIN;


-- CHANGE "NAME" OF "FIELD "moified" ---------------------------
ALTER TABLE "public"."stripe_account" RENAME COLUMN "moified" TO "modified";
-- -------------------------------------------------------------;

COMMIT;


