-- === SQL TO ESTABLISH CONNECTION TO REMOTE POSTGRESQL DB === --

/**
* https://www.postgresql.org/docs/current/static/contrib-dblink-function.html
*/
CREATE EXTENSION "dblink"

/**
* https://wiki.postgresql.org/wiki/Foreign_data_wrappers
* https://www.postgresql.org/docs/9.3/static/sql-createforeigndatawrapper.html
*/
CREATE FOREIGN DATA WRAPPER "pg_wrapper";

/**
* https://www.postgresql.org/docs/current/static/sql-createserver.html
*/
CREATE SERVER "remove_prod" FOREIGN DATA WRAPPER "pg_wrapper" 
OPTIONS (host 'host_name', dbname 'db_name', port 'port', user 'user_name', password 'pswd');

/**
* https://www.postgresql.org/docs/current/static/sql-createusermapping.html
* https://postgrespro.ru/docs/postgrespro/9.6/sql-createusermapping (RU)
* A user mapping typically encapsulates connection information that a foreign-data wrapper uses 
* together with the information encapsulated by a foreign server to access an external data resource
*/
CREATE USER MAPPING FOR PUBLIC SERVER "remove_prod";



-- === QUERY EXAMPLES === --
/**
* https://www.postgresql.org/docs/current/static/contrib-dblink-connect.html
*/
SELECT DBLINK_CONNECT('remove_dev');

SELECT * FROM DBLINK('SELECT "event_id" FROM "event" WHERE "event_id" > 17000') AS "test_event" (event_id INT);

SELECT DBLINK_DISCONNECT();