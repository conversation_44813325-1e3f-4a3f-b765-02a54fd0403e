---
--- Create table event_email
---
BEGIN;

-- CREATE TABLE "event_email" ----------------------------------
CREATE TABLE "public"."event_email" (
	"event_email_id" Serial NOT NULL UNIQUE,
	"created" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"event_id" INTEGER NOT NULL,
	"recipient_type" CHARACTER VARYING( 20 ) NOT NULL,
	"roster_club_id" INTEGER,
	"roster_team_id" INTEGER,
	"event_official_id" INTEGER,
	"email_from" CHARACTER VARYING( 200 ),
	"email_to" CHARACTER VARYING( 200 ),
	"email_subject" CHARACTER VARYING( 300 ),
	"email_bcc" CHARACTER VARYING( 200 ),
	"email_text" TEXT,
	"email_html" TEXT,
	"reason_type" CHARACTER VARYING( 50 )
, CONSTRAINT "unique_event_email_id" UNIQUE( "event_email_id" ) );
CREATE INDEX "index_event_email_event_id" ON "public"."event_email" USING btree( "event_id" );


CREATE INDEX "index_event_email_roster_team_id" ON "public"."event_email" USING btree( "roster_team_id" );


CREATE INDEX "index_event_email_event_official_id" ON "public"."event_email" USING btree( "event_official_id" );


-- Set comments for fields
COMMENT ON COLUMN "public"."event_email"."event_id" IS 'Event ID';
COMMENT ON COLUMN "public"."event_email"."recipient_type" IS 'Recipient Type: ''team'', ''official'', etc.';
COMMENT ON COLUMN "public"."event_email"."roster_team_id" IS 'Roster Team ID (if recipient is team)';
COMMENT ON COLUMN "public"."event_email"."roster_club_id" IS 'Roster Club ID (if recipient is club)';
COMMENT ON COLUMN "public"."event_email"."event_official_id" IS 'Event Official ID (if recipient is official)';
COMMENT ON COLUMN "public"."event_email"."email_from" IS 'Email From field';
COMMENT ON COLUMN "public"."event_email"."email_to" IS 'Email To field';
COMMENT ON COLUMN "public"."event_email"."email_subject" IS 'Email Subject field';
COMMENT ON COLUMN "public"."event_email"."email_bcc" IS 'Email BCC field';
COMMENT ON COLUMN "public"."event_email"."email_text" IS 'Email Body Plain Text';
COMMENT ON COLUMN "public"."event_email"."email_html" IS 'Email Body HTML';
COMMENT ON COLUMN "public"."event_email"."reason_type" IS 'Email reason type. Possible values: ''email'', ''entered'', ''accepted'', ''waitlisted'', ''declined'', etc.';
-- -------------------------------------------------------------;

COMMIT;


---
--- Adding columns to event table for Officials Acceptance email
---

BEGIN;


-- CREATE FIELD "officials_meeting_datetime" -------------------
ALTER TABLE "public"."event" ADD COLUMN "officials_meeting_datetime" CHARACTER VARYING( 100 );
COMMENT ON COLUMN "public"."event"."officials_meeting_datetime" IS 'Text for acceptance email to officials about date and time of officials meeting.';
-- -------------------------------------------------------------

-- CREATE FIELD "officials_meeting_venue" ----------------------
ALTER TABLE "public"."event" ADD COLUMN "officials_meeting_venue" CHARACTER VARYING( 100 );
COMMENT ON COLUMN "public"."event"."officials_meeting_venue" IS 'Text for acceptance email to officials about venue for officials meeting.';
-- -------------------------------------------------------------

-- CREATE FIELD "officials_additional_info" --------------------
ALTER TABLE "public"."event" ADD COLUMN "officials_additional_info" CHARACTER VARYING( 1024 );
COMMENT ON COLUMN "public"."event"."officials_additional_info" IS 'Additional Text for acceptance email to officials.';
-- -------------------------------------------------------------;

COMMIT;



---
--- Add is_local column to roster_club table
---
BEGIN;

-- CREATE FIELD "is_local" -------------------------------------
ALTER TABLE "public"."roster_club" ADD COLUMN "is_local" BOOLEAN;
COMMENT ON COLUMN "public"."roster_club"."is_local" IS 'Is Local flag for club teams';
-- -------------------------------------------------------------;

COMMIT;


---
--- Update is local flags for new teams
---
BEGIN;

UPDATE roster_club rc
SET is_local = (SELECT bool_or(rt.is_local) FROM roster_team rt WHERE rt.roster_club_id = rc.roster_club_id AND rt.deleted IS NULL GROUP BY rc.roster_club_id  );

UPDATE roster_team rt
SET is_local = (SELECT rc.is_local FROM roster_club rc WHERE rc.roster_club_id = rt.roster_club_id);

COMMIT;