BEGIN;

-- SET housing_status_changed_at FROM event_change TABLE
WITH teams AS (
  SELECT rt.roster_team_id,
         ec.created
  FROM roster_team rt
         JOIN event e
              ON e.event_id = rt.event_id
                AND e.housing_company_id <> 1
                AND e.deleted IS NULL
                AND e.season = '2019'
                AND e.is_test IS FALSE
         JOIN event_change ec
              ON ec.roster_team_id = rt.roster_team_id
              AND ec.event_change_id = (
                SELECT event_change_id
                FROM event_change
                WHERE roster_team_id = rt.roster_team_id
                AND action = 'team.housing.status-change'
                ORDER BY created DESC
                LIMIT 1
              )
  WHERE housing_status_changed_at IS NULL
  AND rt.deleted IS NULL
)
UPDATE roster_team rt
SET housing_status_changed_at = t.created
FROM teams t
WHERE rt.roster_team_id = t.roster_team_id;

COMMIT;
