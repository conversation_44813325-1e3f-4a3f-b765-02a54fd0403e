BEGIN;

-- CREATE FIELD "is_test" --------------------------------------
ALTER TABLE "public"."stripe_account" ADD COLUMN "is_test" Boolean DEFAULT false;COMMENT ON COLUMN "public"."stripe_account"."is_test" IS 'Whether the keys are for "test" mode';
-- -------------------------------------------------------------

-- CREATE FIELD "event_owner_id" -------------------------------
ALTER TABLE "public"."stripe_account" ADD COLUMN "event_owner_id" Integer;COMMENT ON COLUMN "public"."stripe_account"."event_owner_id" IS 'The "event_owner" who ownes this Stripe Acc.';
-- -------------------------------------------------------------

COMMIT;


BEGIN;

-- <PERSON><PERSON><PERSON> "FIELDS" OF "UNIQUE "unique_stripe_account_id" -------
ALTER TABLE "public"."stripe_account" DROP CONSTRAINT IF EXISTS "unique_stripe_account_id";
ALTER TABLE "public"."stripe_account" DROP CONSTRAINT IF EXISTS "unique_id";
ALTER TABLE "public"."stripe_account" DROP CONSTRAINT IF EXISTS "unique_row_id";
ALTER TABLE "public"."stripe_account" ADD CONSTRAINT "unique_row_id" UNIQUE( "id", "is_test" );
-- -------------------------------------------------------------

-- CHANGE "FIELDS" OF "UNIQUE "unique_stripe_account_id1" ------
ALTER TABLE "public"."stripe_account" DROP CONSTRAINT IF EXISTS "unique_stripe_account_id1";
ALTER TABLE "public"."stripe_account" DROP CONSTRAINT IF EXISTS "unique_stripe_account_id";
ALTER TABLE "public"."stripe_account" ADD CONSTRAINT "unique_stripe_account_id" UNIQUE( "stripe_account_id", "is_test" );
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- SET "is_test" TRUE FOR test Stripe Keys
UPDATE "stripe_account" "sa" 
SET "is_test" = TRUE
WHERE "sa"."secret_key" ILIKE '%_test_%'
RETURNING *

COMMIT;

-- SET "event_owner_id" 
BEGIN;

UPDATE "stripe_account" "sa"
SET "event_owner_id" = "d"."event_owner_id"
FROM (
	SELECT 
		"d"."secret_key", STRING_AGG("d"."event_owner_id"::TEXT, '')::INTEGER "event_owner_id"
	FROM (
		SELECT DISTINCT "sa"."secret_key", e."event_owner_id"
		FROM "stripe_account" "sa"
		LEFT JOIN "event" e 
		    ON (
		        e.stripe_teams_private_key = sa."secret_key"
		        OR
		        e."stripe_tickets_private_key" = sa."secret_key"
		    )
		WHERE "sa"."secret_key" NOT IN (
	        SELECT "value"->>'secret_key' "secret_key" 
	        FROM "settings" 
	        WHERE "key" = 'stripe_connect'
		)
		AND "sa"."is_test" IS NOT TRUE
		AND "sa"."event_owner_id" IS NULL
	) "d"
	GROUP BY "d"."secret_key"
	HAVING COUNT (d."event_owner_id") = 1 /* ONLY FOR PERSONAL STRIPE ACCOUNTS*/
) "d"
WHERE "sa"."secret_key" = "d"."secret_key"
RETURNING *;

COMMIT;