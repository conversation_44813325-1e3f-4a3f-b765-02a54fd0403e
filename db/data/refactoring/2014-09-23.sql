
--
-- Adding uniqueness index for clubs: clubs should be unique by code and region
--

BEGIN;


-- CREATE UNIQUE "club_code+region" ----------------------------
ALTER TABLE "public"."master_club" ADD CONSTRAINT "club_code+region" UNIQUE( "code","region" );
-- -------------------------------------------------------------;

COMMIT;


---
--- Adding housing fields for THS API
---

BEGIN;


-- CREATE FIELD "tentative_nights" -----------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "tentative_nights" INTEGER;COMMENT ON COLUMN "public"."roster_team"."tentative_nights" IS 'THS Housing: Tentative Nights';
-- -------------------------------------------------------------

-- CREATE FIELD "confirmed_nights" -----------------------------
ALTER TABLE "public"."roster_team" ADD COLUMN "confirmed_nights" INTEGER;COMMENT ON COLUMN "public"."roster_team"."confirmed_nights" IS 'THS Housing: Confirmed Nights';
-- -------------------------------------------------------------;

COMMIT;
