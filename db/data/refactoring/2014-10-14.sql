---
--- Creating purchase_booth table
---

BEGIN;


-- CREATE TABLE "purchase_booth" -------------------------------
CREATE TABLE "public"."purchase_booth" (
	"purchase_booth_id" Serial NOT NULL UNIQUE,
	"created" TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"modified" TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
	"purchase_id" INTEGER NOT NULL,
	"event_booth_id" INTEGER,
	"fee" NUMERIC( 8, 2 ) NOT NULL,
	"quantity" CHARACTER VARYING( 2044 ) NOT NULL,
	"amount" NUMERIC( 8, 2 ) NOT NULL,
	"title" CHARACTER VARYING( 100 ) NOT NULL
, CONSTRAINT "unique_purchase_booth_id" UNIQUE( "purchase_booth_id" ) );
-- Set comments for fields
COMMENT ON COLUMN "public"."purchase_booth"."title" IS 'Custom Booth title';
-- -------------------------------------------------------------;

COMMIT;
