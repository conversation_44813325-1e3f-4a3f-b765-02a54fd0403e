BEGIN;

-- CREATE TABLE "email_template_type" --------------------------
CREATE TABLE "public"."email_template_type" ( 
	"type" Text NOT NULL,
	"group" Text NOT NULL,
	"title" Text NOT NULL,
	CONSTRAINT "unique_type" UNIQUE( "type" ) );
 
CREATE INDEX "index_group" ON "public"."email_template_type" USING btree( "group" );


CREATE INDEX "index_title" ON "public"."email_template_type" USING btree( "title" );
-- -------------------------------------------------------------

COMMIT;

-- CREATE TYPE "email_template_type_group" ---------------------
CREATE TYPE "public"."email_template_type_group" AS Enum( 'notification', 'manual_mailing' );

COMMENT ON TYPE  "public"."email_template_type_group" IS 'Group of email type: 
- notification: automatic notifications of SW
- manual_mailing: group for templates that are used for sending manually by a person';
-- -------------------------------------------------------------

BEGIN;

-- CHANGE "TYPE" OF "FIELD "group" -----------------------------
ALTER TABLE "public"."email_template_type" ALTER COLUMN "group" TYPE "public"."email_template_type_group" USING ("group"::"public"."email_template_type_group");
-- -------------------------------------------------------------

COMMIT;

BEGIN;

-- CREATE FIELD "email_template_type" --------------------------
ALTER TABLE "public"."email_template" ADD COLUMN "email_template_type" Text;COMMENT ON COLUMN "public"."email_template"."email_template_type" IS 'The type (usage purpose) of the email template';
-- -------------------------------------------------------------

-- CREATE INDEX "index_email_template_type" --------------------
CREATE INDEX "index_email_template_type" ON "public"."email_template" USING btree( "email_template_type" );
-- -------------------------------------------------------------

COMMIT;


BEGIN;

INSERT INTO "email_template_type" ("type", "group", "title") VALUES ('official', 'manual_mailing', 'Official');
INSERT INTO "email_template_type" ("type", "group", "title") VALUES ('club_director', 'manual_mailing', 'Club Director');
INSERT INTO "email_template_type" ("type", "group", "title") VALUES ('tickets_customer', 'manual_mailing', 'Tickets Customer');

COMMIT;

BEGIN;

-- CREATE FIELD "is_valid" -------------------------------------
ALTER TABLE "public"."email_template" ADD COLUMN "is_valid" BOOLEAN;COMMENT ON COLUMN "public"."email_template"."is_valid" IS 'Whether the template row can be used for email sending.
For the first step of template creation, we set this field''s value to FALSE. If the second step finished successfully, we update this field to TRUE';
-- -------------------------------------------------------------

COMMIT;


