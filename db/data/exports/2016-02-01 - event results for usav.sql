-- Date	Event	Division	Gender	Age	Team A	Team A Name	Team B	Team B Name	Winning Team	Score 1 (Team A vs Team B)	Score 2	Score 3	Score 4	Score 5
-- This is sql to export LBS 2016 results (event_id = 39)

select
  to_char(m.secs_start, 'dd/MM/YYYY') match_date, e.name event_name, d.name division_name,
  d.gender, d.max_age age,
  rt1.organization_code team_a_code, rt1.team_name team_a_name,
  rt2.organization_code team_b_code, rt2.team_name team_b_name,
  CASE WHEN m.results::JSON->>'winner' = '1' THEN rt1.organization_code
       WHEN m.results::JSON->>'winner' = '2' THEN rt2.organization_code
       ELSE 'Unknown'
  END winning_team,
  m.results::JSON->>'set1' score_1,
  m.results::JSON->>'set2' score_2,
  m.results::JSON->>'set3' score_3,
  m.results::JSON->>'set4' score_4,
  m.results::JSON->>'set5' score_5
from matches m
  INNER JOIN event e ON e.event_id = m.event_id
  INNER JOIN division d ON d.division_id = m.division_id
  INNER JOIN roster_team rt1 ON m.team1_roster_id = rt1.roster_team_id
  INNER JOIN roster_team rt2 ON m.team2_roster_id = rt2.roster_team_id
WHERE m.event_id = 39
and rt1.status_entry = 12
and rt2.status_entry = 12