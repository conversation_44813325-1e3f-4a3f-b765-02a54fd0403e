CREATE SEQUENCE "public"."email_queue_id_seq"
INCREMENT 1
MINVALUE 0
MAXVALUE 2147483647
START 0
CACHE 1
;

CREATE TABLE "public"."email_queue" (
	"id" INTEGER DEFAULT nextval('email_queue_id_seq'::regclass) NOT NULL UNIQUE,
	"date_created" TIMESTAMP WITHOUT TIME ZONE DEFAULT transaction_timestamp() NOT NULL,
	"from" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default" NOT NULL,
	"to" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default" NOT NULL,
	"cc" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default",
	"bcc" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default",
	"replyto" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default",
	"subject" CHARACTER VARYING( 255 ) COLLATE "pg_catalog"."default" NOT NULL,
	"body_html" TEXT COLLATE "pg_catalog"."default",
	"body_text" TEXT COLLATE "pg_catalog"."default"
, CONSTRAINT "unique_email_queue_id" UNIQUE( "id" ) );
CREATE INDEX "index_email_queue_id" ON "public"."email_queue" USING btree( "id" ASC NULLS LAST );