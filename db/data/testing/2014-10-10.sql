---
--- Testing for team code duplicates
---
SELECT mt.organization_code, COUNT(mt.master_team_id) cnt, mc.club_name, mc.master_club_id, u.email
FROM master_team mt
LEFT JOIN master_club mc ON mc.master_club_id = mt.master_club_id
LEFT JOIN club_owner co ON co.master_club_id = mc.master_club_id
LEFT JOIN "user" u ON u.user_id = co.user_id
WHERE mt.deleted = FALSE
GROUP BY mt.organization_code, mc.club_name, mc.master_club_id, u.email
HAVING COUNT(mt.master_team_id) > 1;


---
--- Roster Team Code is not equal to master
---
SELECT rt.organization_code, mt.organization_code, rt.team_name, mt.team_name, mt.master_team_id
FROM roster_team rt
LEFT JOIN master_team mt ON mt.master_team_id = rt.master_team_id
WHERE mt.deleted = FALSE
AND (rt.organization_code <> mt.organization_code OR rt.organization_code IS NULL)
;

UPDATE roster_team _rt
SET organization_code = mt.organization_code
FROM roster_team rt
LEFT JOIN master_team mt ON mt.master_team_id = rt.master_team_id
WHERE mt.deleted = FALSE
AND (rt.organization_code <> mt.organization_code OR rt.organization_code IS NULL)
AND _rt.roster_team_id = rt.roster_team_id
;
