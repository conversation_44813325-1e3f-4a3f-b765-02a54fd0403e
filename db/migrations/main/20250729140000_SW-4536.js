/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add new event settings sub-permissions
        INSERT INTO event_operation (event_operation, parent_event_operation, title) 
        VALUES ('edit_event_general', 'edit_event', 'General'),
               ('edit_event_location', 'edit_event', 'Location'),
               ('edit_event_users', 'edit_event', 'Event Users'),
               ('edit_event_transactional_emails', 'edit_event', 'Transactional Emails')
        ON CONFLICT (event_operation) DO NOTHING;       

        -- Grant all existing Co-EOs with edit_event permission the new sub-permissions
        INSERT INTO event_user_permission (event_operation_id, event_id, user_id, granter_user_id)
        SELECT sub_permissions.event_operation_id, eup.event_id, eup.user_id, eup.granter_user_id
        FROM event_user_permission eup
        CROSS JOIN (VALUES 
            ('edit_event_general'), 
            ('edit_event_location'), 
            ('edit_event_users'), 
            ('edit_event_transactional_emails')
        ) sub_permissions(event_operation_id)
        WHERE eup.event_operation_id = 'edit_event' 
          AND eup.deleted IS NULL
        ON CONFLICT (event_operation_id, event_id, user_id) DO NOTHING;
    `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        -- Remove permissions for the sub-permissions
        DELETE FROM event_user_permission 
        WHERE event_operation_id IN (
            'edit_event_general', 
            'edit_event_location', 
            'edit_event_users', 
            'edit_event_transactional_emails'
        );
        
        -- Remove the sub-permissions from event_operation table
        DELETE FROM event_operation 
        WHERE event_operation IN (
            'edit_event_general', 
            'edit_event_location', 
            'edit_event_users', 
            'edit_event_transactional_emails'
        );
    `);
};
