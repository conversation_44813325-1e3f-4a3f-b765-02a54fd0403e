
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."user" ADD COLUMN IF NOT EXISTS "recognition_image" text default null;
        COMMENT ON COLUMN "public"."user"."recognition_image" IS 'Link to user recognition image';
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."user" DROP COLUMN IF EXISTS "recognition_image";
    `)
};
