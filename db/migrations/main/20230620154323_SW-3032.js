exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."purchase" ADD COLUMN IF NOT EXISTS "payment_hub_order_uuid" TEXT DEFAULT NULL;
        COMMENT ON COLUMN "public"."purchase"."payment_hub_order_uuid" IS 'Purchase and Payment hub order relation';

        ALTER TABLE "public"."purchase_history" ADD COLUMN IF NOT EXISTS "payment_hub_order_uuid" TEXT DEFAULT NULL;
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "public"."purchase" DROP COLUMN IF EXISTS "payment_hub_order_uuid";
        ALTER TABLE "public"."purchase_history" DROP COLUMN IF EXISTS "payment_hub_order_uuid";
    `)
};