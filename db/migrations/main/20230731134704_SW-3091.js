
exports.up = function(knex) {
    return knex.schema.raw(`
        -- -------------- Create custom_form_field_type table --------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."custom_form_field_type"
        (
            custom_form_field_type_id INT GENERATED BY DEFAULT AS IDENTITY,
            created                   TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified                  TIMESTAMP WITHOUT TIME ZONE,
            type                      TEXT                                      NOT NULL UNIQUE,
            description               TEXT                        DEFAULT NULL
        );
        COMMENT ON COLUMN "public"."custom_form_field_type"."type" IS 'Custom field type: select, text, etc.';
        
        INSERT INTO "public"."custom_form_field_type" (type, description)
        VALUES ('select', 'Select field type'),
               ('text', 'Text field type'),
               ('multiselect', 'Multi Select field type'),
               ('checkbox', 'Checkbox field type');
        -- ------------------------------------------------------------------------------------------------------
        
        
        -- --------------- Create new ENUM custom_form_type for custom_form_event.type field --------------------
        CREATE TYPE "public"."custom_form_type" AS ENUM ('so_cal_cup_form');
        -- ------------------------------------------------------------------------------------------------------
        
        
        -- -------------- Create custom_form_event table --------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."custom_form_event"
        (
            custom_form_event_id INT GENERATED BY DEFAULT AS IDENTITY,
            created              TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified             TIMESTAMP WITHOUT TIME ZONE,
            type                 custom_form_type                          NOT NULL,
            event_id             INT                                       NOT NULL,
            header_text          TEXT                                      NOT NULL,
            form_text            TEXT                        DEFAULT NULL,
            UNIQUE (type, event_id)
        );
        
        COMMENT ON COLUMN "public"."custom_form_event"."type" IS 'Custom form type: so_cal_cup_form, etc.';
        COMMENT ON COLUMN "public"."custom_form_event"."event_id" IS 'Event for which form created.';
        -- ------------------------------------------------------------------------------------------------------
        
        
        -- -------------- Create custom_form_field table --------------------------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."custom_form_field"
        (
            custom_form_field_id      INT GENERATED BY DEFAULT AS IDENTITY,
            created                   TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified                  TIMESTAMP WITHOUT TIME ZONE,
            custom_form_event_id      INT                                       NOT NULL,
            custom_form_field_type_id INT                                       NOT NULL,
            label                     TEXT                                      NOT NULL,
            default_value             TEXT                        DEFAULT NULL,
            options                   JSONB                       DEFAULT '{}'::JSONB,
            is_required               BOOLEAN                     DEFAULT FALSE,
            sort_order                INT                         DEFAULT 0  
        );
        CREATE INDEX "custom_form_field_custom_form_event_id_index"
            ON "public"."custom_form_field" USING BTREE (custom_form_event_id);
        COMMENT ON COLUMN "public"."custom_form_field"."custom_form_event_id" IS 'Connection with event form entity';
        COMMENT ON COLUMN "public"."custom_form_field"."custom_form_field_type_id" IS 'Connection with field type entity';
        -- ------------------------------------------------------------------------------------------------------
        
        
        -- --------------- Create new ENUM custom_form_submitter_type for submitter_type.type field -------------
        CREATE TYPE "public"."custom_form_submitter_type" AS ENUM ('roster_club');
        -- ------------------------------------------------------------------------------------------------------
        
        
        -- -------------- Create custom_form_submitted_field_value table ----------------------------------------
        CREATE TABLE IF NOT EXISTS "public"."custom_form_submitted_field_value"
        (
            custom_form_submitted_field_value_id INT GENERATED BY DEFAULT AS IDENTITY,
            created                              TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
            modified                             TIMESTAMP WITHOUT TIME ZONE,
            custom_form_event_id                 INT                                       NOT NULL,
            custom_form_field_id                 INT                                       NOT NULL,
            value                                TEXT                        DEFAULT NULL,
            submitter_type                       custom_form_submitter_type                NOT NULL,
            submitter_id                         INT                                       NOT NULL,
            UNIQUE (custom_form_event_id, custom_form_field_id, submitter_id)
        );
        
        COMMENT ON COLUMN "public"."custom_form_submitted_field_value"."custom_form_event_id"
            IS 'Connection with event form entity';
        COMMENT ON COLUMN "public"."custom_form_submitted_field_value"."custom_form_field_id" IS 'Connection with field entity';
        COMMENT ON COLUMN "public"."custom_form_submitted_field_value"."value" IS 'Submitted form field value.';
        COMMENT ON COLUMN "public"."custom_form_submitted_field_value"."submitter_type" IS 'Submitter type: roster_club, etc.';
        COMMENT ON COLUMN "public"."custom_form_submitted_field_value"."submitter_id"
            IS 'Submitter entity ID. For roster_club submitter_type it is roster_club_id';
        -- ------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."custom_form_field_type";
        DROP TABLE IF EXISTS "public"."custom_form_field";
        DROP TABLE IF EXISTS "public"."custom_form_event";
        DROP TABLE IF EXISTS "public"."custom_form_submitted_field_value";
        DROP TYPE IF EXISTS "public"."custom_form_type";
        DROP TYPE IF EXISTS "public"."custom_form_submitter_type";
        DROP INDEX IF EXISTS "public"."custom_form_field_custom_form_event_id_index";
    `)
};
