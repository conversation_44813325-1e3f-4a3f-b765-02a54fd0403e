
exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "ncsa_event" -----------------------------------------------------------------------------------
        CREATE TABLE "public"."ncsa_event"
        (
            "ncsa_event_id" INT,
            "created"       TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            "modified"      TIMESTAMP WITH TIME ZONE DEFAULT NULL,
            "event_id"      INTEGER UNIQUE
        );
        
        COMMENT ON COLUMN "public"."ncsa_event"."ncsa_event_id" IS 'NCSA Internal Event ID';
        COMMENT ON COLUMN "public"."ncsa_event"."event_id" IS 'SW Internal Event ID (event table)';
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add indexes to "ncsa_event" ---------------------------------------------------------------------------------
        CREATE INDEX "ncsa_event_ncsa_event_id_event_id_index" ON "public"."ncsa_event" ("ncsa_event_id", "event_id");
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add modified trigger to ncsa_event table --------------------------------------------------------------------
        CREATE TRIGGER "update_ncsa_event_modified"
            BEFORE UPDATE
            ON "public"."ncsa_event"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add values to ncsa_event table ------------------------------------------------------------------------------
        INSERT INTO "public"."ncsa_event"
            ("ncsa_event_id", "event_id")
        VALUES (24361, 21159),
               (24362, 21083),
               (24357, 21016),
               (24358, 21034),
               (14900, 0);
        -- -------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
          DROP TABLE IF EXISTS "public"."ncsa_event";
    `)
};
