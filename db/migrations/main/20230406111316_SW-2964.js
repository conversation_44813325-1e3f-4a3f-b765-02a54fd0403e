exports.up = function(knex) {
    return knex.raw(`
        -- CREATE FIELD "deleted_at" -------------------------
        ALTER TABLE "public"."user" ADD COLUMN "deleted_at" TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL;

        -- DROP UNIQUE EMAIL CONSTRAINT ----------------------
        ALTER TABLE "public"."user" DROP CONSTRAINT IF EXISTS "unique_user_email";
        DROP INDEX IF EXISTS "user_l_tr_email_uindex";

        -- CREATE UNIQUE email AND deleted_at CONSTRAINT -----
        CREATE UNIQUE INDEX "unique_active_user_by_email"
            ON "public"."user" (LOWER(TRIM(email))) WHERE deleted_at IS NULL;
    `)
};

exports.down = function(knex) {
    return knex.raw(`
        -- DROP FIELD "deleted_at" -------------------------
        ALTER TABLE "public"."user" DROP COLUMN "deleted_at";

        -- CREATE UNIQUE EMAIL CONSTRAINT ------------------
        ALTER TABLE "public"."user" ADD CONSTRAINT "unique_user_email" UNIQUE( "email" );
        CREATE UNIQUE INDEX "user_l_tr_email_uindex" ON public.user (LOWER(TRIM(email)));

        -- DROP UNIQUE email AND deleted_at CONSTRAINT -----
        DROP INDEX IF EXISTS "unique_active_user_by_email";
    `)
};
