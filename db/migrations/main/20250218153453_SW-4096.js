
exports.up = function(knex) {
    return knex.schema.raw(`    
        CREATE TABLE "public"."event_exhibitor_invoice" (
            "event_exhibitor_invoice_id"    INT GENERATED ALWAYS AS IDENTITY,
            "created"                       TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
            "modified"                      TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE DEFAULT NOW(),
            "event_exhibitor_id"            INT NOT NULL,
            "purchase_id"                   INT NOT NULL,
            "event_dates"                   JSONB DEFAULT '{}'::JSONB,
            "booths"                        INTEGER[] DEFAULT '{}'::INTEGER[],
            "comment"                       TEXT,
            PRIMARY KEY("event_exhibitor_invoice_id")
        );
        
        CREATE TRIGGER update_event_exhibitor_invoice_modified 
        BEFORE UPDATE ON "public"."event_exhibitor_invoice" 
        FOR EACH ROW EXECUTE PROCEDURE update_modified_column();

        COMMENT ON COLUMN "public"."event_exhibitor_invoice"."event_exhibitor_id" IS 'Identifier of applied Exhibitor''s Event';
        COMMENT ON COLUMN "public"."event_exhibitor_invoice"."purchase_id" IS 'Invoice identifier';
        COMMENT ON COLUMN "public"."event_exhibitor_invoice"."event_dates" IS 'Days that an Exhibitor wants to be at the event';
        COMMENT ON COLUMN "public"."event_exhibitor_invoice"."booths" IS 'Booths that an Exhibitor wants to use.';
        COMMENT ON COLUMN "public"."event_exhibitor_invoice"."comment" IS 'Comments';
  `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."event_exhibitor_invoice";
  `)
};
