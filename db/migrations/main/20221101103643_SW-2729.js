exports.up = function(knex) {
    return knex.raw(`
        ALTER TYPE public.gender ADD VALUE 'unspecified' AFTER 'female';
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        --- ALTER TABLE COLUMN TO TEXT ---
        ALTER TABLE public."user" ALTER "gender" TYPE TEXT;
        ALTER TABLE public."master_athlete" ALTER "gender" TYPE TEXT;
        ALTER TABLE public."master_club" ALTER "director_gender" TYPE TEXT;
        ALTER TABLE public."master_staff" ALTER "gender" TYPE TEXT;
        ALTER TABLE public."ncsa_sport" ALTER "gender" TYPE TEXT;
        ALTER TABLE public."webpoint_adult" ALTER "gender" TYPE TEXT;
        ALTER TABLE public."webpoint_parse" ALTER "gender" TYPE TEXT;
        
        --- RECREATE ENUM TYPE ---
        DROP TYPE public.gender;
        CREATE TYPE public.gender AS ENUM('male', 'female');

        --- ALTER TABLE COLUMN TO enum ---
        ALTER TABLE public."user" ALTER gender TYPE gender USING gender::gender;
        ALTER TABLE public."master_athlete" ALTER gender TYPE gender USING gender::gender;
        ALTER TABLE public."master_club" ALTER director_gender TYPE gender USING director_gender::gender;
        ALTER TABLE public."master_staff" ALTER gender TYPE gender USING gender::gender;
        ALTER TABLE public."ncsa_sport" ALTER gender TYPE gender USING gender::gender;
        ALTER TABLE public."webpoint_adult" ALTER gender TYPE gender USING gender::gender;
        ALTER TABLE public."webpoint_parse" ALTER gender TYPE gender USING gender::gender;
    `);
};