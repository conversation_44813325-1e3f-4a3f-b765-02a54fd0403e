
exports.up = function(knex) {
  return knex.raw(`
    DROP TRIGGER IF EXISTS update_roster_team_and_roster_club_modified ON public.roster_team;
    DROP FUNCTION IF EXISTS public.update_roster_team_and_roster_club_modified_columns();
  `)
};

exports.down = function(knex) {
  return knex.raw(`
    CREATE OR REPLACE FUNCTION public.update_roster_team_and_roster_club_modified_columns()
        RETURNS trigger
        LANGUAGE plpgsql
    AS
    $function$
    BEGIN
        NEW.modified = NOW();
        EXECUTE 'UPDATE "public"."roster_club" SET modified = now() WHERE roster_club_id = $1' USING OLD."roster_club_id";
        RETURN NEW;
    END
    $function$;
    
    CREATE TRIGGER update_roster_team_and_roster_club_modified
        BEFORE UPDATE
        ON public.roster_team
        FOR EACH ROW
    EXECUTE PROCEDURE
        public.update_roster_team_and_roster_club_modified_columns();
  `)
};
