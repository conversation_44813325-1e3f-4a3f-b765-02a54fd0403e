exports.up = function (knex) {
    return knex.schema.raw(`
        update email_template_group set variables = '[
            {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
            },
            {
            "field": "event_short_name",
            "title": "Event Short Name",
            "pattern": "{event_short_name}",
            "is_available_for_subject": true
            },
            {
            "field": "event_city",
            "title": "Event City",
            "pattern": "{event_city}"
            },
            {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
            },
            {
            "field": "event_email",
            "title": "Event Email",
            "pattern": "{event_email}"
            },
            {
            "field": "event_month",
            "title": "Event Month",
            "pattern": "{event_month}"
            },
            {
            "field": "club_name",
            "title": "Club Name",
            "pattern": "{club_name}",
            "is_available_for_subject": true
            },
            {
            "field": "receiver_first",
            "title": "Receiver First Name",
            "pattern": "{receiver_first}",
            "is_available_for_subject": true
            },
            {
            "field": "receiver_last",
            "title": "Receiver Last Name",
            "pattern": "{receiver_last}",
            "is_available_for_subject": true
            },
            {
            "field": "receiver_name",
            "title": "Receiver First and Last Name",
            "pattern": "{receiver_name}",
            "is_available_for_subject": true
            },
            {
            "field": "roster_teams",
            "title": "Team Names (separated by comma)",
            "pattern": "{teams}"
            },
            {
            "field": "roster_teams_ul",
            "title": "Team Names Ul",
            "pattern": "{teams_ul}",
            "custom_action": true
            },
            {
            "field": "teams_divs",
            "title": "Team Names with divisions (separated by comma)",
            "pattern": "{teams_divs}",
            "custom_action": true
            },
            {
            "field": "social_icons",
            "title": "Social Icons",
            "pattern": "{social_icons}",
            "custom_action": true
            },
            {
            "field": "facebook_icon",
            "title": "Facebook Icon",
            "pattern": "{facebook_icon}",
            "custom_action": true
            },
            {
            "field": "twitter_icon",
            "title": "Twitter Icon",
            "pattern": "{twitter_icon}",
            "custom_action": true
            },
            {
            "field": "instagram_icon",
            "title": "Instagram Icon",
            "pattern": "{instagram_icon}",
            "custom_action": true
            },
            {
            "field": "snapchat_icon",
            "title": "Snapchat Icon",
            "pattern": "{snapchat_icon}",
            "custom_action": true
            },
            {
            "field": "director_first",
            "title": "Director First Name",
            "pattern": "{director_first}",
            "is_hidden": true,
            "is_available_for_subject": true
            },
            {
            "field": "director_last",
            "title": "Director Last Name",
            "pattern": "{director_last}",
            "is_hidden": true,
            "is_available_for_subject": true
            },
            {
            "field": "director_name",
            "title": "Director First and Last Name",
            "pattern": "{director_name}",
            "is_hidden": true,
            "is_available_for_subject": true
            },
            {
            "field": "qr_code_image",
            "title": "Barcode to checkin",
            "pattern": "{qr_code_image}",
            "custom_action": true
            },
            {
            "field": "description_link",
            "title": "Description link",
            "pattern": "{description_link}",
            "custom_action": true
            }
        ]'
        where "group" = 'clubs'
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        update email_template_group set variables = '[
            {
            "field": "event_name",
            "title": "Event Name",
            "pattern": "{event_name}",
            "is_available_for_subject": true
            },
            {
            "field": "event_short_name",
            "title": "Event Short Name",
            "pattern": "{event_short_name}",
            "is_available_for_subject": true
            },
            {
            "field": "event_website",
            "title": "Event Website",
            "pattern": "{event_website}"
            },
            {
            "field": "receiver_first",
            "title": "Receiver First Name",
            "pattern": "{receiver_first}",
            "is_available_for_subject": true
            },
            {
            "field": "receiver_last",
            "title": "Receiver Last Name",
            "pattern": "{receiver_last}",
            "is_available_for_subject": true
            },
            {
            "field": "receiver_name",
            "title": "Receiver First and Last Name",
            "pattern": "{receiver_name}",
            "is_available_for_subject": true
            },
            {
            "field": "roster_teams",
            "title": "Team Names (separated by comma)",
            "pattern": "{teams}",
            "custom_action": true
            },
            {
            "field": "team_name",
            "title": "Team Name",
            "pattern": "{team_name}",
            "custom_action": true
            },
            {
            "field": "social_icons",
            "title": "Social Icons",
            "pattern": "{social_icons}",
            "custom_action": true
            },
            {
            "field": "facebook_icon",
            "title": "Facebook Icon",
            "pattern": "{facebook_icon}",
            "custom_action": true
            },
            {
            "field": "twitter_icon",
            "title": "Twitter Icon",
            "pattern": "{twitter_icon}",
            "custom_action": true
            },
            {
            "field": "instagram_icon",
            "title": "Instagram Icon",
            "pattern": "{instagram_icon}",
            "custom_action": true
            },
            {
            "field": "snapchat_icon",
            "title": "Snapchat Icon",
            "pattern": "{snapchat_icon}",
            "custom_action": true
            },
            {
            "field": "qr_code_image",
            "title": "Barcode to checkin",
            "pattern": "{qr_code_image}",
            "custom_action": true
            },
            {
            "field": "description_link",
            "title": "Description link",
            "pattern": "{description_link}",
            "custom_action": true
            }
        ]'
        where "group" = 'clubs'
    `);
};
