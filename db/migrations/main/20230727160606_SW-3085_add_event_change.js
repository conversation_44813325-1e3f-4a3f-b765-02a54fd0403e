exports.up = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."event_change" ADD COLUMN IF NOT EXISTS "old_data" JSONB DEFAULT NULL;
        COMMENT ON COLUMN "public"."event_change"."old_data"
            IS 'Data before change';
        ALTER TABLE "public"."event_change" ADD COLUMN IF NOT EXISTS "new_data" JSONB DEFAULT NULL;
        COMMENT ON COLUMN "public"."event_change"."new_data"
            IS 'Data after change';
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        ALTER TABLE "public"."event_change" DROP COLUMN "new_data";
        ALTER TABLE "public"."event_change" DROP COLUMN "old_data";
    `);
};
