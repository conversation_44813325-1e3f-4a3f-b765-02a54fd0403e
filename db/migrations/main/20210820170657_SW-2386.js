
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add new column official_additional_payment_category.show_for_officials --------------------------------------
        ALTER table "public"."official_additional_payment_category" 
                ADD COLUMN "show_for_officials" BOOLEAN NOT NULL DEFAULT FALSE;
        -- -------------------------------------------------------------------------------------------------------------        
                
        -- Add new column official_additional_payment_category.show_for_staff ------------------------------------------       
        ALTER table "public"."official_additional_payment_category" 
                ADD COLUMN "show_for_staff" BOOLEAN NOT NULL DEFAULT FALSE;
        -- -------------------------------------------------------------------------------------------------------------
          
        -- Do not allow to be show_for_officials and show_for_staff both true for the category -------------------------        
        ALTER TABLE "public"."official_additional_payment_category"
            ADD CONSTRAINT ck_show_for_staff_and_show_for_officials_not_true
            CHECK (show_for_officials <> show_for_staff);
        -- -------------------------------------------------------------------------------------------------------------    
        
        -- Set show_for_officials = TRUE for all existed categories ----------------------------------------------------         
        UPDATE "public"."official_additional_payment_category" SET "show_for_officials" = TRUE;
        -- -------------------------------------------------------------------------------------------------------------    

        -- Insert categories for staffers ------------------------------------------------------------------------------        
        INSERT INTO "public"."official_additional_payment_category" (category, event_id, show_for_staff)
        SELECT "ins"."category", "ins"."event_id", true
        FROM "public"."official_additional_payment_category" "ins"; 
        -- -------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM "public"."official_additional_payment_category" WHERE "show_for_staff" IS TRUE;

        ALTER table "public"."official_additional_payment_category" 
                DROP COLUMN IF EXISTS "show_for_officials";
                
        ALTER table "public"."official_additional_payment_category" 
                DROP COLUMN IF EXISTS "show_for_staff";
    `)
};
