
exports.up = function(knex) {
    return knex.schema.raw(`
        -- Remove NULLs in email_unsubscribe_list.event_id ----------------------------------------------
        UPDATE email_unsubscribe_list SET event_id = 0 WHERE event_id IS NULL;
        -- ----------------------------------------------------------------------------------------------
        
        -- Set email_unsubscribe_list.event_id NOT NULL -------------------------------------------------
        ALTER TABLE email_unsubscribe_list ALTER COLUMN event_id SET NOT NULL;
        -- ----------------------------------------------------------------------------------------------

        -- Change event UNIQUE to event + event_id UNIQUE -----------------------------------------------
        DROP INDEX IF EXISTS email_unsubscribe_list_email_uindex;

        CREATE UNIQUE INDEX IF NOT EXISTS email_unsubscribe_list_email_event_id_uindex
            ON email_unsubscribe_list (LOWER(TRIM(email::TEXT)), event_id);
        -- ----------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
         ALTER TABLE email_unsubscribe_list ALTER COLUMN event_id DROP NOT NULL;
         UPDATE email_unsubscribe_list SET event_id = NULL WHERE event_id = 0;
         DROP INDEX IF EXISTS email_unsubscribe_list_email_event_id_uindex;
         CREATE UNIQUE INDEX IF NOT EXISTS email_unsubscribe_list_email_uindex
            ON public.email_unsubscribe_list (LOWER(TRIM("email")));
    `);
};
