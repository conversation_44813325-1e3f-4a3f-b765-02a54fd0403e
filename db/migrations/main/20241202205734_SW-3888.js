/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        create index if not exists event_user_permission_event_id_user_id_index
            on event_user_permission (event_id, user_id);

        create index if not exists stripe_payment_intent_payment_intent_id_index
            on stripe_payment_intent (payment_intent_id);

        create index if not exists event_change_system_job_id_index
            on event_change (system_job_id);
    `)
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        drop index if exists event_user_permission_event_id_user_id_index;

        drop index if exists stripe_payment_intent_payment_intent_id_index;

        drop index if exists event_change_system_job_id_index;
    `)
};
