
exports.up = function(knex) {
    return knex.raw(`
        -- CREATE TABLE "custom_payment_uncollected_fee_balance_info" --------------------------------------------------
        CREATE TABLE "public"."custom_payment_uncollected_fee_balance_info"
        (
            "custom_payment_uncollected_fee_balance_info_id" INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            "custom_payment_id"                              INT                                    NOT NULL,
            "created"                                        TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            "modified"                                       TIMESTAMP WITH TIME ZONE DEFAULT NULL,
            "sw_fee"                                         NUMERIC(8, 2)                          NOT NULL,
            "balance_details"                                JSONB                                  NOT NULL,
            "current_balance_sum"                            NUMERIC(8, 2)                          NOT NULL,
            "new_balance_sum"                                NUMERIC(8, 2)                          NOT NULL
        );
        
        COMMENT ON COLUMN "public"."custom_payment_uncollected_fee_balance_info"."custom_payment_id"
            IS 'Relation with custom_payment table';
        COMMENT ON COLUMN "public"."custom_payment_uncollected_fee_balance_info"."sw_fee"
            IS 'SW Fee for a payment type at the moment of uncollected Fee payment';
        COMMENT ON COLUMN "public"."custom_payment_uncollected_fee_balance_info"."balance_details"
            IS 'Current uncollected SW Fee balance details (accepted teams, cash payments etc.)';
        COMMENT ON COLUMN "public"."custom_payment_uncollected_fee_balance_info"."current_balance_sum"
            IS 'Current uncollected SW Fee balance value';
        COMMENT ON COLUMN "public"."custom_payment_uncollected_fee_balance_info"."new_balance_sum"
            IS 'New uncollected SW Fee balance value';
        
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add indexes to "custom_payment_uncollected_fee_balance_info" ------------------------------------------------
        CREATE INDEX "custom_payment_fee_balance_info_custom_payment_id_index"
            ON "public"."custom_payment_uncollected_fee_balance_info" ("custom_payment_id");
        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add modified trigger to custom_payment_uncollected_fee_balance_info table -----------------------------------
        CREATE TRIGGER "update_custom_payment_uncollected_fee_balance_info_modified"
            BEFORE UPDATE
            ON "public"."custom_payment_uncollected_fee_balance_info"
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
        -- -------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        DROP TABLE IF EXISTS "public"."custom_payment_uncollected_fee_balance_info";
    `)
};
