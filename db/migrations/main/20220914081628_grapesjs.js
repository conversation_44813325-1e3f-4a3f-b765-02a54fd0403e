exports.up = function (knex) {
    return knex.schema.raw(`
        ALTER TABLE email_template ADD COLUMN IF NOT EXISTS bee_html TEXT DEFAULT NULL;
        ALTER TABLE email_template ADD COLUMN IF NOT EXISTS grapes_json JSONB DEFAULT NULL;
        
        COMMENT ON COLUMN email_template.bee_html IS 'Bee editor html';
        COMMENT ON COLUMN email_template.grapes_json IS 'Grapes editor json';

        UPDATE email_template set bee_html = email_html;
    `);
};

exports.down = function (knex) {
    return knex.schema.raw(`
        ALTER TABLE email_template DROP COLUMN IF EXISTS bee_html;
        ALTER TABLE email_template DROP COLUMN IF EXISTS grapes_json;
    `);
};
