exports.up = function(knex) {
    return knex.raw(`
        INSERT INTO "public"."email_template_group" ("group", title, description, variables, usage_restrictions, position)
        VALUES (
            'clubs_directors',
            'Club Directors',
            '<i>Email templates that can be sent to active club directors</i>',
            '[
              {
                "field": "club_name",
                "title": "Club Name",
                "pattern": "{club_name}",
                "is_available_for_subject": true
              },
              {
                "field": "first",
                "title": "Club Director First Name",
                "pattern": "{cd_first}",
                "is_available_for_subject": true
              },
              {
                "field": "last",
                "title": "Club Director Last Name",
                "pattern": "{cd_last}",
                "is_available_for_subject": true
              },
              {
                "field": "name",
                "title": "Club Director First and Last Name",
                "pattern": "{cd_name}",
                "is_available_for_subject": true
              }
            ]',
            '{ "roles": ["god_mode"] }',
            99
        );
        UPDATE "email_template_group" SET "position" = 15 WHERE "group" = 'booths.payments';
        UPDATE "email_template_group" SET "position" = 14 WHERE "group" = 'camps';
        UPDATE "email_template_group" SET "position" = 13 WHERE "group" = 'event.owners';
        UPDATE "email_template_group" SET "position" = 12 WHERE "group" = 'clubs_directors';
        INSERT INTO "email_template_type" (type, email_template_group, title, description, long_title)
            VALUES ('content', 'clubs_directors', 'Manual Mailing', '<em>Manual Mailing</em>', 'Manual Mailing');
    `);
};

exports.down = function(knex) {
    return knex.raw(`
        DELETE FROM "email_template_type" WHERE "email_template_group" = 'clubs_directors';
        DELETE FROM "email_template_group" WHERE "group" = 'clubs_directors';
        UPDATE "email_template_group" SET "position" = 14 WHERE "group" = 'booths.payments';
        UPDATE "email_template_group" SET "position" = 13 WHERE "group" = 'camps';
        UPDATE "email_template_group" SET "position" = 12 WHERE "group" = 'event.owners';
    `);
};
