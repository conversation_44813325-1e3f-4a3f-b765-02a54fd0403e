exports.up = function(knex) {
    return knex.schema.raw(`
        CREATE SCHEMA IF NOT EXISTS tilled;

        -- Create "tilled"."payment_intent" table -----------------------------------------------------------------

        CREATE TABLE tilled.payment_intent (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            tilled_payment_intent_id TEXT NOT NULL,
            id_at_gateway TEXT NOT NULL,
            status TEXT NOT NULL,
            amount NUMERIC(8, 2) NOT NULL,
            tilled_fee NUMERIC(8, 2) NOT NULL,
            tilled_percentage NUMERIC(8, 2) NOT NULL,
            created TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL
        );
        CREATE UNIQUE INDEX idx_tilled_payment_intent_tilled_payment_intent_id ON tilled.payment_intent(tilled_payment_intent_id);
        CREATE UNIQUE INDEX idx_tilled_payment_intent_id_at_gateway ON tilled.payment_intent(id_at_gateway);

        CREATE TRIGGER "update_tilled_payment_intent_modified"
            BEFORE UPDATE
            ON tilled.payment_intent
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();
    
        -- -------------------------------------------------------------------------------------------------------------
            
        -- Create "tilled"."customer" table -----------------------------------------------------------------

        CREATE TABLE tilled.customer (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            tilled_customer_id TEXT NOT NULL,
            id_at_gateway TEXT NOT NULL,
            user_id INT NOT NULL,
            tilled_customer_object JSONB NOT NULL,
            created TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL
        );
        CREATE UNIQUE INDEX idx_tilled_customer_tilled_customer_id ON tilled.customer(tilled_customer_id);
        CREATE UNIQUE INDEX idx_tilled_customer_id_at_gateway ON tilled.customer(id_at_gateway);
        CREATE UNIQUE INDEX idx_tilled_customer_user_id ON tilled.customer(user_id);

        CREATE TRIGGER "update_tilled_customer_modified"
            BEFORE UPDATE
            ON tilled.customer
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();

        -- -------------------------------------------------------------------------------------------------------------
  
        -- Create "tilled"."account" table -----------------------------------------------------------------

        CREATE TABLE tilled.account (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            tilled_account_id TEXT NOT NULL,
            id_at_gateway TEXT NOT NULL,
            account_name TEXT NOT NULL,
            account_email TEXT NOT NULL,
            account_statement VARCHAR(20) NOT NULL,
            user_id INT NOT NULL,
            is_test BOOLEAN NOT NULL,
            is_platform BOOLEAN NOT NULL,
            created TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL
        );
        CREATE UNIQUE INDEX idx_tilled_account_tilled_account_id ON tilled.account(tilled_account_id);
        CREATE UNIQUE INDEX idx_tilled_account_id_at_gateway ON tilled.account(id_at_gateway);
        CREATE INDEX idx_tilled_account_user_id ON tilled.account(user_id);

        CREATE TRIGGER "update_tilled_account_modified"
            BEFORE UPDATE
            ON tilled.account
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();

        -- -------------------------------------------------------------------------------------------------------------
  
        -- Create "tilled"."account" table -----------------------------------------------------------------

        CREATE TABLE tilled.event (
            id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
            tilled_event_id TEXT NOT NULL,
            id_at_gateway TEXT NOT NULL,
            type TEXT NOT NULL,
            data JSONB NOT NULL,
            tilled_object_id TEXT NOT NULL,
            created TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL,
            modified TIMESTAMP WITHOUT TIME ZONE DEFAULT now() NOT NULL
        );
        CREATE UNIQUE INDEX idx_tilled_event_tilled_event_id ON tilled.event(tilled_event_id);
        CREATE UNIQUE INDEX idx_tilled_event_id_at_gateway ON tilled.event(id_at_gateway);
        CREATE INDEX idx_tilled_event_tilled_object_id ON tilled.event(tilled_object_id);

        CREATE TRIGGER "update_tilled_event_modified"
            BEFORE UPDATE
            ON tilled.event
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_column();

        -- -------------------------------------------------------------------------------------------------------------

        -- Alter "public"."purchase" table -----------------------------------------------------------------

        ALTER TABLE public.purchase ADD COLUMN tilled_payment_intent_id TEXT;
        CREATE INDEX idx_purchase_tilled_payment_intent_id ON public.purchase(tilled_payment_intent_id);
        
        -- -------------------------------------------------------------------------------------------------------------


        -- Alter "public"."event" table -----------------------------------------------------------------
        
        ALTER TABLE public.event ADD COLUMN tilled_tickets_fee NUMERIC(8, 2);
        ALTER TABLE public.event ADD COLUMN tilled_percentage NUMERIC(8, 2);
        ALTER TABLE public.event ADD COLUMN tilled_tickets_account_id INT;
        CREATE INDEX idx_event_tilled_tickets_account_id ON public.event(tilled_tickets_account_id);

        -- -------------------------------------------------------------------------------------------------------------

    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE public.purchase DROP COLUMN tilled_payment_intent_id;
        ALTER TABLE public.event DROP COLUMN tilled_tickets_fee;
        ALTER TABLE public.event DROP COLUMN tilled_percentage;
        ALTER TABLE public.event DROP COLUMN tilled_tickets_account_id;
        
        DROP TABLE tilled.event;
        DROP TABLE tilled.account;
        DROP TABLE tilled.customer;
        DROP TABLE tilled.payment_intent;
    `);
};
