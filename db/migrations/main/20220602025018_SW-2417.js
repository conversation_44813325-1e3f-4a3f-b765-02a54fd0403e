
exports.up = function(knex) {
  return knex.schema.raw(String.raw`
    -- Create tickets.payments template ------------------------------------------------------------
    WITH main_template AS (
        INSERT INTO public.email_template
        (email_html, email_subject, email_text,
        event_owner_id, recipient_type, sender_type, title, event_id, bee_json,
        img_name, email_template_type, is_valid, email_template_group, published,
        deleted)
        VALUES
        ('<!DOCTYPE html><html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en"><head><title></title><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width,initial-scale=1"><!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><style>
        *{box-sizing:border-box}body{margin:0;padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}#MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}.desktop_hide,.desktop_hide table{mso-hide:all;display:none;max-height:0;overflow:hidden}@media (max-width:660px){.row-content{width:100%!important}.column .border,.mobile_hide{display:none}table{table-layout:fixed!important}.stack .column{width:100%;display:block}.mobile_hide{min-height:0;max-height:0;max-width:0;overflow:hidden;font-size:0}.desktop_hide,.desktop_hide table{display:table!important;max-height:none!important}}
        </style></head><body style="background-color:#fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none"><table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff"><tbody><tr><td><table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table 
        class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px">{event_logo}</p></div></div></td></tr></table></td>
        <td class="column column-2" width="16.666666666666668%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:15px;padding-left:0;padding-top:15px"><div></div></td></tr></table></td><td 
        class="column column-3" width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px"><div 
        style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:right;mso-line-height-alt:16.8px">&nbsp;</p><p style="margin:0;font-size:14px;text-align:center"><span style="font-size:26px;"><strong>{event_name}&nbsp;</strong></span></p><p style="margin:0;font-size:14px;text-align:center">{event_dates_info} &nbsp;&nbsp;</p></div>
        </div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td 
        class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="divider_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-bottom:10px;padding-left:10px;padding-right:10px"><div align="center"><table border="0" 
        cellpadding="0" cellspacing="0" role="presentation" width="100%" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid #bbb"><span>&#8202;</span></td></tr></table></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-3" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td>
        <table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" 
        border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px">
        <span style="font-size:16px;">Present this QR code on a mobile device along with a <span style="background-color:#ffffff;color:#337ab7;"><strong>Government photo ID</strong></span> to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-4" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" 
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center"><span style="font-size:20px;">{ticket_holder_name}</span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-5" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table
         class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-left:5px;padding-right:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td><td class="column column-2" width="50%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" 
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center">{qr_code_image}</p></div></div></td></tr></table></td><td 
        class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
        </table><table class="row row-6" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="25%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:0"><div></div></td></tr></table></td><td class="column column-2" width="50%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:5px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center">{apple_wallet_icon}</p></div></div></td></tr></table></td><td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-7" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-left:10px;padding-right:10px;padding-top:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;mso-line-height-alt:32.4px"><span style="font-size:18px;color:#9b9b9b;">Participant information:</span></p></div></div></td></tr></table>
        </td></tr></tbody></table></td></tr></tbody></table><table class="row row-8" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" 
        width="41.666666666666664%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div><table class="text_block mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td 
        style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px"><span style="font-size:16px;">Pass barcode:</span></p><p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px"><span style="font-size:16px;">Email:</span></p><p 
        style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px"><span style="font-size:16px;">Phone:</span></p><p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px"><span style="font-size:16px;">Purchased:</span></p><p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:25.2px">&nbsp;</p><p style="margin:0;font-size:16px;text-align:right;mso-line-height-alt:28.8px"><span style="font-size:16px;">Price:</span></p></div></div></td></tr></table>
        <div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div></td><td class="column column-2" width="58.333333333333336%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div><table class="text_block mobile_hide" width="100%" border="0" cellpadding="0" cellspacing="0" 
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:25.2px;color:#555;line-height:1.8;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:16px;mso-line-height-alt:28.8px"><span style="font-size:16px;">{ticket_barcode}</span></p><p 
        style="margin:0;font-size:16px;mso-line-height-alt:28.8px"><span style="font-size:16px;">{payer_email}</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:28.8px"><span style="font-size:16px;">{payer_phone}</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:28.8px"><span style="font-size:16px;">{payment_date_time}</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:25.2px">&nbsp;</p><p style="margin:0;font-size:16px;mso-line-height-alt:28.8px">
        <span style="font-size:16px;">{ticket_price}&nbsp;</span></p></div></div></td></tr></table><div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div></td></tr></tbody></table></td></tr></tbody></table><table class="row row-9" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" 
        role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div><table class="text_block desktop_hide" width="100%" border="0" cellpadding="0" 
        cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word;mso-hide:all;display:none;max-height:0;overflow:hidden"><tr><td style="padding-bottom:10px;padding-left:10px;padding-right:10px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:14px;mso-line-height-alt:21px;color:#555;line-height:1.5;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:16px;mso-line-height-alt:24px">
        <span style="font-size:16px;">Pass barcode: {ticket_barcode}</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:24px"><span style="font-size:16px;">Email: {payer_email}</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:24px"><span style="font-size:16px;">Phone: {payer_phone}</span></p><p style="margin:0;font-size:16px;mso-line-height-alt:24px"><span style="font-size:16px;">Purchased: {payment_date_time}</span></p><p 
        style="margin:0;font-size:16px;mso-line-height-alt:21px">&nbsp;</p><p style="margin:0;font-size:16px;mso-line-height-alt:24px"><span style="font-size:16px;">Price: {ticket_price}&nbsp;</span></p></div></div></td></tr></table><div class="spacer_block" style="height:5px;line-height:5px;font-size:1px">&#8202;</div></td></tr></tbody></table></td></tr></tbody></table><table class="row row-10" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:Arial,sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:14px;font-family:''Helvetica Neue'',Helvetica,Arial,sans-serif;mso-line-height-alt:16.8px;color:#555;line-height:1.2"><p style="margin:0"><span style="color:#9b9b9b;font-size:18px;">All ticket holders on this purchase:</span></p><p style="margin:0"><em><span style="color:#9b9b9b;font-size:12px;">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></em></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody>
        </table><table class="row row-11" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="25%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td><td class="column column-2" width="50%" 
        style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px"><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" 
        style="font-size:14px;mso-line-height-alt:16.8px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:14px;text-align:center">{tickets_links}</p></div></div></td></tr></table></td><td class="column column-3" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="empty_block" width="100%" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tr><td style="padding-right:0;padding-bottom:5px;padding-left:0;padding-top:5px"><div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table><table class="row row-12" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0"><tbody><tr><td><table class="row-content stack" align="center" border="0" 
        cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:#fff;color:#000;width:640px" width="640"><tbody><tr><td class="column column-1" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0"><table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" 
        style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word"><tr><td><div style="font-family:sans-serif"><div class="txtTinyMce-wrapper" style="font-size:12px;mso-line-height-alt:14.399999999999999px;color:#555;line-height:1.2;font-family:Tahoma,Verdana,Segoe,sans-serif"><p style="margin:0;font-size:12px"><span style="font-size:18px;color:#9b9b9b;">Important Information:</span></p><p style="margin:0;font-size:12px"><span style="font-size:14px;">{tickets_receipt_descr}</span>
        </p><p style="margin:0;font-size:12px;mso-line-height-alt:14.399999999999999px">&nbsp;</p><p style="margin:0;font-size:12px;text-align:center"><span style="font-size:14px;">{social_icons} </span></p></div></div></td></tr></table></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><!-- End --></body></html>', 
        '{ticket_holder_name}''s VIP Tickets for {event_name}', 
        '{event_logo}
        {event_name} {event_dates_info}
        Present this QR code on a mobile device along with a Government photo ID to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.{ticket_holder_name}{qr_code_image}
        {apple_wallet_icon}Participant information:
        Pass barcode:Email:Phone:Zip Code:Purchased: Price:  {ticket_barcode}{payer_email}{payer_phone}{payer_zip}{payment_date_time} {ticket_price}   Pass barcode: {ticket_barcode}Email: {payer_email}
        Phone: {payer_phone}Zip Code: {payer_zip}Purchased: {payment_date_time} Price: {ticket_price}  All ticket holders on this purchase:
        (Click on a name to access each ticket if you do not receive the individual QR code ticket email)
        {tickets_links}Important Information:{tickets_receipt_descr}
        {social_icons}', null, null, null, 'Assigned free ticket email template', null,
        '{
            "page": {
              "body": {
                "type": "mailup-bee-page-proprerties",
                "content": {
                  "style": {
                    "color": "#000000",
                    "font-family": "Tahoma, Verdana, Segoe, sans-serif"
                  },
                  "computedStyle": {
                    "align": "center",
                    "linkColor": "#0000FF",
                    "messageWidth": "640px",
                    "messageBackgroundColor": "#FFFFFF"
                  }
                },
                "webFonts": [],
                "container": {
                  "style": {
                    "background-color": "#ffffff"
                  }
                }
              },
              "rows": [
                {
                  "type": "two-columns-3-9-empty",
                  "uuid": "8dc99e86-facf-4f99-b16c-4a73f6709a61",
                  "columns": [
                    {
                      "uuid": "13491a97-7ef5-4976-88c2-b5ce25028565",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "ea8ea509-e28e-4372-8cdf-56b1ec3c9441",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Event''s Logo Image\">{event_logo}</code></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 5
                    },
                    {
                      "uuid": "ab0f0cf4-ad56-4a5c-8c96-4ebcc14016b2",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "15px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "15px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 2
                    },
                    {
                      "uuid": "f155dfb4-bec0-4437-bc83-fb0bd4cb7dce",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "815319b0-a075-4f1e-b49b-6307ff942846",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 26px; line-height: 31px;\" data-mce-style=\"font-size: 26px; line-height: 31px;\"><strong>{event_name}&nbsp;</strong></span></p><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{event_dates_info} &nbsp;&nbsp;</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 5
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "d48adbd9-d3c0-4adc-8b46-0836c863c708",
                  "columns": [
                    {
                      "uuid": "daaad7d1-7cdc-46c6-a5a1-e2a33c99ff05",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-divider",
                          "uuid": "2a2d3b85-6a43-4300-94d1-816a3d675202",
                          "descriptor": {
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "divider": {
                              "style": {
                                "width": "100%",
                                "border-top": "1px solid #BBBBBB"
                              }
                            },
                            "computedStyle": {
                              "align": "center",
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "2ff35e03-88a6-4802-93fe-3a07a8229883",
                  "columns": [
                    {
                      "uuid": "38fe332b-62f5-412f-91dd-fa7c0984a5c0",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "5238a7af-f7f8-4366-838b-1b3712b41655",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 19px;\" data-mce-style=\"font-size: 16px; line-height: 19px;\">Present this QR code on a mobile device along with a <span style=\"background-color: #ffffff; color: #337ab7; line-height: 14px;\" data-mce-style=\"background-color: #ffffff; color: #337ab7; line-height: 14px;\"><strong>Government photo ID</strong></span> to gain entry. Adults will NOT be admitted without a corresponding ID. Minors without ID must accompany adults to enter.</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "745604c7-8063-4576-96fa-a8bf49d55e3a",
                  "columns": [
                    {
                      "uuid": "b657e9c3-6775-4ce2-8a5e-d46dff1c9d37",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "32618c85-c011-4da7-891b-db21f29ff8e0",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><span style=\"font-size: 20px; line-height: 24px;\" data-mce-style=\"font-size: 20px; line-height: 24px;\">{ticket_holder_name}</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "500px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "449493e8-06d8-4781-82bc-23a04ca7c210",
                  "columns": [
                    {
                      "uuid": "8c8bd558-b2f1-4259-afb8-51f199605f86",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "5px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "5px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "bfca73bb-730d-4e95-b2c7-57a23cb3f289",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f131899d-deed-4445-89ff-8152ffa25dc1",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{qr_code_image}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "5px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 6
                    },
                    {
                      "uuid": "71528071-61cc-44d1-a61a-0ab6d775e993",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 3
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "500px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "966df2ff-a3d3-4f57-ab18-0c079c633dee",
                  "columns": [
                    {
                      "uuid": "8f3aa883-b462-40c7-b822-301193f4759d",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "0px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "38257881-30d8-4246-a5be-744263afc157",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "2391bab3-650f-41e5-a4b5-54aa60add560",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\">{apple_wallet_icon}</p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 6
                    },
                    {
                      "uuid": "c922f613-14a9-4838-9d2b-759884407b88",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 3
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "500px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "25ef8439-13e7-482d-935f-96f5466022bf",
                  "columns": [
                    {
                      "uuid": "96a608a7-4bfa-4e85-8942-69e502af65be",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "21c36dd1-53f6-4203-b598-152147f192f0",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 14px; line-height: 25px; word-break: break-word;\" data-mce-style=\"font-size: 14px; line-height: 25px; word-break: break-word;\"><span style=\"font-size: 18px; color: #9b9b9b; line-height: 32px;\" data-mce-style=\"font-size: 18px; color: #9b9b9b; line-height: 32px;\">Participant information:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "180%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "two-columns-3-9-empty",
                  "uuid": "dc31086c-be26-4284-8d30-425c03849bbb",
                  "columns": [
                    {
                      "uuid": "3f6a4e9f-dc29-4d93-abdd-b6ed53877d57",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "8143d083-50f9-45e6-b04d-4ce4aeeba3bd",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Pass barcode:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Email:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Phone:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Purchased:</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word; text-align: right;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">Price:</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "180%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": true,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 5
                    },
                    {
                      "uuid": "85e4736c-287f-4b4c-9ed6-4f90f44babff",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "3d7d538e-be85-4904-9f92-a5e2ac04ed46",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 25px;\" data-mce-style=\"font-size: 14px; line-height: 25px;\"><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{ticket_barcode}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_email}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payer_phone}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{payment_date_time}</span></p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 28px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 28px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 28px;\" data-mce-style=\"font-size: 16px; line-height: 28px;\">{ticket_price}&nbsp;</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "180%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "0px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": true,
                              "hideContentOnDesktop": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 7
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "500px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "24d8ff7a-b079-4914-9728-ce62c28eb203",
                  "columns": [
                    {
                      "uuid": "1285400a-9327-4dc7-bef9-0cec48818d23",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "6463abe7-a579-4e49-bfff-cca7c2163257",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 21px;\" data-mce-style=\"font-size: 14px; line-height: 21px;\"><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Pass barcode: {ticket_barcode}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Email: {payer_email}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Phone: {payer_phone}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Purchased: {payment_date_time}</span></p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 16px; line-height: 24px; word-break: break-word;\" data-mce-style=\"font-size: 16px; line-height: 24px; word-break: break-word;\"><span style=\"font-size: 16px; line-height: 24px;\" data-mce-style=\"font-size: 16px; line-height: 24px;\">Price: {ticket_price}&nbsp;</span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "150%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "0px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false,
                              "hideContentOnDesktop": true
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "500px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "efee5eb3-9645-405e-a905-20ac97345aa8",
                  "columns": [
                    {
                      "uuid": "50309f73-e9f2-46ea-8386-b151b8c53011",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "f3889c0b-eb2d-48b6-ad38-940396fe02ec",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px; font-family: Tahoma, Verdana, Segoe, sans-serif;\" data-mce-style=\"font-size: 14px; line-height: 16px; font-family: Tahoma, Verdana, Segoe, sans-serif;\"><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><span style=\"color: #9b9b9b; font-size: 18px; line-height: 21px;\" data-mce-style=\"color: #9b9b9b; font-size: 18px; line-height: 21px;\">All ticket holders on this purchase:</span></p><p style=\"line-height: 14px; word-break: break-word;\" data-mce-style=\"line-height: 14px; word-break: break-word;\"><em><span style=\"color: #9b9b9b; font-size: 12px; line-height: 14px;\" data-mce-style=\"color: #9b9b9b; font-size: 12px; line-height: 14px;\">(Click on a name to access each ticket if you do not receive the individual QR code ticket email)</span></em></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "''Helvetica Neue'', Helvetica, Arial, sans-serif",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "three-columns-empty",
                  "uuid": "a166dd66-04e5-47a3-b7b7-84247d8467ce",
                  "columns": [
                    {
                      "uuid": "60b15c4a-dacb-496e-8b30-9f459b6728c0",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 3
                    },
                    {
                      "uuid": "ac3f3376-092f-4093-9476-1e1454e41200",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "eee3a9ad-3d84-462a-bfe4-72168cf26d12",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><p style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\" data-mce-style=\"font-size: 14px; line-height: 16px; word-break: break-word; text-align: center;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Links\">{tickets_links}</code></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 6
                    },
                    {
                      "uuid": "ea17647b-32da-4d70-a9da-59fb14c23914",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [],
                      "grid-columns": 3
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "500px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                },
                {
                  "type": "one-column-empty",
                  "uuid": "502bbbc6-d05c-43c8-8f8c-196442714d44",
                  "columns": [
                    {
                      "uuid": "45c7a634-62fe-4caa-b4ac-30aa2c8127ae",
                      "style": {
                        "border-top": "0px solid transparent",
                        "border-left": "0px solid transparent",
                        "padding-top": "5px",
                        "border-right": "0px solid transparent",
                        "padding-left": "0px",
                        "border-bottom": "0px solid transparent",
                        "padding-right": "0px",
                        "padding-bottom": "5px",
                        "background-color": "transparent"
                      },
                      "modules": [
                        {
                          "type": "mailup-bee-newsletter-modules-text",
                          "uuid": "390dc1eb-23e1-4114-9c3e-58a516f9a408",
                          "descriptor": {
                            "text": {
                              "html": "<div class=\"txtTinyMce-wrapper\" style=\"font-size: 12px; line-height: 14px;\" data-mce-style=\"font-size: 12px; line-height: 14px;\"><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 18px; line-height: 21px; color: #9b9b9b;\" data-mce-style=\"font-size: 18px; line-height: 21px; color: #9b9b9b;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">Important Information</code>:</span></p><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">{tickets_receipt_descr}</code></span></p><p style=\"font-size: 12px; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; line-height: 14px; word-break: break-word;\">&nbsp;</p><p style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\" data-mce-style=\"font-size: 12px; text-align: center; line-height: 14px; word-break: break-word;\"><span style=\"font-size: 14px; line-height: 16px;\" data-mce-style=\"font-size: 14px; line-height: 16px;\"><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Social Icons\">{social_icons}</code><code spellcheck=\"false\" data-bee-type=\"mergetag\" data-bee-code=\"\" data-bee-name=\"Tickets Receipt Description\">&nbsp;</code></span></p></div>",
                              "style": {
                                "color": "#555555",
                                "font-family": "inherit",
                                "line-height": "120%"
                              },
                              "computedStyle": {
                                "linkColor": "#0000FF"
                              }
                            },
                            "style": {
                              "padding-top": "10px",
                              "padding-left": "10px",
                              "padding-right": "10px",
                              "padding-bottom": "10px"
                            },
                            "computedStyle": {
                              "hideContentOnMobile": false
                            }
                          }
                        }
                      ],
                      "grid-columns": 12
                    }
                  ],
                  "content": {
                    "style": {
                      "color": "#000000",
                      "width": "640px",
                      "background-color": "#FFFFFF",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    },
                    "computedStyle": {
                      "rowColStackOnMobile": true,
                      "rowReverseColStackOnMobile": false
                    }
                  },
                  "container": {
                    "style": {
                      "background-color": "transparent",
                      "background-image": "none",
                      "background-repeat": "no-repeat",
                      "background-position": "top left"
                    }
                  }
                }
              ],
              "title": "BF-basic-newsletter",
              "template": {
                "name": "template-base",
                "type": "basic",
                "version": "0.0.1"
              },
              "description": "BF-basic-newsletter"
            },
            "comments": {}
          }',null, 'tickets.assigned.ticket.free', true, 'tickets.payments', true, null)
            RETURNING email_template_id
        ), insert_type AS (
        -- Create tickets.assigned.ticket.free template type ------------------------------------------------------------
        INSERT INTO public.email_template_type
        (type, email_template_group, title, description, long_title, is_trigger,
        default_email_template_id)
            VALUES ('tickets.assigned.ticket.free', 'tickets.payments', 'Assigned Free Ticket email template',
                '<em>Assigned Free Ticket email template</em>', 'Assigned Free Ticket email template', true,
                (SELECT email_template_id
                FROM main_template))
                )

        -- Create event email trigger for tickets.assigned.ticket.free template type ------------------------------------------------------------
        INSERT
            INTO public.event_email_trigger (email_template_type, email_template_group, email_template_id, event_id)
            VALUES ('tickets.assigned.ticket.free', 'tickets.payments', (SELECT email_template_id FROM main_template), 0);
  `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DELETE FROM email_template WHERE email_template_type = 'tickets.assigned.ticket.free';
        DELETE FROM event_email_trigger WHERE email_template_type = 'tickets.assigned.ticket.free';
        DELETE FROM email_template_type WHERE type = 'tickets.assigned.ticket.free';
    `)
};
