# Manual Test Plan: Disable Import SE and AAU for Club Directors

## 1. Test Objectives
- Verify that SE (SportEngine) and AAU import buttons are disabled for Club Directors after season switching until September 1st
- Verify that appropriate tooltips are displayed when hovering over disabled buttons
- Verify that the buttons are styled correctly (grey color, cursor not-allowed)
- Verify that backend protection prevents direct API calls for imports before September 1st
- Verify that the feature works correctly in different environments (production vs. development)

## 2. Test Environments
- **Production Environment**: All restrictions should be applied
- **Development Environment**: Import functionality should work normally (buttons not disabled)

## 3. Test Scenarios

### 3.1 Frontend Testing - Production Environment

#### Test Case 1: Button Appearance Before September 1st
**Preconditions:**
- User has Club Director role
- Current date is before September 1st of the current year
- User is in production environment

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Observe the SE Import and AAU Import buttons

**Expected Results:**
- Both buttons should be disabled (grey color #BEBEBE)
- Cursor should show "not-allowed" when hovering over the buttons

#### Test Case 2: SE Import Button Tooltip
**Preconditions:**
- Same as Test Case 1

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Hover over the disabled SE Import button

**Expected Results:**
- A tooltip should appear with text: "Imports will be available on September 1st when USAV activates the new season."

#### Test Case 3: AAU Import Button Tooltip
**Preconditions:**
- Same as Test Case 1

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Hover over the disabled AAU Import button

**Expected Results:**
- A tooltip should appear with text: "Imports will be available on September 1st when AAU activates the new season."

#### Test Case 4: Button Appearance After September 1st
**Preconditions:**
- User has Club Director role
- Current date is on or after September 1st of the current year
- User is in production environment

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Observe the SE Import and AAU Import buttons

**Expected Results:**
- Both buttons should be enabled (normal blue color)
- Buttons should be clickable
- No tooltips should appear when hovering over the buttons

### 3.2 Frontend Testing - Development Environment

#### Test Case 5: Button Appearance in Development Environment
**Preconditions:**
- User has Club Director role
- User is in development environment (localhost, dev.*, *.dev.*)
- Any date (before or after September 1st)

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Observe the SE Import and AAU Import buttons

**Expected Results:**
- Both buttons should be enabled (normal blue color)
- Buttons should be clickable
- No tooltips should appear when hovering over the buttons

### 3.3 Backend Testing - Production Environment

#### Test Case 6: API Protection for SE Import Before September 1st
**Preconditions:**
- User has Club Director role
- Current date is before September 1st of the current year
- User is in production environment

**Steps:**
1. Attempt to trigger an SE import via direct API call to `/api/club/import/sportengine`

**Expected Results:**
- Request should be rejected with HTTP 403 status code
- Response should contain message: "Import disabled until September 1st."

#### Test Case 7: API Protection for AAU Import Before September 1st
**Preconditions:**
- User has Club Director role
- Current date is before September 1st of the current year
- User is in production environment

**Steps:**
1. Attempt to trigger an AAU import via direct API call to `/api/club/import/aau`

**Expected Results:**
- Request should be rejected with HTTP 403 status code
- Response should contain message: "Import disabled until September 1st."

#### Test Case 8: API Access for SE Import After September 1st
**Preconditions:**
- User has Club Director role
- Current date is on or after September 1st of the current year
- User is in production environment

**Steps:**
1. Attempt to trigger an SE import via direct API call to `/api/club/import/sportengine`

**Expected Results:**
- Request should be processed normally (no 403 error)

#### Test Case 9: API Access for AAU Import After September 1st
**Preconditions:**
- User has Club Director role
- Current date is on or after September 1st of the current year
- User is in production environment

**Steps:**
1. Attempt to trigger an AAU import via direct API call to `/api/club/import/aau`

**Expected Results:**
- Request should be processed normally (no 403 error)

### 3.4 Backend Testing - Development Environment

#### Test Case 10: API Access for SE Import in Development Environment
**Preconditions:**
- User has Club Director role
- User is in development environment
- Any date (before or after September 1st)

**Steps:**
1. Attempt to trigger an SE import via direct API call to `/api/club/import/sportengine`

**Expected Results:**
- Request should be processed normally (no 403 error)

#### Test Case 11: API Access for AAU Import in Development Environment
**Preconditions:**
- User has Club Director role
- User is in development environment
- Any date (before or after September 1st)

**Steps:**
1. Attempt to trigger an AAU import via direct API call to `/api/club/import/aau`

**Expected Results:**
- Request should be processed normally (no 403 error)

### 3.5 Edge Cases and Boundary Testing

#### Test Case 12: Date Boundary Testing - August 31st
**Preconditions:**
- User has Club Director role
- Current date is August 31st of the current year
- User is in production environment

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Observe the SE Import and AAU Import buttons
4. Attempt to trigger imports via direct API calls

**Expected Results:**
- Buttons should be disabled with tooltips
- API calls should be rejected with 403 status

#### Test Case 13: Date Boundary Testing - September 1st
**Preconditions:**
- User has Club Director role
- Current date is September 1st of the current year
- User is in production environment

**Steps:**
1. Log in as a Club Director
2. Navigate to the Club Athletes page
3. Observe the SE Import and AAU Import buttons
4. Attempt to trigger imports via direct API calls

**Expected Results:**
- Buttons should be enabled without tooltips
- API calls should be processed normally

#### Test Case 14: User Role Testing - Non-Club Director
**Preconditions:**
- User does not have Club Director role
- Current date is before September 1st of the current year
- User is in production environment

**Steps:**
1. Log in as a non-Club Director user
2. Navigate to the Club Athletes page (if accessible)

**Expected Results:**
- Import buttons should not be visible to non-Club Director users

## 4. Test Data Requirements
- Club Director user account
- Non-Club Director user account
- Access to both production and development environments
- Ability to simulate or test on dates before and after September 1st

## 5. Test Execution Notes
- For date-dependent tests, you may need to mock the current date or coordinate testing around the September 1st boundary
- For environment-dependent tests, ensure you have access to both production and development environments
- Document any deviations from expected results during testing
- Take screenshots of UI elements for documentation purposes
