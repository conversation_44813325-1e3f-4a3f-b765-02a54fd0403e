# Event Settings Granular Permissions Implementation

## Overview
This implementation adds granular permissions for Event Settings, allowing Event Owners (EOs) to grant Co-EOs specific access to different sections of event settings rather than all-or-nothing access.

## Requirements Implemented

### Core Features
✅ **Granular Permission Options**: Added four independent sub-permissions under Event Settings:
- **General** (`edit_event_general`) - Access to general event settings
- **Location** (`edit_event_location`) - Access to location management
- **Event Users** (`edit_event_users`) - Access to Co-EO management
- **Transactional Emails** (`edit_event_transactional_emails`) - Access to email template management

✅ **UI Integration**: The existing permission modal automatically displays the hierarchical structure with the new sub-permissions

✅ **Backend Access Control**: All relevant API endpoints now check for specific sub-permissions instead of the generic `edit_event` permission

✅ **Frontend Access Control**: Event settings tabs are now conditionally displayed based on user's specific permissions

✅ **Migration for Existing Users**: Created migration to automatically grant existing Co-EOs all four sub-permissions to maintain current functionality

## Technical Implementation

### 1. Database Changes
**File**: `db/migrations/main/20250729140000_SW-4536.js`
- Adds four new event operations as children of `edit_event`
- Automatically grants existing Co-EOs all new sub-permissions
- Includes rollback functionality

### 2. Backend Changes

#### Constants Updated
**Files**: 
- `api/services/event/operations.js`
- `frontend/sport-wrench.constant.js`

Added new permission constants:
```javascript
EDIT_EVENT_GENERAL: 'edit_event_general',
EDIT_EVENT_LOCATION: 'edit_event_location', 
EDIT_EVENT_USERS: 'edit_event_users',
EDIT_EVENT_TRANSACTIONAL_EMAILS: 'edit_event_transactional_emails'
```

#### Policy Updates
**File**: `config/policies/event_access.js`

Updated controller policies to use granular permissions:
- `Event/SettingsController`: Different methods now check for appropriate sub-permissions
- `Event/EventUserController`: Now checks for `EDIT_EVENT_USERS` permission
- `Event/AEMController`: Now checks for `EDIT_EVENT_TRANSACTIONAL_EMAILS` permission

### 3. Frontend Changes

#### Permission Management UI
**Files**: 
- `frontend/events/settings/general/event-users/event-users-permissions/`

The existing hierarchical permission UI automatically handles the new sub-permissions without requiring changes.

#### Access Control
**Files**:
- `frontend/events/settings/event-settings.directive.js`
- `frontend/events/settings/event-settings.html`
- `frontend/common/EventACLService.js`

Updated to:
- Check specific sub-permissions for tab visibility
- Updated `userHasOnlyEventEditPermissions` logic to handle granular permissions
- Added permission check functions for each settings section

## Scenarios Covered

### ✅ Scenario 1: Add/Edit Modal Shows Granular Options
The existing permission modal automatically displays the hierarchical structure with:
- Edit Events pages (parent)
  - General (child)
  - Location (child) 
  - Event Users (child)
  - Transactional Emails (child)

### ✅ Scenario 2: Save New Co-EO with Selected Sub-permissions
The existing save functionality works with the new granular permissions. Only selected sub-permissions are persisted in the database.

### ✅ Scenario 3: UI Access Enforcement – Allowed Tab
Co-EOs with specific sub-permissions can access and edit the corresponding sections.

### ✅ Scenario 4: UI Access Enforcement – Blocked Tab
Co-EOs without specific sub-permissions cannot access the corresponding tabs (they won't be visible).

### ✅ Scenario 5: Live Permission Change
When permissions are updated, the backend invalidates the user's cached data, forcing a refresh of permissions on next request.

### ✅ Scenario 6: Default Migration for Existing Co-EOs
The migration automatically grants all existing Co-EOs all four sub-permissions, maintaining current functionality.

### ✅ Scenario 7: Edit Modal Reflects Stored State
The existing permission loading functionality correctly displays the current state of granular permissions.

### ✅ Scenario 8: Validation – Zero Sub-permissions Allowed
The system allows Co-EOs to have zero Event Settings permissions, in which case no settings tabs will be visible.

## Testing

### Unit Tests
**File**: `test/unit/event-settings-permissions.test.js`
- Tests permission constants are correctly defined
- Tests EventUserService can handle granular permissions

### Integration Tests  
**File**: `test/integration/event-settings-permissions.test.js`
- Verifies permission constants integration
- Verifies policy configuration updates
- Verifies migration file exists

## Deployment Notes

1. **Run Migration**: Execute the migration to add new permissions and update existing Co-EOs
2. **No Breaking Changes**: Existing functionality is preserved through the migration
3. **Backward Compatibility**: The system continues to work with the existing `edit_event` permission structure

## Future Enhancements

The hierarchical permission system is now in place and can be easily extended to add more granular permissions to other areas of the application following the same pattern.
