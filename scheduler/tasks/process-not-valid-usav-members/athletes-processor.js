
'use strict';

const historyActions = require('./history-actions');

const {
    MEMBER_TYPE,
    CANCELED_MEMBER_STATUS,
} = require('../../../api/lib/SEUtilsService');

class AthletesProcessor {
    #LIMIT = 100;

    async getMembers(season) {
        const query = `
            SELECT 
                (
                    SELECT coalesce(array_to_json(array_agg(row_to_json(teams))), '[]'::json)
                    FROM (
                        SELECT 
                            ra.roster_athlete_id,
                            rt.roster_club_id,
                            ra.roster_team_id,
                            e.event_id
                        FROM roster_athlete ra
                        JOIN event e 
                            ON e.event_id = ra.event_id
                            AND e.deleted IS NULL
                            AND e.date_start > now() AT TIME ZONE e.timezone
                        JOIN roster_team rt 
                            ON rt.roster_team_id = ra.roster_team_id
                            AND rt.deleted IS NULL
                            AND rt.locked IS FALSE
                        WHERE ra.master_athlete_id = ma.master_athlete_id
                            AND ra.deleted IS NULL
                            AND ra.deleted_by_user IS NULL
                    ) teams
                ) AS event_roster_teams,
                ma.aau_membership_id IS NOT NULL AS has_aau_membership,
                ma.master_athlete_id,
                ma.master_team_id,
                ma.master_club_id
            FROM master_athlete ma
            WHERE ma.usav_number IS NOT NULL
                AND ma.membership_status = $1
                AND ma.deleted IS NULL
                AND ma.season = $2
            LIMIT $3`;

        const { rows } = await Db.query(query, [
            CANCELED_MEMBER_STATUS,
            season,
            this.#LIMIT
        ]);

        return rows || [];
    }

    async processMember(memberData) {
        this.#validateMemberData(memberData);

        let tr = null;

        try {
            tr = await Db.begin();

            if (!_.isEmpty(memberData.event_roster_teams)) {
                await this.#removeMemberFromEventRoster(tr, memberData.event_roster_teams);
                await this.#saveRosterDeletingHistoryActions(tr, memberData);
            }

            const preparedAthleteClubData = await this.#prepareAthleteClubData(memberData);

            await this.#updateAthleteClubRow(tr, memberData.master_athlete_id, preparedAthleteClubData);

            if (this.#athleteWasRemovedFromRoster(memberData, preparedAthleteClubData)) {
                await historyActions.saveAthleteRemovedFromClubRosterAction(tr, memberData);
            }

            if (preparedAthleteClubData.deleted) {
                await historyActions.saveAthleteDeletedAction(tr, memberData);
            } else {
                await historyActions.saveAthleteUSAVDataRemovedAction(tr, memberData);
            }

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async #removeMemberFromEventRoster(tr, eventRosterTeams) {
        if (!tr) {
            throw new Error('Transaction should be defined');
        }

        if (_.isEmpty(eventRosterTeams)) {
            throw new Error(`eventRosterTeams can't be empty`);
        }

        const rosterAthleteIDs = eventRosterTeams.map(team => team.roster_athlete_id);

        const query = knex('roster_athlete')
            .update({ deleted: knex.fn.now() })
            .whereNull('deleted')
            .whereNull('deleted_by_user')
            .whereIn('roster_athlete_id', rosterAthleteIDs);

        return tr.query(query);
    }

    async #updateAthleteClubRow(tr, masterAthleteID, preparedData) {
        if (!tr) {
            throw new Error('Transaction should be defined');
        }

        if (_.isEmpty(preparedData)) {
            throw new Error(`preparedData couldn't be empty'`);
        }

        if (!masterAthleteID) {
            throw new Error('masterAthleteID is required');
        }

        const query = knex('master_athlete')
            .update(preparedData)
            .where('master_athlete_id', masterAthleteID)
            .whereNull('deleted');

        return tr.query(query);
    }

    async #prepareAthleteClubData(memberData) {
        let data = {};

        if (memberData.has_aau_membership) {
            const fieldsToSetEmpty = SportEngineMemberService
                .import.process
                .memberImport
                .prepareEmptyUsavFields(MEMBER_TYPE.ATHLETE);

            data = Object.assign(fieldsToSetEmpty, data);
        } else {
            data.deleted = knex.fn.now();

            if (memberData.master_team_id) {
                data.master_team_id = null;
            }
        }

        return data;
    }

    #validateMemberData(memberData = {}) {
        const {
            master_athlete_id,
            master_club_id,
            event_roster_teams,
            has_aau_membership,
        } = memberData;

        if (!master_athlete_id) {
            throw new Error('master_athlete_id is required');
        }

        if (!master_club_id) {
            throw new Error('master_club_id is required');
        }

        this.#validateEventRosterTeamsData(event_roster_teams);

        if (!_.isBoolean(has_aau_membership)) {
            throw new Error('has_aau_membership should be boolean');
        }
    }

    #validateEventRosterTeamsData(eventRosterTeams = []) {
        if (!_.isArray(eventRosterTeams)) {
            throw new Error('eventRosterTeams should be an array');
        }

        if (_.isEmpty(eventRosterTeams)) {
            return;
        }

        eventRosterTeams.forEach((eventRosterTeam = {}) => {
            if (!eventRosterTeam.roster_club_id) {
                throw new Error('roster club id is required');
            }

            if (!eventRosterTeam.roster_team_id) {
                throw new Error('roster team id is required');
            }

            if (!eventRosterTeam.event_id) {
                throw new Error('event id is required');
            }
        });
    }

    async #saveRosterDeletingHistoryActions(tr, memberData) {
        await Promise.all(memberData.event_roster_teams.map(async (team) => {
            const historyData = {
                ...team,
                master_athlete_id: memberData.master_athlete_id,
                master_club_id: memberData.master_club_id,
            };

            await historyActions.saveAthleteRemovedFromEventRosterAction(tr, historyData);
        }));
    }

    #athleteWasRemovedFromRoster(memberData, preparedAthleteClubData) {
        return memberData.master_team_id && !preparedAthleteClubData.master_team_id;
    }
}

module.exports = new AthletesProcessor();
