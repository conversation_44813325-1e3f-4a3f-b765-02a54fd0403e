const Joi = require('joi');

module.exports = {
    scheduleParams: Joi.object({
        clubPrivateRegCode: Joi.string().allow(null),
        eventID: Joi.number().integer()
            .when('clubPrivateRegCode', {
                is: Joi.exist().not(null),
                then: Joi.allow(null),
                otherwise: Joi.required()
            }),
        gender: Joi.string().allow(null),
        startFrom: Joi.string().allow(null),
        startTill: Joi.string().allow(null),
        teamCode: Joi.string().allow(null),
        requireBothTeams: Joi.boolean().default(false),
        orderBy: Joi.string().allow(null)
    })
};
