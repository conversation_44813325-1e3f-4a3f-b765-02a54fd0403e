const Joi = require('joi');

const { PAYMENT_TYPE } = require('../constants/payments');

module.exports = {
    clubInvoice: Joi.object().keys({
        roster_club_id: Joi.number().required().label('Roster Club ID'),
        amount: Joi.number().min(1).required().label('Total Amount'),
        description: Joi.string().allow(null, '').optional().label('Description'),
    }),
    clubInvoicePurchaseUpdate: Joi.object().keys({
        total_amount: Joi.number().min(1).required().label('Total Amount'),
        payment_intent_id: Joi.string().required().label('Payment Intent ID'),
        payment_type: Joi.string().valid(PAYMENT_TYPE.CARD, PAYMENT_TYPE.ACH).required().label('Payment Type'),
        purchase_id: Joi.number().required().label('Purchase ID'),
        master_club_id: Joi.number().required().label('Master Club ID'),
        club_owner_id: Joi.number().required().label('Club Owner ID'),
        user: Joi.object().keys({
            first: Joi.string().required().label('User First Name'),
            last: Joi.string().required().label('User Last Name'),
            user_id: Joi.number().required().label('User ID'),
            email: Joi.string().required().label('User Email'),
            phone: Joi.number().required().label('User Phone'),
            payer_ip: Joi.string().optional().label('Payer IP Address'),
        })
    })
};
