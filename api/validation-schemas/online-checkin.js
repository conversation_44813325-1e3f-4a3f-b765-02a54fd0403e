const Joi = require('joi');

const EMAIL_REGEX = /^\w+(?:[._+-]\w+)*@\w+(?:[_.-]\w+)*\.[a-zA-Z]{2,4}$/;
const PHONE_REGEX = /^\d{10,}$/;

const customRegexMsg = (msg) => {
    const _msg = msg || 'is invalid';

    return {
        messages: {
            'string.regex':{
                base: _msg
            }
        }
    };
};

const checkinResendSchema = Joi.object().preferences(customRegexMsg()).keys({
    email: Joi.string().pattern(EMAIL_REGEX).preferences(customRegexMsg()).label('Email'),
    phone: Joi.string().pattern(PHONE_REGEX).preferences(customRegexMsg()).label('Phone'),
}).or('email', 'phone').label('Request body');

const checkinSchema = Joi.object().keys({
    staffers: Joi.array().items(Joi.object().keys({
        name: Joi.string().required().label(`Staffer Name`),
        staff_id: Joi.number().integer().min(1).required().label(`Staffer Identifier`),
        phone: Joi.string().pattern(PHONE_REGEX).preferences(customRegexMsg()).required().label(`Staffer Phone`),
        email: Joi.string().pattern(EMAIL_REGEX).preferences(customRegexMsg()).required().label(`Staffer Email`),
        staff_type: Joi.number().integer().valid(1,2).required().label('Staffer Type')
    })).min(1).optional().label('Staffers list')
})

const checkinDeactivateSchema = Joi.object().keys({
    reason: Joi.string().required().allow(null, '').label('Reason'),
})

module.exports = {
    checkinResendSchema,
    checkinSchema,
    checkinDeactivateSchema,
};
