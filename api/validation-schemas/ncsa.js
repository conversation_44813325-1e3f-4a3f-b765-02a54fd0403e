
const Joi = require('joi');

const { ZIP_MESSAGE, CA_ZIP_REG_EXP, US_ZIP_REG_EXP, EMAIL_REGEX } = require('../lib/joi-constants');

const CA_ZIP_VALIDATOR = Joi.string().pattern(CA_ZIP_REG_EXP).preferences(ZIP_MESSAGE).required();
const US_ZIP_VALIDATOR = Joi.string().pattern(US_ZIP_REG_EXP).preferences(ZIP_MESSAGE).required();

let athleteSubmitSchema = Joi.object().keys({
    parent_email: Joi.string().pattern(EMAIL_REGEX).required().label('Parent Email'),
    parent_phone: Joi.string().min(10).required().replace(/\D/g, '').label('Parent Phone'),
    parent_first: Joi.string().required().label('Parent First Name'),
    parent_last: Joi.string().required().label('Parent Last Name'),
    athlete_first: Joi.string().required().label('Athlete First Name'),
    athlete_last: Joi.string().required().label('Athlete Last Name'),
    athlete_gradyear: Joi.number().required().min(2000).label('Athlete Graduation Year'),
    gender: Joi.string().valid('male', 'female').required().label('Gender'),
    zip: Joi.required().when('country', {
        is: 'CA',
        then: CA_ZIP_VALIDATOR,
        otherwise: US_ZIP_VALIDATOR,
    }).label('Zip')
})

module.exports = {
    athleteSubmitSchema
}
