'use strict';

const Joi = require('joi');

const {
    ZIP_MESSAGE,
    CA_ZIP_REG_EXP,
    US_ZIP_REG_EXP,
    USAV_REG_EXP,
    OFFICIALS_PAYMENT_OPTION,
    EMAIL_REGEX
} = require('../lib/joi-constants');

const CA_ZIP_VALIDATOR = Joi.string().pattern(CA_ZIP_REG_EXP).preferences(ZIP_MESSAGE).required();
const US_ZIP_VALIDATOR = Joi.string().pattern(US_ZIP_REG_EXP).preferences(ZIP_MESSAGE).required();

module.exports = {
	reg_info: Joi.object().keys({
		is_official 				: Joi.boolean().label('Is Official'),
		is_staff 					: Joi.boolean().label('Is Staff'),
		payment_option 				: Joi.string()
                                        .when(Joi.ref('is_official'), {
                                            is 			: true,
                                            then 		: Joi.valid(
                                                OFFICIALS_PAYMENT_OPTION.DIRECT_DEPOSIT,
                                                OFFICIALS_PAYMENT_OPTION.ON_SITE,
                                                OFFICIALS_PAYMENT_OPTION.MAILED,
                                                OFFICIALS_PAYMENT_OPTION.ARBITER_PAY,
                                                OFFICIALS_PAYMENT_OPTION.RQ_PAY
                                            ).required(),
                                            otherwise 	: Joi.optional().allow(null, '')
                                        }).label('Official Payment Option'),
        staff_payment_option 	    : Joi.string()
                                        .when(Joi.ref('is_staff'), {
                                            is 			: true,
                                            then 		: Joi.valid(
                                                OFFICIALS_PAYMENT_OPTION.DIRECT_DEPOSIT,
                                                OFFICIALS_PAYMENT_OPTION.ON_SITE,
                                                OFFICIALS_PAYMENT_OPTION.MAILED,
                                                OFFICIALS_PAYMENT_OPTION.ARBITER_PAY,
                                                OFFICIALS_PAYMENT_OPTION.NO_PAYMENT_REQUIRED
                                            ).required(),
                                            otherwise 	: Joi.optional().allow(null, '')
                                        }).label('Staff Payment Option'),
		bank_account_number 		: Joi.string().max(50).label('Account Number')
										.when(Joi.ref('payment_option'), {
											is 			: OFFICIALS_PAYMENT_OPTION.DIRECT_DEPOSIT,
											then 		: Joi.required(),
											otherwise 	: Joi.optional().allow(null)
										}),
		bank_account_routing		: Joi.string().max(50).label('Account Routing')
										.when(Joi.ref('payment_option'), {
											is 			: OFFICIALS_PAYMENT_OPTION.DIRECT_DEPOSIT,
											then 		: Joi.required(),
											otherwise 	: Joi.optional().allow(null)
                                        }),
        arbiterpay_username		    : Joi.string().max(50).label('ArbiterPay Username')
                                        .when(Joi.ref('payment_option'), {
                                            is 			: OFFICIALS_PAYMENT_OPTION.ARBITER_PAY,
                                            then 		: Joi.required(),
                                            otherwise 	: Joi.optional().allow(null)
                                        }),
        rq_pay_username		        : Joi.string().pattern(EMAIL_REGEX).optional().allow(null, '')
                                        .max(50).label('RQ Pay Username'),
        arbiterpay_account_number	: Joi.string().max(50).label('ArbiterPay Account Number')
                                        .when(Joi.ref('payment_option'), {
                                            is 			: OFFICIALS_PAYMENT_OPTION.ARBITER_PAY,
                                            then 		: Joi.required(),
                                            otherwise 	: Joi.optional().allow(null)
                                        }),
		travel_method 				: Joi.string().label('Travel Method')
										.when(Joi.ref('is_official'), {
											is 			: true,
											then 		: Joi.valid('car', 'air', 'other').required(),
											otherwise 	: Joi.allow(null).optional()
										}),
		able_to_transport_others 	: Joi.boolean().allow(null).label('Ability to transport others'),
		need_hotel_room 	 		: Joi.boolean().allow(null).label('Requesting a hotel room'),
        roommate_preference	 		: Joi.string().optional().allow('', null).label('Roommate Preference'),
		schedule_availability 		: Joi.object()
                                        .when(Joi.ref('is_official'), {
                                            is 			: true,
                                            then 		: Joi.object().pattern(
                                                            /^day_[0-9]*$/, Joi.number().required().valid(0, 1)
                                                            ).required(),
                                            otherwise 	: Joi.optional().allow(null, '')
                                        }).label('Schedule Availability'),
		departure_datetime 		    : Joi.string().required().label('Departure Date/Time'),
		additional_restrictions 	: Joi.string().allow('', null).label('Additional Restrictions'),
		first 						: Joi.string().min(1).label('First Name'),
		last  						: Joi.string().min(1).label('Last Name'), 
		official_id 				: Joi.number().min(1).label('Official ID'),
        hotel_nights_required       : Joi.object().optional().allow(null).label('Required Hotel Nights'),
        staff_arrival_datetime      : Joi.string()
                                        .when(Joi.ref('is_staff'), {
                                            is 			: true,
                                            then 		: Joi.required(),
                                            otherwise 	: Joi.optional().allow(null, '')
                                        }).label('Arrival Date/Time'),
        clothes                     : Joi.array().items(Joi.object().keys({
                                        size            : Joi.string().required().label('Clothing size'),
                                        common_item_id  : Joi.string().required().label('Clothing item id'),
                                        gender_size     : Joi.string().allow(null).label('Gender size'),
                                    })).optional().label('Clothes'),
        official_additional_role_id : Joi.number().optional().allow(null).label('Official additional role id'),

	}).or('is_official', 'is_staff'),

	sch_info: Joi.object().keys({
		schedule_name 		: Joi.string().optional().allow(null).label('Schedule Name'),
		work_status 		: Joi.string().optional().allow(null)
									.valid('pending', 'approved', 'declined', 'waitlisted')
									.label('Work Status'),
		rank 				: Joi.string().optional().allow(null)
									.valid('UnRated', 'International', 'National', 'Jr. National', 
											'Regional', 'Provisional', 'In Region', 'Other'
									)
									.label('Rank'),
		notes 				: Joi.string().optional().allow(null).allow('').label('Notes'),
        official_additional_role_id : Joi.number().optional().allow(null).label('Official additional role id'),
	}).or('schedule_name', 'work_status', 'rank', 'official_additional_role_id', 'additional_restrictions'),

	profile: Joi.object().keys({
		is_official 				    : Joi.boolean().label('Is Official').when(Joi.ref('is_staff'), {
                                                is 		: false,
                                                then 	: Joi.boolean().required().valid(true)
                                            }),
		is_staff 					    : Joi.boolean().label('Is Staff'),
		region 						    : Joi.string().length(2).label('USAV Region')
                                            .when(Joi.ref('sanctioned_by_usav'), {
                                                is: true,
                                                then: Joi.string().required(),
                                                otherwise: Joi.string().optional().allow(null, '')
                                            }),
		usav_num 					    : Joi.when('sanctioned_by_usav', {
		                                    is: true,
                                            then: Joi.when('country', {
                                                is: 'US',
                                                then: Joi.string().pattern(USAV_REG_EXP).required().label('USAV'),
                                                otherwise: Joi.string().pattern(USAV_REG_EXP).optional().allow(null, '').label('USAV')
                                            }),
                                            otherwise: Joi.string().pattern(USAV_REG_EXP).optional().allow(null, '').label('USAV'),
                                        }),
		background_screening 		    : Joi.string().max(20).optional().allow(null, '').label('Background Screening'),
		address 					    : Joi.string().max(100).required().label('Address'),
		country 					    : Joi.string().length(2).optional().allow(null, '').label('Country'),
		city 						    : Joi.string().required().max(200).label('City'),
		state 						    : Joi.any().optional().label('State')
                                            .when('country', {
                                                is: 'US',
                                                then: Joi.string().length(2).required(),
                                            })
                                            .when('country', {
                                                is: 'CA',
                                                then: Joi.string().length(2).required().label('Province')
                                            }),
        zip 						    : Joi.when('country', {
                                                is: 'CA',
                                                then: CA_ZIP_VALIDATOR,
                                                otherwise: US_ZIP_VALIDATOR,
                                            }).label('Zip'),
        aau_region                      : Joi.string().label('AAU Region')
                                            .when(Joi.ref('sanctioned_by_aau'), {
                                                is: true,
                                                then: Joi.string().required(),
                                                otherwise: Joi.string().optional().allow(null, '')
                                            }),
        aau_number 					    : Joi.string().label('AAU Membership')
                                            .when('sanctioned_by_aau', {
                                                is: true,
                                                then: Joi.string().required(),
                                                otherwise: Joi.string().optional().allow(null, ''),
                                            }),
        special_sizing_requests         : Joi.string().optional().allow(null, '').label('Special Sizing Requests'),
        birthdate                       : Joi.string().required().label('Birthdate'),
        emergency_contact_name          : Joi.string().required().label('Emergency Contact Name'),
        emergency_phone                 : Joi.string().required().replace(/\D/g, '').min(10).label('Emergency Contact Phone'),
        is_staff_only                   : Joi.boolean().optional().allow(null),
        sanctioned_by_usav              : Joi.boolean().optional().allow(null),
        sanctioned_by_aau               : Joi.boolean().optional().allow(null),
        emergency_contact_relationship  : Joi.string().required().label('Emergency Contact Relationship'),
		advancement 				    : Joi.boolean().label('Candidate for Advancement')
                                            .when(Joi.ref('is_staff_only'), {
                                                is 			: false,
                                                then 		: Joi.required(),
                                                otherwise 	: Joi.optional().allow(null)
                                            }),
		rank 						    : Joi.string().label('Rank')
                                            .when(Joi.ref('is_staff_only'), {
                                                is 			: false,
                                                then 		: Joi.string().valid('UnRated', 'International', 'National', 'Jr. National',
                                                    'Regional', 'Provisional', 'In Region', 'Other'
                                                ).required(),
                                                otherwise 	: Joi.optional().allow(null)
                                            }),
        usav                            : Joi.any(),
        not_completed                   : Joi.boolean().optional().allow(null, '').label('Not completed profile'),
        aau_not_completed               : Joi.boolean().optional().allow(null, '').label('Not completed aau profile'),
        clothes                         : Joi.array().items(Joi.object().keys({
                                            size: Joi.string().required().allow(null, '').label('Clothing size'),
                                            common_item_id: Joi.string().required().label('Clothing item id'),
                                        })).optional().label('Clothes'),
        arbiter_pay_account_number      : Joi.string().optional().allow(null, '').max(50)
                                            .label('ArbiterPay Account Number'),
        arbiter_pay_username            : Joi.string().optional().allow(null, '').max(50).label('ArbiterPay Username'),
        rq_pay_username                 : Joi.string().optional().allow(null, '').max(50).label('RQ Pay Username'),
	}),
    update_additional_restrictions: Joi.object().keys({
        additional_restrictions: Joi.string().allow('', null).label('Additional Restrictions'),
    }),
}
