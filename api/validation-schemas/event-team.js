const Joi = require('joi');
const { EMAIL_REGEX } = require("../lib/joi-constants");

let manualMemberSchema = Joi.array().items(
    Joi.object().keys({
        first: Joi.string().required().label('Member first name'),
        last: Joi.string().required().label('Member last name'),
        email: Joi.string().pattern(EMAIL_REGEX).required().label('Email'),
        type: Joi.string().valid('staff', 'athlete').label('Member type'),
        staff_role: Joi.when('type', {
            is: 'staff',
            then: Joi.number().required().valid(4 /*Head Coach*/),
            otherwise: Joi.any().forbidden()
        }).label('Staffer role'),
        roster_team_id: Joi.any().optional(),
        master_team_id: Joi.any().optional()
    })
).min(1).label('Members');

module.exports = {
    manualMemberSchema
}
