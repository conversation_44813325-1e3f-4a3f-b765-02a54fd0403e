'use strict';

const fs = require('fs').promises;
const qr = require('qr-image');
const FileUploadService = require('../services/FileUploadService');

class QRCodeGenerator {
    constructor(
        {
            s3Path,
            localPath,
        }
    ) {
        this.s3Path = s3Path.replace(/\/$/, '');
        this.localPath = localPath;
    }

    async createQRCode(imageContent, imageFileName, maxUploadAttempts) {
        const imageFileBytes = this._generateQRCodeFile(imageContent);

        for(let attemptNumber = 1; attemptNumber <= maxUploadAttempts; attemptNumber++) {
            try{
                return await this._uploadQRCode(imageFileName, imageFileBytes);
            }
            catch(err) {
                if(attemptNumber === maxUploadAttempts) {
                    await this._saveQRCodeLocally(imageFileName, imageFileBytes);
                    throw err;
                }
            }
        }
    }

    async _uploadQRCode(imageFileName, imageFileBytes) {
        const S3Path = `${this.s3Path}/${imageFileName}`;

        await FileUploadService.uploadFileToS3(imageFileBytes, 'image/png', S3Path);

        return sails.config.urls.home_page.baseUrl + S3Path;
    }

    async _saveQRCodeLocally(imageFileName, imageFileBytes) {
        const localPath = `${this.localPath}/${imageFileName}`;

        await fs.writeFile(localPath, imageFileBytes, {mode: 0o664, flag: 'w'});
    }

    _generateQRCodeFile(imageContent) {
        return qr.imageSync(imageContent, {
            type: 'png',
            size: 6,
            margin: 1
        });
    }

    getQRCodeFilePath(imageFileName) {
        return `${this.s3Path}/${imageFileName}`;
    }
}

module.exports = QRCodeGenerator;
