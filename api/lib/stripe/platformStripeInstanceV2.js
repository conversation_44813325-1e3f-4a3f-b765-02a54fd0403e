'use strict';

const
    argv            = require('optimist').argv,
    settingsRow     = argv.prod?'stripe_connect':'stripe_connect_dev';

const STRIPE_API_VERSION = '2020-08-27';

//Delete these file after Stripe update to the latest version

let stripeSingleton = (function (dbSettingsKey, apiVersion) {
    let stripeInstance;

    function initPlatformStripe () {
        return Db.query(`SELECT "value" FROM "settings" WHERE "key" = '${dbSettingsKey}'`)
        .then(function getPlatformKeys (result) {
            let row          = _.first(result.rows),
                platformData = row && row.value;

            if(_.isEmpty(platformData)) {
                throw {
                    text: 'Stripe Platform settings error (Empty value returned from settings table)'
                }
            } else if (!platformData.secret_key) {
                throw {
                    text: 'No Platform Secret Key found'
                }
            } else {
                loggers.debug_log.verbose('Initializing platform stripe. Api ver.', apiVersion);
                let stripe = require('stripe')(platformData.secret_key, { apiVersion, maxNetworkRetries: 2 });
                return (stripeInstance = stripe);
            }
        })
    }

    return {
        getStripeInstance: function () {
            return (!stripeInstance)
                ?initPlatformStripe()
                :Promise.resolve(stripeInstance)
        }
    }
})(settingsRow, STRIPE_API_VERSION);

module.exports = stripeSingleton;
