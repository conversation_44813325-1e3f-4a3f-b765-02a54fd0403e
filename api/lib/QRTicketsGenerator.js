'use strict';

const
    knox        = require('knox'),
    qr          = require('qr-image'),
    crypto      = require('crypto'),
    optimist    = require('optimist'),
    luhn        = require('./Luhn'),
    fs          = require('mz/fs'),
    path        = require('path'),

    moment      = require('moment'),
    _           = require('lodash'),
    QRCodeGenerator = require('./QRCodeGenerator'),

    S3_PATH                     = '/images/qrcode/',
    QR_CONTENT_FORMAT           = '{version}{event_id}{barcode}{timestamp}{tickets}';

const qrCodeGenerator = new QRCodeGenerator({
    s3Path: S3_PATH,
    localPath: path.resolve(__dirname, '..', '..', '..', 'data', 'tickets', 'qrcodes'),
});

const toBase64 = (input) => Buffer.from(input).toString('base64');

module.exports = {
    generate: function (data) {
        return Promise.resolve().then(() => {
            if (!data.qr_content) {
                return Promise.reject(new Error('QR content required'));
            }

            let imageName;

            if (data.imageName) {
                imageName = data.imageName;
            } else {
                imageName = this.generateHash(data, true);
            }

            return this.uploadQRImage({
                imageName,
                imageContent: data.qr_content,
                s3Config: data.s3Config,
            })
            .then(() => imageName)
        });
    },

    uploadQRImage: async function (data) {
        const {imageName, imageContent, retryCount = 1} = data;
        const maxUploadAttempts = 1 + retryCount;
        const imageFileName = `${imageName}.png`;

        await qrCodeGenerator.createQRCode(imageContent, imageFileName, maxUploadAttempts);

        return imageName;
    },

    // covered 😄👍
    // 17.09.2015: content compatible to SWT API V3
    generateContent: function (params = {}, version = '4.1', isNamedTicketsMode) {
        if(_.isEmpty(params)) {
            throw new Error('Params can\'t be empty');
        }

        let purchaserName;

        let {tickets, event_id, ticket_barcode, purchase_timestamp, purchaser_first, purchaser_last} = params;

        /**
         * tickets min length = 3 (when event has one ticket type)
         * first char       - ticket types count
         * two chars  - bought tickets number (with leading zero) for some ticket type
         * two chars  - bought tickets number (with leading zero) for next ticket type
         * ....
         * https://docs.google.com/document/d/136fTZ-0pqtLEo78FRddmHBVxH4qq67JGqaVOqn8ah7M/edit#heading=h.ndmuyl8rvuwf
         */
        if(!tickets || !_.isString(tickets) || tickets.length < 3) {
            throw new Error('Tickets param should be string and can\'t be empty');
        }

        if(!event_id || _.isNaN(Number(event_id)) || Number(event_id) < 0) {
            throw new Error('Event ID param should be positive integer and can\'t be empty');
        }

        if(ticketBarcodeIsNotValid(ticket_barcode)) {
            throw new Error('Ticket Barcode is not valid or empty');
        }

        // unix timestamp in seconds (has 10 digits length)
        if(purchaseTimestampIsNotValid(purchase_timestamp)) {
            throw new Error('Purchase Timestamp is not valid or empty');
        }

        if(!_.isString(version) || !/^[0-9]/.test(version)) {
            throw new Error('Invalid version parameter value');
        }

        let qrContentData = {
            version     : version.substring(0, 1),
            event_id    : quantityFormat(event_id),
            barcode     : ticket_barcode,
            timestamp   : purchase_timestamp,
            tickets     : tickets
        };

        if(isNamedTicketsMode) {
            if(!_.isString(purchaser_first)) {
                throw new Error('Purchaser First is not valid or empty');
            }

            if(purchaser_first.length < 2) {
                loggers.errors_log.error('Purchaser First length is shorter than 2');
            }

            if(!_.isString(purchaser_last)) {
                throw new Error('Purchaser Last is not valid or empty');
            }

            if(purchaser_last.length < 2) {
                loggers.errors_log.error('Purchaser Last length is shorter than 2');
            }

            purchaserName = `${purchaser_first}_${purchaser_last}`.toUpperCase();
        }

        let code =
            QR_CONTENT_FORMAT.replace(/{([a-z_]*)}/g, (wholeMatching, groupMatch) => {
                return qrContentData[groupMatch]
            });

        return code + luhn.getControlDigit(code) + (version === '4.1' && isNamedTicketsMode ? toBase64(purchaserName) : '');

    },
    // covered 😄👍
    generateHash: function (params = {}, applyFormatting) {
        if(_.isEmpty(params)) {
            throw new Error('Params can\'t be empty');
        }

        let {ticket_barcode, purchase_id, user_id, event_id} = params;

        if(ticketBarcodeIsNotValid(ticket_barcode)) {
            throw new Error('Ticket Barcode is not valid or empty');
        }

        if(!event_id || _.isNaN(Number(event_id)) || Number(event_id) < 0) {
            throw new Error('Event ID is not valid or empty');
        }

        if(!purchase_id || _.isNaN(Number(purchase_id)) || Number(purchase_id) < 0) {
            throw new Error('Purchase ID is not valid or empty');
        }

        if(!user_id || _.isNaN(Number(user_id)) || Number(user_id) < -1) {
            throw new Error('User ID is not valid or empty');
        }

        if(applyFormatting && !_.isBoolean(applyFormatting)) {
            throw new Error('Apply Formatting should be boolean');
        }

        let hash = _genHash(ticket_barcode, purchase_id, user_id, event_id);

        return applyFormatting 
                ? _imageName(ticket_barcode, hash)
                : hash;
    },
    generateImageName: function (barcode, hash) {
        return _imageName (barcode, hash);
    },
    removeTicketImage: function (data, cb) {
        return new Promise((resolve, reject) => {
            let hash        = _genHash(data.ticket_barcode, data.purchase_id, data.user_id, data.event_id),
                imageName   = `${S3_PATH}${_imageName(data.ticket_barcode, hash)}.png`;

            const BUCKET_SETTINGS = (optimist.argv.prod)
                                    ? sails.config.s3.ticketsBucket
                                    : sails.config.s3.ticketsBucketDev;
            
            knox.createClient(BUCKET_SETTINGS)
            .deleteFile(imageName, function (err, resp) {
                if(err) {
                    reject(err);
                } else {
                    loggers.debug_log.verbose('Removing image', imageName, '->', resp.statusCode);
                    resolve(imageName);
                }
            })
        }).then(imageName => {
            if(cb) {
                cb(null, imageName);
            } else {
                return imageName;
            }
        }).catch(err => {
            if(cb) {
                cb(err);
            } else {
                return Promise.reject(err);
            }
        });
    },

    // covered 😄👍
    generateQRCodeItemsString: function (ticketsList, receipt) {
        if(!_.isArray(ticketsList) || !ticketsList.length) {
            throw new Error(`Ticket List should be array and can't be empty`);
        }

        if(!_.isArray(receipt) || !receipt.length) {
            throw new Error(`Receipt should be array and can't be empty`);
        }

        let findReceiptItem = sortOrderIndex => {
            if(!_.isNumber(sortOrderIndex) || sortOrderIndex < 0) {
                throw new Error(`Sort Order Index should be a positive integer`);
            }

            for (let item of receipt) {
                if(!_.isNumber(item.order) || item.order < 0) {
                    throw new Error(`Item Order Index should be a positive integer`);
                }

                if(item.order === sortOrderIndex) {
                    return item.quantity;
                }
            }
            return 0;
        };  

        let resultStr = '' + ticketsList.length;  

        for (let ticket of ticketsList) {
            let receiptItemQty = findReceiptItem(ticket.sort_order);
            resultStr += (receiptItemQty) ? quantityFormat(receiptItemQty) : '00';
        }   

        return resultStr;
    },

    generateSWTContent (data, version, isNamedTicketsMode) {
        let ticketsQRList =
            this.generateQRCodeItemsString(data.event_tickets, data.purchase_tickets);

        return this.generateContent({
            tickets 				: ticketsQRList,
            event_id 				: data.event_id,
            ticket_barcode 			: data.ticket_barcode,
            purchase_timestamp 		: data.purchase_timestamp,
            purchaser_first         : data.first,
            purchaser_last          : data.last
        }, version, isNamedTicketsMode)
    }
}

function quantityFormat(q) {
    return (q < 10)?('0' + q):q;
}

function purchaseTimestampIsNotValid (purchase_timestamp) {
    if(!purchase_timestamp || _.isNaN(Number(purchase_timestamp)) || String(purchase_timestamp).length !== 10) {
        return true;
    }

    let timestampDate = moment.unix(purchase_timestamp);

    if(!timestampDate.isValid()) {
        return true;
    }

    // timestamp should be between '"current season" - 2 years' and NOW
    let now     = moment().add(1, 'second').startOf('second'); // round to seconds because purchase_timestamp rounded to seconds
    let minDate = moment(`${
        global.sails
            ? sails.config.sw_season.current
            : (new Date()).getUTCFullYear()
    }/01/01`, 'YYYY/MM/DD').subtract(2, 'y');

    let purchaseTimeStampInPast     = minDate.isAfter(timestampDate);
    let purchaseTimeStampInFuture   = now.isBefore(timestampDate);

    return purchaseTimeStampInPast || purchaseTimeStampInFuture;
}

function ticketBarcodeIsNotValid(ticket_barcode) {
    return !ticket_barcode || _.isNaN(Number(ticket_barcode)) || String(ticket_barcode).length !== 9
        || Number(ticket_barcode) < 0;
}

function _genHash (barcode, purchase_id, user_id, event_id) {
    var hash = crypto.createHash('sha256').update(
        `${barcode}${purchase_id}${user_id}${event_id}`
    ).digest('hex');
    return hash;
}

function _imageName (barcode, hash) {
    if(!hash || hash.length < 64) {
        return null;
    }
    return `${hash.substring(0, 32)}-${barcode}-${hash.substring(32)}`;
}
