'use strict';

const { participant: participantUsavSchema } = require('../../../validation-schemas/participant-usav');

module.exports = {
    // post /api/tickets/user/check
    userEmailCheck: function (req, res) {
        let $email = req.body.email;

        if(!$email) {
            return res.validation('Email is a required field');
        }

        Db.query(
            `SELECT u.user_id 
             FROM "user" u 
             WHERE LOWER(TRIM(u.email)) = LOWER(TRIM($1))`,
            [$email]
        ).then(result => {
            res.status(200).json({ exists: (result.rowCount > 0) })
        }).catch(err => {
            res.customRespError(err); 
        })
    },

    // POST /api/tickets/usav/check
    participantUSAVCheck: async function (req, res) {
        try {
            let member = {};
            let result = participantUsavSchema.validate(req.body);

            if (result.error) {
                const lastErrorMessageIndex = result.error.details.length - 1;

                return res.validation(result.error.details[lastErrorMessageIndex].message);
            } else {
                member = Object.assign({},
                    { birthdate: { date: result.value.birthdate } },
                    _.omit(result.value, ['birthdate'])
                );
            }

            let validationResult = await SportEngineMemberService.validation.processMember(
                member, SportEngineMemberService.validation.MEMBER_TYPE.junior
            )

            if(!_.isEmpty(validationResult.validationError)) {
                return res.validation(validationResult.validationError);
            }

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    }
}
