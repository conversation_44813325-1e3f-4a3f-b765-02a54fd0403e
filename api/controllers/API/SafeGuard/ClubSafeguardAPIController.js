'use strict';

module.exports = {
	// POST /api/safeguard
	saveRequest: function (req, res) {
		var $requestJSON 	= req.body.JSONrequest,
			$requestXML 	= req.body.request,
			$responseXML 	= req.body.response,
			$method 		= req.body.method,
			$ipAddress 		= req.headers['x-forwarded-for'] || req.connection.remoteAddress || null,
			sqlQuery;

		if(_.isEmpty(req.body)) {
			return res.validation('No data passed');
		}

		sqlQuery = squel.insert().into('safeguard_request').set('ip', $ipAddress);

		if($requestJSON) {
			sqlQuery.set('request_json', $requestJSON);
		}

		if($requestXML) {
			sqlQuery.set('request_xml', $requestXML);
		}

		if($responseXML) {
			sqlQuery.set('response_xml', $responseXML);
		}

		if($method) {
			sqlQuery.set('method', $method);
		}

		Db.query(sqlQuery)
		.then(function () {
			res.ok();
		}).catch(function (err) {
			res.customRespError(err, {status: 400})
		})
	}
}
