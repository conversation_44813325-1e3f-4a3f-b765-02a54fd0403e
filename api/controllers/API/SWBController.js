'use strict';

/**
 * Controller to export data to SW Builder app
 *
 */

 var argv = require('optimist').argv;

// Service function to convert UTC date to SQL format "YYYY-MM-DD HH:MM:SS"
function _utc_to_sql(timestamp) {
    if (!timestamp) return null;
    try {
        var ts = new Date(parseInt(timestamp, 10));
        return ts.toISOString().slice(0, 19).replace('T', ' ');
    } catch (err) {
        console.log(err);
        return null;
    }
}

module.exports = {

    eventlist: function (req, res) {
        // Check Authentication
        //if (_auth_api(req, res)) return;

        //var now = new Date(); // Get current server time
        //var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format
        var query =
                `SELECT 
                   e.event_id, 
                   e.name, 
                   e.long_name, 
                   e.city, 
                   e.state,
                   e.country, 
                   e.region, 
                   e.location, 
                   e.sport_id, 
                   e.has_female_teams, 
                   e.has_male_teams, 
                   e.has_coed_teams, 
                   e.date_start, 
                   e.date_end,
                   e.event_owner_id, 
                   e.admin_security_pin,
                   e.modified,
                   (e.teams_settings->>'manual_teams_addition')::BOOLEAN IS TRUE "manual_teams_addition",
                   e.sport_variation_id,
                   e.esw_id
                 FROM event e 
                 WHERE e.deleted IS NULL
                   AND e.allow_teams_registration IS TRUE
                   AND e.is_test = FALSE `
                // Doug asked to return not published events for SWB API
                //'   AND e.published = TRUE ' +
                //'   AND e.live_to_public IS TRUE'
                ;

        Db.query(query).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.status(500).json(err.error)
        });
    },

    // GET /api/swb/v1/divisions/:event/:timestamp
    divisions: function (req, res) {
        // Check Authentication
        //if (_auth_api(req, res)) return;

        var eventId = req.params.event;
        var timestamp = req.params.timestamp;
        var now = new Date(); // Get current server time
        //var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        // Getting Divisions Info
        var query =
            'SELECT ' +
            '   d.division_id, '+
            '   d.name, '+
            '   d.short_name, '+
            '   d.gender, '+
            '   d.level, '+
            '   d.max_teams, '+
            '   d.modified '+
            ' FROM division d ' +
            ' WHERE d.event_id = $1 ';
        var params = [eventId];
        // Adding after timestamp condition
        if (_utc_to_sql(timestamp)) {
            query = query + ' AND d.modified > $2 ';
            params.push(_utc_to_sql(timestamp));
        }

        query = query + ' ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level';

        Db.query(query, params).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.status(200).json({error: err.toString() + ' SQL:' + query});
        })
    },

    // GET /api/swb/v1/clubs/:event/:timestamp
    clubs: function (req, res) {
        // Check Authentication
        //if (_auth_api(req, res)) return;

        var eventId = req.params.event;
        var timestamp = req.params.timestamp;
        var now = new Date(); // Get current server time
        //var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        // Getting Clubs Info
        var query =
            `SELECT rc.roster_club_id,
                   rc.master_club_id,
                   rc.club_name,
                   rc.region,
                   rc.state,
                   rc.country,
                   rc.code,
                   GREATEST(MAX(rt.modified), rc.modified) "modified"
            FROM roster_club rc
                     JOIN roster_team rt
                                ON rt.roster_club_id = rc."roster_club_id"
                                    AND rt.event_id = rc.event_id
                                    AND rt.deleted IS NULL
            WHERE rc.deleted IS NULL
              AND rc.event_id = $1`;
        var params = [eventId];

        query +=
            `
            GROUP BY rc.roster_club_id`;

        // Adding after timestamp condition
        if (_utc_to_sql(timestamp)) {
            query = query + ' HAVING (SELECT MAX(rt.modified) > $2 OR rc.modified > $2) ';
            params.push(_utc_to_sql(timestamp));
        }

        Db.query(query, params).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.status(200).json({error: err.toString() + ' SQL:' + query});
        })
    },

    // GET /api/swb/v1/teams/:event/:timestamp
    // GET /api/swb/v1/teams/:event/:timestamp/:team
    teams: function (req, res) {
        // Check Authentication
        //if (_auth_api(req, res)) return;

        var eventId = req.params.event;
        var timestamp = req.params.timestamp;
        var teamId = req.params.team;
        var now = new Date(); // Get current server time
        //var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

            // Getting Teams Info
            var query =
                'SELECT ' +
                '   rt.roster_team_id, ' +
                '   rt.master_team_id, ' +
                '   rt.roster_club_id, ' +
                '   rt.team_name, ' +
                '   rt.age, ' +
                '   rt.division_id, ' +
                '   d.name division_name, ' +
                '   rt.organization_code, ' +
                '   rt.gender, ' +
                '   rt.date_accepted, ' +
                '   rt.status_entry, ' +
                '   rt.modified ' +
                '  FROM roster_team rt ' +
                '  LEFT JOIN division d ON d.division_id = rt.division_id ' +
                '   WHERE rt.deleted IS NULL ' +
                '     AND rt.event_id = $1 ';
        // query = query + ' AND rt.status_entry = 12 ';
        var params = [eventId];
            // Adding after timestamp condition
            if (_utc_to_sql(timestamp)) {
                params.push(_utc_to_sql(timestamp));
                query = query + ' AND rt.modified > $' + params.length;
            }
            if (teamId) {
                params.push(teamId);
                query = query + ' AND rt.roster_team_id = $' + params.length;
            }

            //return cb(query);
            Db.query(query, params).then(function (result) {
                result.rows.forEach(function(row) {
                    row.team_name = row.team_name.replace(/(\r\n|\n|\r)/gm,"");
                });

                res.status(200).json(result.rows);
            }).catch(err => {
                res.status(200).json({error: err.toString() + ' SQL:' + query});
            })
    },

    // GET /api/swb/v1/athletes/:event/:timestamp
    // GET /api/swb/v1/athletes/:event/:timestamp/:rowId
    athletes: function (req, res) {
        // Check Authentication
        //if (_auth_api(req, res)) return;

        var eventId = req.params.event;
        var timestamp = req.params.timestamp;
        var athleteId = req.params.rowId;
        var now = new Date(); // Get current server time
        //var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        // Getting Teams Info
        var query =
            'SELECT ma.* ' +
            '  FROM master_athlete ma ' +
            '  LEFT JOIN roster_team rt ON rt.master_team_id = ma.master_team_id ' +
            '   WHERE rt.event_id = $1 ';
        var params = [eventId];
        // Adding after timestamp condition
        if (timestamp) {
            params.push(_utc_to_sql(timestamp));
            query = query + ' AND rt.modified > $' + params.length;
        }
        if (athleteId) {
            params.push(athleteId);
            query = query + ' AND ma.master_athlete_id = $' + params.length;
        }

        //return cb(query);
        Db.query(query, params).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.status(200).json({error: err.toString() + ' SQL:' + query});
        })
    },


    // GET /api/swb/v1/officials/:event/:timestamp
    // GET /api/swb/v1/officials/:event/:timestamp/:rowId
    officials: function (req, res) {
        // Check Authentication
        //if (_auth_api(req, res)) return;

        var eventId = req.params.event;
        var timestamp = req.params.timestamp;
        var rowId = req.params.rowId;
        var now = new Date(); // Get current server time
        //var serverTime = now.toJSON(); // Convert it date string in ISO 8601 format

        // Getting Officials Info
        var query =
            'SELECT ' +
            ' eo.official_id, ' +
            ' eo.event_official_id, ' +
            ' u.first, ' +
            ' u.last, ' +
            ' o.country, ' +
            ' o.state, ' +
            ' o.city, ' +
            ' o.zip, ' +
            ' o.address, ' +
            ' o.region, ' +
            ' o.usav_num, ' +
            ' o.background_screening, ' +
            ' o.advancement, ' +
            ' o.rank, ' +
            ' eo.payment_option, ' +
            ' eo.work_status, ' +
            ' eo.head_official, ' +
            ' eo.is_staff, ' +
            ' eo.is_official, ' +
            ' eo.travel_method, ' +
            ' eo.able_to_transport_others, ' +
            ' eo.need_hotel_room, ' +
            ' eo.roommate_preference, ' +
            ' eo.schedule_availability, ' +
            ' TO_CHAR(eo.departure_datetime, \'YYYY-MM-DD HH24:MI:SS\') "departure_datetime", ' +
            ' eo.additional_restrictions,' +
            ' eo.modified event_official_modified, ' +
            ' o.modified official_modified, ' +
            ' u.modified user_modified ' +
            '  FROM event_official eo ' +
            '  LEFT JOIN official o ON o.official_id = eo.event_official_id ' +
            '  LEFT JOIN "user" u ON u.user_id = o.user_id ' +
            '   WHERE eo.event_id = $1 ';
        var params = [eventId];
        // Adding after timestamp condition
        if (timestamp) {
            params.push(_utc_to_sql(timestamp));
            query = query + ' AND ( o.modified > $' + params.length + ' OR eo.modified > $' + params.length + ' )';
        }
        if (rowId) {
            params.push(rowId);
            query = query + ' AND eo.event_official_id = $' + params.length;
        }

        //return cb(query);
        Db.query(query, params).then(function (result) {
            res.status(200).json(result.rows);
        }).catch(err => {
            res.status(200).json({error: err.toString() + ' SQL:' + query});
        })
    },

    // GET /api/swb/v1/event/:event/clear-chache
    clearEventCache: function (req, res) {
        var $eventId = parseInt(req.params.event, 10);

        if(!$eventId) return res.validation('Invalid Event identifier passed');

        Db.query(
            `SELECT e.esw_id, e.event_id FROM "event" e WHERE e.event_id = $1`,
            [$eventId]
        ).then(function (result) {
            var event = _.first(result.rows);

            if(_.isEmpty(event)) {
                throw {
                    validation: `Event with id #${$eventId} not found`
                }
            }

            return event;
        }).then(function (eventData) {
            var keysPrefix = '/api/esw/';

            const prefixes = {
                eswPrefix       : (eventData.esw_id)
                                    ?(keysPrefix + eventData.esw_id + '*')
                                    :null,
                swPrefix        : (keysPrefix + eventData.event_id + '*'),
                eventsPrefix    : (keysPrefix + 'events')
            };

            return Promise.all([
                EswCache.clearEvent(eventData.esw_id),
                EswCache.clearEventsList(),
                Cache.removeByMask(prefixes.swPrefix),
                Cache.removeByMask(prefixes.eswPrefix),
                Cache.removeByMask(prefixes.eventsPrefix),
            ])
        }).then(function (removedKeysCount) {
            loggers.debug_log.verbose(
                'Removed Redis Cache:',
                    removedKeysCount[0], 'for "event_id",',
                    removedKeysCount[1], 'for "esw_id",',
                    removedKeysCount[2], 'for events list');
            res.ok()
        }).catch(function (err) {
            res.customRespError(err, {status: 400});
        })
    },
    // GET /api/swb/v1/journal_count/:event
    journal_count: function (req, res) {
        let $eventId = parseInt(req.params.event, 10);

        if(!$eventId) return res.validation('Invalid Event identifier passed');

        Db.query(
            `SELECT COUNT(*) FROM event_journal WHERE event_id = $1`, [$eventId]
        ).then(result => {
            let count = !_.isEmpty(result.rows[0]) && result.rows[0].count;

            res.status(200).json({count: count || 0});
        }).catch( err => {
            res.customRespError(err, {status: 400});
        });
    },
    // POST /api/swb/v1/teams/:event/add
    addTeamsManually: function (req, res) {
        let eventID   = Number(req.params.event);
        let teamsData = req.body;

        return EventTeamService.manual_teams_addition.addTeamsManually(eventID, teamsData)
            .then(() => {
                res.status(200).json({ success: true });
            }).catch(err => {
                res.status(200).json({
                    success : false,
                    error   : err.validation || err.message
                });
            });
    }
};
