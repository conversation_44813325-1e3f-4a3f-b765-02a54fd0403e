'use strict';

module.exports = {

    //get /api/usav-admin/events
    getEvents: function (req, res) {
        let params = req.query;

        USAVAdminService.events.getUSAVEventsList(params)
            .then(events => {
                res.status(200).json({events})
            })
            .catch(err => res.customRespError(err))
    },

    //get /api/usav-admin/event-types
    getEventTypes: function (req, res) {
        USAVAdminService.events.getEventTypes()
            .then(types => {
                res.status(200).json({types})
            })
            .catch(err => res.customRespError(err))
    },

    //get /api/usav-admin/event/:event/members
    getMembersData: function (req, res) {
        let eventID = Number(req.params.event);
        let params  = req.query;

        USAVAdminService.dashboard.getMembersData(eventID, params)
            .then(members => {
                res.status(200).json({members});
            })
            .catch(err => res.customRespError(err))
    },

    //get /api/usav-admin/event/:event/club/:club/find
    getClubData: function (req, res) {
        let rosterClubID    = Number(req.params.club);
        let eventID         = Number(req.params.event);

        USAVAdminService.club.getClubData(rosterClubID, eventID)
            .then(club => {
                res.status(200).json({club});
            })
            .catch(err => res.customRespError(err))
    },

    getMemberInfo: function (req, res) {
        const member = req.param('member');

        const allowMembers = {
            'staff': 'roster_staff_role',
            'athlete': 'roster_athlete',
        };

        USAVAdminService.info.getInfo(req.params.id, allowMembers[member])
            .then((response => res.status(200).json({ information: response })))
            .catch(err => res.customRespError(err));
    },
};
