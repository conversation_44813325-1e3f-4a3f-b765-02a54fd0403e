const {
    TILLED_ACCOUNT_TYPE,
} = require('../../../constants/event-owner');


// GET /api/eo/tilled-accounts/:type
module.exports = {
    friendlyName: 'Tilled Accounts',
    description: 'Tilled Accounts',

    inputs: {
        type: {
            type: 'string',
            example: 'tickets',
            description: 'Source type',
            required: true,
            isIn: [TILLED_ACCOUNT_TYPE.TICKETS],
        },
        event: {
            type: 'number',
            example: 22,
            description: 'Event ID',
            required: false,
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
        unauthorized: {
            statusCode: 401,
        },
        forbidden: {
            statusCode: 403,
        },
    },

    fn: async function (inputs, exits) {
        const { type, event } = inputs;

        let eventOwnerId;

        if (event) {
            eventOwnerId = eventOwnerService.findId(event, this.req.user);
        } else if (this.req.user.event_owner_id) {
            eventOwnerId = this.req.user.event_owner_id;
        }

        if (!eventOwnerId) {
            return exits.forbidden('You have no access to this event');
        }

        try {
            const accounts = await getTilledAccounts(type, eventOwnerId);

            exits.success({
                accounts,
            });
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

function getTilledAccounts(type, eventOwnerId) {
    switch (type) {
        case TILLED_ACCOUNT_TYPE.TICKETS:
            return getTicketsTilledAccounts(eventOwnerId);
        default:
            throw { validation: 'Invalid type passed' };
    }
}

function getTicketsTilledAccounts(eventOwnerId) {
    const query = knex('tilled.account')
        .select({
            account_id: 'id',
            tilled_account_id: 'tilled_account_id',
            account_name: 'account_name',
            account_email: 'account_email',
            account_statement: 'account_statement',
            account_type: 'account_type',
            created: 'created',
            modified: 'modified',
            is_test: 'is_test',
            is_platform: 'is_platform',
        })
        .where('event_owner_id', eventOwnerId)
        .orderBy('id');

    return Db.query(query).then(({ rows }) => rows);
}
