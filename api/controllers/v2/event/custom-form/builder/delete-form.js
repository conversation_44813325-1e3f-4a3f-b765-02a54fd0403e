

//DELETE /api/custom-form-builder/event/:event/form/:form_id
module.exports = {
    friendlyName: 'Custom Event Form Deleting',
    description: 'Deletes specific custom form for a specific event',

    inputs: {
        event: {
            type: 'number',
            description: 'Event ID'
        },
        form_id: {
            type: 'number',
            description: 'Form ID'
        }
    },
    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        const { event: eventID, form_id: formID } = inputs;

        try {
            await EventService.eventCustomForm.editing.delete(eventID, formID);

            exits.success();
        } catch (err) {
            this.res.customRespError(err);
        }
    }
}
