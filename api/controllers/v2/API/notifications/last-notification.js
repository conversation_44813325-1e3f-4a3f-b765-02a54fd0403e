module.exports = {
    friendlyName: 'Get last unread notifications',

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function(inputs, exits) {
        try {
            const notification = await UserNotificationService.getLastUnreadNotification(this.req.user.user_id);

            return exits.success({ notification });
        } catch (error) {
            return this.res.customRespError(error);
        }
    }
};
