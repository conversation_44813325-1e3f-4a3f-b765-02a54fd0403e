'use strict';

const fetch       = require('node-fetch'),
      querystring = require('querystring'),
      xml         = require('nodexml');

const WP          = {
    'MBR_DATA_URI'       : 'https://webpoint.usavolleyball.org/wp15/API/MbrData.wp?APIKey=0A63B6D5-E825-454A-BE6D-1EC2BED67EA7',
    'CLUB_MBR_DATA_URI'  : 'https://webpoint.usavolleyball.org/wp15/API/MbrsData.wp?APIKey=57BC15FE-3260-4AA1-A968-A7DC236FA86F'
};

const MEMBER_ID_REGEX  = /^[0-9]{7}$/;

/*
To Replace MbrVerify (Same parameters to pull the single member)
*/

/*
To Replace ClubMbrData 
    (This has changed.  The old process required a username and password 
    for the club you were pulling, we are now using the Webpoint CompanyID for the clubs to pull the data.  
    You can also pull by team and by region if the need was there)

    These parameter fields are: ClubID= OR RegionID= OR TeamID=
*/
module.exports = {
    // get /api/admin/webpoint2/member/:usav/find
    findUSAVData: function (req, res) {
        let MemberID = req.params.usav;

        if(!MEMBER_ID_REGEX.test(MemberID)) return res.validation('Invalid USAV Member ID');

        let options = {
            method: 'POST',
            body: querystring.stringify({ MemberID }),
            headers: {
                "Content-type": "application/x-www-form-urlencoded; charset=UTF-8"
            }
        };

        fetch(WP.MBR_DATA_URI, options)
            .then(parseWebpointResponse)
            .then (member => res.status(200).json({ member }))
            .catch(err => res.customRespError(err));
    },

    // get /api/admin/webpoint2/clubmbr/find[?ClubID,TeamID]
    findClubMembersUSAVData: function (req, res) {
        let ClubID = req.query.ClubID || null;
        let TeamID = req.query.TeamID || null;

        if(!ClubID && !TeamID) return res.validation('ClubID or TeamID must be provided');

        let options = {
            method  : 'POST',
            body: querystring.stringify(ClubID ? { ClubID } : { TeamID }),
            headers: {
                "Content-type": "application/x-www-form-urlencoded; charset=UTF-8"
            }
        };

        fetch(WP.CLUB_MBR_DATA_URI, options)
            .then(parseWebpointResponse)
            .then(members => res.status(200).json({ members }))
            .catch(err => res.customRespError(err));
    }
};

function parseWebpointResponse (response) {
    return response.text()
        .then(text => {
            try {
                // this is ideal situation: we've got JSON response !!!
                return Promise.resolve(JSON.parse(text));
            }
            catch (e) {
                // sometime we got back XML object or even plain text
                // both possibly mean some error occured
                let res = xml.xml2obj(text);
                
                if(res['RESPONSE'] && res['RESPONSE']['Code'] != '0' && res['RESPONSE']['Msg']) {
                    return Promise.reject(new Error(res['RESPONSE']['Msg']));
                } 

                /* some errors are of form 
                    [ Error 1 - No matching records found ]
                    [ Error 3 - Insufficient search parameters provided. ]
                    [ Error 309 - No members found ]
                */ 
                
                let errorPattern = /(\[ Error [^\]]+\])/g;
                let error = _.first(res.toString().match(errorPattern));

                if(error) {
                    return Promise.reject(new Error(error));
                }

                // failed to humanize parse error
                return Promise.reject(e);
            }
        })
};
