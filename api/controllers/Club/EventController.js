'use strict';

// move ComEventsController here
module.exports = {
    all: function (req, res) {
        // used in two tabs: SW events and my teams events
        return res.ok();
    },    
    eventGeneralInfo: function (req, res) {
        return res.ok();
    },
    exportResults: function (req, res) {
        // export results in xls xlsx csv formats
        return res.ok();
    },
    // get /api/club/event/:event/teams_list
    entryList: function (req, res) {
        var $event_id = parseInt(req.params.event, 10);
        if(!$event_id) return res.validation('Invalid event identifier')

        Db.query(ENTRY_LIST_SQL, [$event_id])
        .then(result => {
            let divisions = result.rows,
                accepted    = 0,
                entered     = 0,
                maximum     = 0;

            divisions.forEach(d => {
                accepted    += d.accepted_teams_count;
                entered     += d.entered_teams_count;
                maximum     += (d.maximum || 0);
            });

            res.status(200).json({ divisions, accepted, entered, maximum });
        }).catch(err => {
            res.customRespError(err);
        });
    }
}

var ENTRY_LIST_SQL = 
'select  \
    d.name "division_name", d.gender, d.division_id,  \
    coalesce(nullif(d.max_teams, 0), d.max_teams) "maximum", \
    ( \
        case \
            when (e.clubs_teams_visibility::text = \'hidden\') then \'[]\'::json \
            else ( \
                select coalesce(array_to_json(array_agg(row_to_json(t))), \'[]\'::json) \
                from ( \
                    select  rt.gender, rt.team_name, rc.club_name, \
                            (rt.status_entry = 12) "accepted" \
                    from roster_team rt \
                    left join roster_club rc \
                        on rc.roster_club_id = rt.roster_club_id \
                    where rt.division_id = d.division_id   \
                        and rt.event_id = d.event_id   \
                        and rt.deleted is null  \
                        and rt.status_entry = any (  \
                            (case  \
                                when e.clubs_teams_visibility::text = \'all\' then \'{12, 13, 14}\' \
                                else \'{12}\'  \
                            end)::integer[]\
                        ) \
                    order by rt.team_name asc, rt.status_entry asc  \
                ) "t" \
            ) \
        end \
    ) "teams",  \
    (   \
        select \
            (case  \
                when e.show_number_of_teams_for_cd IS TRUE \
                then count(a_rt.roster_team_id) \
                else 0  \
            end)\
        from roster_team a_rt  \
        where a_rt.division_id = d.division_id  \
            and a_rt.event_id = d.event_id  \
            and a_rt.deleted is null  \
            and a_rt.status_entry = 12  \
     )::int "accepted_teams_count",  \
    (   \
        select  \
        (case  \
            when e.show_number_of_teams_for_cd IS TRUE \
            then count(e_rt.roster_team_id) \
            else 0  \
        end)\
        from roster_team e_rt  \
        where e_rt.division_id = d.division_id  \
            and e_rt.event_id = d.event_id  \
            and e_rt.deleted is null  \
            and e_rt.status_entry in (12, 13, 14)  \
     )::int "entered_teams_count"  \
from "event" e \
left join "division" d \
    on d.event_id = e.event_id \
where e.event_id = $1 \
    and e.live_to_public = true \
    and d.published = true \
group by d.division_id, e.clubs_teams_visibility, e.show_number_of_teams_for_cd \
ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level';
