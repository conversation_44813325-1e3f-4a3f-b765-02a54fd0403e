

module.exports = {
    //put /api/club/personal-info/:type(staff|athlete)/merge
    mergeFromPreviousSeason: async (req, res) => {
        const masterClubID = Number(req.session.passport.user.master_club_id);
        const type = req.params.type;

        if(!masterClubID) {
            return res.validation('You have no club created.');
        }

        try {
            let updatedMembersCount = await ClubService.membersPersonalInfo.mergePersonalInfoFromPreviousSeason(
                masterClubID, type, sails.config.sw_season.current
            );

            res.status(200).json({ count: updatedMembersCount });
        } catch (err) {
            res.customRespError(err);
        }
    }
}
