'use strict';

const S3_FOLDER         = 'teamsImport/';
const FORM_ELEMENT_NAME = 'file';

module.exports = {
    // POST /api/upload/teamsImport
	teamsImport: function (req, res) {
        let fileName;

        try {
            let initialFileName = req.file(FORM_ELEMENT_NAME)._files[0].stream.filename,
                ext             = initialFileName.substr(initialFileName.lastIndexOf('.')),
                now             = new Date().toISOString();

            fileName            = `${S3_FOLDER}${req.user.user_id}@${now.replace(/:/g, '-')}${ext}`;
        } catch (err) {
            loggers.errors_log.error(err);
        }

        FileUploadService.uploadMultipartToS3(req, FORM_ELEMENT_NAME, fileName)
        .then(() => {
            res.status(200).send('OK')
        })
        .catch(res.customRespError.bind(res));
    }
};

