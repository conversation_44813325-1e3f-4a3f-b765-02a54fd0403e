'use strict';

const swUtils = require('../../lib/swUtils');

module.exports = {
    // GET /api/event/:event/users
    all: async function (req, res) {
        let eventID = Number(req.params.event);

        if (!eventID) {
            return res.validation('Invalid event id specified');
        }

        Promise.all([
            EventUserService.getAllUsers(eventID),
            EventUserService.getEODataByEventID(eventID),
            EventUserService.getUserPermissions()
        ]).then(result => {
            let [users, eoData, permissions] = result;

            res.status(200).json({ users, eo: eoData, permissions });

        }).catch(err => res.customRespError(err));
    },
    //PUT /api/event/:event/user
    updateUserPermissions: async function (req, res) {
        let eventID         = Number(req.params.event);
        let email           = req.body.email;
        let permissions     = req.body.permissions;
        let granterUserID   = Number(req.user.user_id);
        let userID          = null;


        if (!eventID) {
            return res.validation('Invalid event id specified');
        }

        if (!email) {
            return res.validation('Invalid user email specified');
        }

        if (!permissions) {
            return res.validation('Invalid user permissions specified');
        }

        try {
            if(_.isEmpty(permissions)) {
                userID = await EventUserService.markAsDeleted(eventID, email);
            } else {
                userID = await EventUserService.updateUserPermissions(granterUserID, eventID, email, permissions);
            }

            res.status(200).json({});

            RedisService.setUserDataMonitorKeysDirty(userID)
                .catch(ErrorSender.defaultError.bind(ErrorSender));
        } catch (err) {
            res.customRespError(err)
        }
    },

    //DELETE /api/event/:event/user
    markAsDeleted: async function (req, res) {
        let eventID = Number(req.params.event);
        let email   = req.body.email;

        if (!eventID) {
            return res.validation('Invalid event id specified');
        }

        if (!email) {
            return res.validation('Invalid user email specified');
        }

        try {
            let deletedUserID = await EventUserService.markAsDeleted(eventID, email);

            if(!deletedUserID) {
                return res.validation('Event user not found');
            }

            res.status(200).json({});

            RedisService.setUserDataMonitorKeysDirty(deletedUserID)
                .catch(ErrorSender.defaultError.bind(ErrorSender));
        } catch (err) {
            res.customRespError(err);
        }
    },
    // GET /api/event/:event/users/find
    findEligibleUsers: function(req,res) {
        let eventID = Number(req.params.event);
        let search = swUtils.escapeStr(req.query.search_param || '');

        if (!eventID) {
            return res.validation('Invalid event id specified');
        }

        if (!search) {
            return res.validation('No search string provided');
        }

        EventUserService.findEligibleUsers(eventID, search)
            .then(data => {
                res.status(200).json({
                    users           : data.map(item => _.omit(item, ['total_rows'])),
                    search_limit    : EventUserService.EVENT_USERS_SEARCH_LIMIT,
                    total_rows      : (data.length !== 0 ) ? data[0].total_rows : 0
                })
            }).catch(err => {
                res.customRespError(err);
            });
    },
    // 'POST /api/event/:event/user/add'			 		
    addUserByEmail: async function(req, res) {
        let eventID         = Number(req.params.event);
        let email           = req.body.email;
        let permissions     = req.body.permissions;
        let granterUserID   = Number(req.user.user_id);

        if (!eventID) {
            return res.validation('Invalid event id specified');
        }

        if (!email) {
            return res.validation('No email provided');
        }

        if(!_.isObject(permissions) || _.isEmpty(permissions)) {
            return res.validation('No permissions provided');
        }

        try {
            let { success, userID } = await EventUserService.addUserByEmail(granterUserID, eventID, email, permissions);

            if(!success) {
                return res.validation('Insert failed');
            }

            res.status(200).json({});

            // setting all "loggedin:userID:*" >0, making user to refetch data from db
            return RedisService.setUserDataMonitorKeysDirty(userID)
                .catch(ErrorSender.defaultError.bind(ErrorSender));

        } catch (err) {
            res.customRespError(err);
        }
    },

    // GET /api/user-permissions
    getUserPermissions: function (req, res) {
        return EventUserService.getUserPermissions()
            .then(permissions => res.status(200).json({permissions}))
            .catch(res.customRespError.bind(res))
    },

    // GET /api/user-permissions/tree
    getUserPermissionsTree: function (req, res) {
        return EventUserService.getUserPermissionsTree()
            .then(permissions => res.status(200).json({permissions}))
            .catch(res.customRespError.bind(res))
    },
};
