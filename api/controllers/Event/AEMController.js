'use strict';

/* Advanced Email Module Controller */

// URLs are here: /userconfig/routes/event/aem.js

module.exports = {
	// 👍 ok
	// GET /api/event/:event/aem/init
    getInitData: function (req, res) {
        loggers.debug_log.verbose('getInitData', req.user);
        const eventID = parseInt(req.params.event, 10),
              eventOwnerID = eventOwnerService.findId(eventID, req.user);
loggers.debug_log.verbose('eventID: ', eventID, 'eventOwnerID: ', eventOwnerID);
        Promise.all([
            AEMService.getEOTmplsList(eventOwnerID, eventID),
            AEMService.getBeeEditorUID(eventOwnerID),
        ])
            .then(([template_groups]) => {
                res.status(200).json({template_groups});
            })
            .catch(res.customRespError);
    },

	// GET /api/event/:event/aem/tmpl/:tmpl
	getTmpl: function (req, res) {
		let eventID 		= req.params.event,
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user);

		AEMService.getTemplate(eventOwnerID, eventID, tmplID)
		.then(template => {

			if (template !== null) {
				delete template.email_text;
				delete template.is_valid;
				delete template.published;
			}

			res.status(200).json({ template });
		})
		.catch(res.customRespError.bind(res));
	},

	// PUT /api/event/:event/aem/tmpl/:tmpl
	saveTmpl: function (req, res) {
		let eventID 		= req.params.event,
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user);

		AEMService.saveTemplate(eventOwnerID, eventID, tmplID, req.body)
		.then(() => {
			res.status(200).send('OK');
		})
		.catch(res.customRespError.bind(res));
	},

	// POST /api/event/:event/aem/tmpl
	createTmpl: function (req, res) {
		let event_id 		= req.params.event,
			event_owner_id 	= eventOwnerService.findId(event_id, req.user);

		AEMService.createTemplate(_.extend({ event_owner_id, event_id }, req.body))
		.then(template => {
			res.status(200).json({ template });
		})
		.catch(res.customRespError.bind(res));
	},

	// 👍 ok
	// GET /api/event/:event/aem/preview/:tmpl
	getTemplateHTML: function (req, res) {
		let eventID 		= req.params.event,
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user);

		AEMService.eoTmplPreview(eventOwnerID, tmplID)
		.then(html => {
			// res.setHeader('content-type', 'text/html');
			res.type('html');
			res.send(html);
		})
		.catch(err => {
			let message = err.message || err.validation;

			res.status(500);
			res.type('html');
			res.send(message);
		});
	},

	// 👍 ok
	// POST /api/event/:event/aem/tmpl/:tmpl/duplicate
	duplicateTemplate: function (req, res) {
		let eventID 		= parseInt(req.params.event, 10),
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user);

		AEMService.duplicateTemplate(eventOwnerID, req.body.eventIds, tmplID, req.user)
		.then(rows => {
            const currentEvent = rows.find(row => row.event_id === eventID);

            if (!_.isEmpty(currentEvent)) {
                return res.status(200).json({
                    template: {
                        id: currentEvent.email_template_id
                    }
                });
            }

            res.ok();
		})
		.catch(res.customRespError.bind(res));
	},

	// 👍 ok
	// POST /api/event/:event/aem/tmpl/:tmpl/assign
	assignToTrigger: function (req, res) {
		let eventID 		= parseInt(req.params.event, 10),
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user);

		AEMService.assignTmplToTrigger(eventOwnerID, eventID, tmplID)
		.then(() => {
			res.status(200).send('OK')
		})
		.catch(res.customRespError.bind(res));
	},

	// DELETE /api/event/:event/aem/tmpl/:tmpl/remove
	deleteTemplate: function (req, res) {
		let eventID 		= parseInt(req.params.event, 10),
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user);

		AEMService.deleteTemplate(eventOwnerID, eventID, tmplID)
		.then(() => {
			res.status(200).send('OK')
		})
		.catch(res.customRespError.bind(res));
	},

	/* === SENDING === */


	// GET /api/event/:event/aem/sending/:group/available-tmpls
	teamsTemplates: function (req, res) {
		let eventID 		= parseInt(req.params.event, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user),
			tmplsGroup 		= req.params.group;


        AEMService.getTemplatesForSending(tmplsGroup, eventOwnerID, eventID)
		.then(templates => {
			res.status(200).send({ templates });
		})
		.catch(res.customRespError.bind(res));
	},

	// POST /api/event/:event/aem/tmpl/:tmpl/send
	testSend: function (req, res) {
		let eventID 		= parseInt(req.params.event, 10),
			tmplID 			= parseInt(req.params.tmpl, 10),
			eventOwnerID 	= eventOwnerService.findId(eventID, req.user),
			userID 			= parseInt(req.user.user_id, 10),
			html 			= req.body.html,
			subject 		= req.body.subject,
			receiver 	    = req.body.receiver;

		AEMSenderService.sendTestLetter(tmplID, userID, eventID, eventOwnerID, html, subject, receiver)
		.then(({email, isTestDataUsed}) => {
			res.status(200).json({email, isTestDataUsed});
		})
		.catch(res.customRespError.bind(res));
	},

	// POST /api/event/:event/aem/:group/send-email
	generalSend: function (req, res) {
		let eventID         = Number(req.params.event);
		let tmplGroup 		= req.params.group;
        let eventOwnerID    = eventOwnerService.findId(eventID, req.user);
        let userID 			= Number(req.user.user_id);
        let templateID      = Number(req.body.template_id);
        /*
        * We omit "save_template" property, because this option is not implemented,
        * and might be not implemented at all
        */
        let letterData      = _.omit(req.body, 'filters', 'save_template');
        let filters         = req.body.filters;


        AEMSenderService.sendLetters(
            tmplGroup, userID, eventID, eventOwnerID, templateID, letterData, filters, EmailService.emailCategory.EMAIL_CATEGORIES.informational
        ).then(sent_count => {
            res.status(200).json({ sent_count });
        })
        .catch(res.customRespError.bind(res));
	},

	//GET /api/event/:event/aem/mass-send
    massSend: function (req, res) {
        const events          = eventOwnerService.findAllEvents(req.user);
        const currentSeason   = sails.config.sw_season.current;
        const availableEOIDs  = eventOwnerService.findAvailableEventsOwnersID(req.user);

        const isOnlyEvents = req.query.onlyEvents;

        AEMService.getEvents(events, currentSeason, availableEOIDs, req.params.event)
            .then(response => {
                const { events } = response;

                if (isOnlyEvents) {
                    return res.json({ events });
                }

                return res.json(response);

            })
            .catch(res.customRespError);
    },

    // GET /api/event/:event/aem/retrieve-jobs/:template
    retrieveJobs: function (req, res) {
	    const { template: templateID } = req.params;

	    if (!Number(templateID)) {
	        return res.validation('Invalid template identifier');
        }

        SystemJobService.retrieveJobs({ templateID })
            .then(response => res.json({ jobs: response }))
            .catch(res.customRespError);
    }
};
