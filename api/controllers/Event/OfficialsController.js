'use strict';

const fs                = require('fs'),
      OfficialsService  = require('../../services/OfficialsService');

const CLIENT_SQL_SERVER_ERROR_MSG = {
        'ck_is_email_notifications_receiver' : 'Only Head Official with Approved work status can be made the Email Notifications Receiver',
        'ck_is_email_contact_provider'       : 'Only Head Official with Approved work status can be made the Email Contact Provider',
        'unique_event_email_contact_provider': 'Event Contact Provider must be unique across the event'
    };

module.exports = { 
    // get /api/event/:event/officials
    index: function (req, res) {
        let eventID = parseInt(req.params.event, 10);

        OfficialsService.getAll(eventID, {role: OfficialsService.checkin.ROLE.OFFICIAL})
        .then(data => {
            res.status(200).json({
                link        : OfficialsService.SCHEDULE_APP_LINK + eventID,
                officials   : data.role_data,
                event       : data.event
            });
        })
        .catch(res.customRespError.bind(res));
    },
    // get /api/event/:event/official/:official
    find: function (req, res) {
        var official_id = parseInt(req.param('official'), 10);
        var event_id = parseInt(req.param('event'), 10);
        if(!official_id) return res.status(400).json({error: 'No official selected'});
        if(!event_id) return res.status(400).json({error: 'No event selected'});
        
        OfficialsService.findOne(event_id, official_id, function (err, data) {
            if(err) return res.serverError();
            return res.status(200).json(data);
        });       
    },
    // post /api/event/:event/official/:official/make_head
    make_head: function(req, res) {
        let eventID     = parseInt(req.params.event, 10),
            officialID  = parseInt(req.params.official, 10);

        OfficialsService.manage.makeHead(eventID, officialID)
        .then((result) => {
            res.status(200).json({
                is_email_notifications_receiver : result.is_email_notifications_receiver,
                is_email_contact_provider       : result.is_email_contact_provider
            });
        })
        .catch(res.customRespError.bind(res));
    },
    // post /api/event/:event/official/:official/demote
    remove_head: function (req, res) {
        let eventID     = parseInt(req.params.event, 10),
            officialID  = parseInt(req.params.official, 10);

        OfficialsService.manage.removeHead(eventID, officialID)
        .then((result) => {
            res.status(200).json({
                is_email_notifications_receiver : result.is_email_notifications_receiver,
                is_email_contact_provider       : result.is_email_contact_provider
            });
        })
        .catch(res.customRespError.bind(res));
    },
    // put /api/event/:event/official/:official/set_receiver?to=
    setEmailNotificationsReceiver: function (req, res) {
        let eventID = parseInt(req.params.event, 10),
            officialID = parseInt(req.params.official, 10);

        let isEmailNotificationsReceiver = null;
        
        if (req.query.to == 'true') isEmailNotificationsReceiver = true;
        if (req.query.to == 'false') isEmailNotificationsReceiver = false;

        if (!eventID) return res.validation('Invalid event id specified');
        if (!officialID) return res.validation('Invalid official id specified');
        if (toString.call(isEmailNotificationsReceiver) !== '[object Boolean]') return res.validation('Email Notifications Receiver Role flag should be a boolean');


        OfficialsService.manage.setEmailNotificationsReceiver(eventID, officialID, isEmailNotificationsReceiver)
            .then(() => {
                res.status(200).json({updated: "ok"});
            })
            .catch(err => {
                if (err.constraint && CLIENT_SQL_SERVER_ERROR_MSG[err.constraint]) {
                    res.validation(CLIENT_SQL_SERVER_ERROR_MSG[err.constraint]);
                } else {
                    res.customRespError(err);
                }
            });
    },
    //put /api/event/:event/official/:official/set_contact_provider?to=
    setEmailContactProvider : function(req, res) {
        let eventID = parseInt(req.params.event, 10),
            officialID = parseInt(req.params.official, 10);

        let isEmailContactProvider = null;

        if (req.query.to == 'true') isEmailContactProvider = true;
        if (req.query.to == 'false') isEmailContactProvider = false;

        if (!eventID) return res.validation('Invalid event id specified');
        if (!officialID) return res.validation('Invalid official id specified');
        if (toString.call(isEmailContactProvider) !== '[object Boolean]') return res.validation('Email Contact Provider Role flag should be a boolean');

        OfficialsService.manage.setEmailContactProvider(eventID, officialID, isEmailContactProvider)
        .then(() => {
            res.status(200).json({updated: "ok"});
        })
        .catch(err => {
            if (err.constraint && CLIENT_SQL_SERVER_ERROR_MSG[err.constraint]) {
                res.validation(CLIENT_SQL_SERVER_ERROR_MSG[err.constraint]);
            } else {
                res.customRespError(err);
            }
        });
    },
    // put /api/event/:event/official/:official/clinic
    setOfficialIsClinic: function (req, res) {
        const eventId = parseInt(req.params.event, 10),
            officialId = parseInt(req.params.official, 10),
            isClinic = req.body.use_clinic;

        if (!eventId) return res.validation('Invalid event id specified');
        if (!officialId) return res.validation('Invalid official id specified');
        if (typeof isClinic !== 'boolean') return res.validation('Invalid use_clinic value passed');

        OfficialsService.manage.setOfficialIsClinic(eventId, officialId, isClinic)
            .then(()=>res.status(200).json({}))
            .catch(err => res.customRespError(err));
    },
    // post /api/event/:event/official/:official/remove_receiver
    removeEmailNotificationsReceiver: function (req, res) {
        let eventID = parseInt(req.params.event, 10),
            officialID = parseInt(req.params.official, 10);
        
        if (!eventID) return res.validation('Invalid event id specified');
        if (!officialID) return res.validation('Invalid official id specified');

        OfficialsService.manage.removeEmailNotificationsReceiver(eventID, officialID)
            .then(result => {
                res.status(200).json({updated: "ok"});
            })
            .catch(err => {
                res.customRespError(err);
            });
    },
    // post /api/official/event/:event/officials/export
    export_to_excel: function (req, res) {
        let eventID         = parseInt(req.params.event, 10),
            officialsList   = req.query.officials && req.query.officials.map(id => parseInt(id, 10));

        if (!Number.isInteger(eventID)) {
            return res.validation('Invalid event identifier');
        }

        let query = OfficialsService.get_export_query(officialsList, eventID);

        XLSXService.export(query, 'officials').then(filePath => {
            res.download(filePath, err => {
                if (err) {
                    loggers.errors_log.error(err);
                    if(err.code === 'ENOENT') {
                        res.render('500', { error: 'File not found' });
                    } else {
                        res.serverError();
                    }
                } else {
                    fs.unlinkSync(filePath);
                }
            });
        }).catch(res.customRespError.bind(res));
    },

    // put /api/event/:event/official/:official/update
    updateRegInfo: function (req, res) {
        let $eventID    = Number(req.params.event),
            $officialID = req.params.official,
            $data       = req.body;

        let personName  = `${req.user.first} ${req.user.last}`;

        OfficialsService.updateRegData($eventID, $officialID, personName, $data)
        .then(() => {
            res.ok();
        }).catch(res.customRespError.bind(res));
    },
    // put /api/event/:event/officials/update
    updateRegInfoGroup: function (req, res) {
        let $eventID    = parseInt(req.params.event, 10),
            $officials  = req.body.officials,
            $data       = req.body.data;

        let personName  = `${req.user.first} ${req.user.last}`;

        OfficialsService.updateRegDataGroup($eventID, $officials, personName, $data)
        .then(() => {
            res.ok();
        }).catch(res.customRespError.bind(res));
    },
    // put /api/event/:event/officials/:official/payout
    makePayout: function(req, res) {
        const $eventId    = parseInt(req.params.event, 10),
              $officialId = parseInt(req.params.official, 10),
              $amount     = +req.body.amount || 0;
        
        if (!$eventId) return res.validation('Invalid event id specified');
        if (!$officialId) return res.validation('Invalid official id specified');

        /*  validate amount:
            1. strictly possitive 
            2. additional validation possible (ex. max value ?)
        */

        if($amount <= 0 ) return res.validation('Amount must be a positive number');

        const eventOwnerID = eventOwnerService.findId($eventId, req.user);

        StripeService.account.makePayout($eventId, eventOwnerID, $officialId, $amount)
            .then(result => {
                if (result.rowCount == 1) {
                    res.status(200).json({ success : true });
                } else {
                    throw new Error('Error making payout');
                }
            })
            .catch(err => {
                loggers.errors_log.error('Error making payout', err);
                res.customRespError(err);
            });
    },
    // get /api/event/:event/officials/payouts/history
    getPayoutsHistory: function (req, res) {
        const $eventId = parseInt(req.params.event, 10),
              $officialId = req.query.officialId 
                                ? parseInt(req.query.officialId, 10) 
                                : req.session.passport.user.official_id;

        if (!$eventId) return res.validation('Invalid event id specified');
        if (!$officialId) return res.validation('Invalid official id specified');

        StripeService.account.getPayoutsHistory($eventId, $officialId)
            .then(result => {
                res.status(200).json({
                    payouts_history: result.rows.map(row => _.omit(row, 'total')),
                    total: result.rows[0] ? result.rows[0].total : null
                });
            })
            .catch(err => {
                res.customRespError(err);
            });

    },
    // get /api/event/:event/officials/payouts/dashboard_url
    getOfficialStripeDashboardURL: function (req, res) {
        const $eventId = parseInt(req.params.event, 10),
              $officialId = req.session.passport.user.official_id;

        if (!$eventId) return res.validation('Invalid event id specified');
        if (!$officialId) return res.validation('Invalid official id specified');

        StripeService.account.getOfficialStripeDashboardURL($eventId, $officialId)
            .then(dashboardURL => {
                res.status(200).json({dashboardURL});
            })
            .catch(err => {
                res.customRespError(err);
            });
    },
    //get /api/event/:event/officials/payouts/create_express_account_url
    generateExpressAcccountCreationLink: function (req, res) {
        const $eventId = parseInt(req.params.event, 10),
            $officialId = req.user.official_id,
            $userId = req.user.user_id,
            $userEmail = req.user.email;

        if (!$eventId) return res.validation('Invalid event id specified');
        if (!$officialId) return res.validation('Invalid official id specified');


        StripeService.account.generateExpressAcccountCreationLink($eventId, $userId, $userEmail)
            .then(connectionURL => {
                if (connectionURL) {
                    res.status(200).json({connectionURL});
                }
                else {
                    throw new Error('Error generating connection URL')
                }
            })
            .catch(err => {
                res.customRespError(err);
            });

    }
    
}

