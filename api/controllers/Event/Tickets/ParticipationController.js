'use strict';

module.exports = {

    // delete /api/event/:event/camp/:code/participation
    cancelParticipation: (req, res) => {
        let eventID = Number(req.params.event);
        let barcode = Number(req.params.code);
        let tickets = req.body.tickets;

        PaymentService.tickets.participation.cancelParticipation(eventID, tickets, barcode)
            .then(purchase => {
                return Promise.all(
                    tickets.map(ticket => sendCancellationNotification(
                        eventID, ticket.purchase_ticket_id, purchase.purchase_id
                    ))
                ).then(() => {
                    res.status(200).json({});
                })
            })
            .catch(res.customRespError.bind(res))
    }

};

function sendCancellationNotification (eventID, purchaseTicketID, purchaseID) {
    let templateGroup   = AEMService.CAMP_GROUP;
    let templateType    = AEMService.triggers.CAMPS_PARTICIPATION_CANCELLATION_ACTION;
    let receiverFilters = { purchase_ticket: purchaseTicketID, payments: [purchaseID] };

    return AEMSenderService.sendTriggerNotification(templateGroup, templateType, eventID, receiverFilters);
}
