'use strict';

// GET /api/event/:event/accounting/:item{teams|tickets|booths}/totals
module.exports.getTotals = function (req, res) {
    let eventID     = Number(req.params.event);
    let paymentFor  = req.params.item;

    AccountingService.TotalsService.getTotals(eventID, paymentFor)
    .then(stats => {
        res.status(200).json({ stats });
    })
    .catch(res.customRespError.bind(res));    
}
