'use strict';

module.exports = {
    // GET /api/event/:event/accounting/escrow
    getEscrow: function (req, res) {
        let eventID = Number(req.params.event);

        return AccountingService.EscrowService.getEscrow(eventID)
            .then(escrow => {
                res.status(200).json({ escrow });
            }).catch(err => {
                res.customRespError(err);
            })
    }
};
