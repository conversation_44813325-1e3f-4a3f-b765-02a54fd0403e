'use strict';

const moment = require('moment'),
    locationSchema = require('../../validation-schemas/event-location').location,
    clothesRequirementsSchema = require('../../validation-schemas/event-schemas').clothes_requirements,
    stripeService = require('../../services/StripeService.js'),
    ESW_LINK= `${sails.config.urls.esw.baseUrl}/#/events/`,
    { EXTRA_FEE_COLLECTION_MODE } = require('../../constants/payments'),
    { RESOURCES_TO_DUPLICATE } = require('../../constants/event-duplication');
const {POINT_OF_SALES_TYPE} = require("../../constants/sales-hub");

const eventCreateSchema = require('../../validation-schemas/event').createSchema;
const eventUpdateSchema = require('../../validation-schemas/event').updateSchema;

const OFFICIALS_TEMPLATES_DEFAULTS = {
        ACCEPTED    : 16,
        APPLIED     : 11,
        DECLINED    : 18,
        WAITLISTED  : 17
    },
    DATE_FIELDS         = ['date_end', 'date_reg_close', 'date_reg_open',
        'date_start', 'roster_deadline', 'date_official_reg_open',
        'date_official_reg_close', 'online_team_checkin_end', 'online_team_checkin_start',
        'officials_hotel_date_start', 'officials_hotel_date_end',
        'date_staff_reg_open', 'date_staff_reg_close',
        'staff_hotel_date_start', 'staff_hotel_date_end',
        'date_exhibitors_reg_open','date_exhibitors_reg_close',
    ];


const JSON_FIELDS_TO_STRINGIFY = [
    'tickets_purchase_additional_fields',
    'social_links',
    'tickets_options',
    'tickets_locations',
    'team_members_validation',
    'official_payment_method',
    'officials_hotel_comp',
    'tickets_settings',
    'swb_settings',
    'teams_settings',
    'tickets_discount',
    'staff_payment_method',
    'staff_hotel_comp',
    'coordinates',
];

const FIELDS_TO_MAKE_FALSE = [
    'tickets_published',
    'tickets_visible',
    'live_to_public',
    'published',
    'schedule_published',
];

const FIELDS_TO_SET_NULL = [
    'tickets_options',
    'date_end',
    'date_reg_close',
    'date_reg_open',
    'date_start',
    'late_reg_date',
    'officials_meeting_datetime',
    'tickets_purchase_date_end',
    'tickets_purchase_date_start',
    'event_tickets_code',
    'date_official_reg_open',
    'date_official_reg_close',
    'date_exhibitors_reg_open',
    'date_exhibitors_reg_close',
    'date_staff_reg_open',
    'date_staff_reg_close',
    'tickets_sw_balance',
    'roster_deadline',
    'online_team_checkin_end',
    'online_team_checkin_start',
    'credit_surcharge',
    'housing_teams_access_level',
    'club_private_reg_active',
    'housing_rooming_list_due_date',
    'exhibitors_sw_fee',
    'stripe_statement'
];

const FIELDS_TO_MAKE_TRUE = [
    'has_rosters',
    'tickets_use_connect',
    'teams_use_connect',
    'allow_ach_payments',
];

const FIELDS_TO_REMOVE_ON_COPYING = [
    'stripe_teams_percent',
    'stripe_tickets_fixed',
    'stripe_teams_fixed',
    'ach_teams_percent',
    'ach_teams_max_fee',
    'stripe_tickets_percent',
    'teams_entry_sw_fee',
    'tickets_sw_fee',
    'teams_sw_balance',
    'teams_sw_target_balance',
    'tickets_sw_target_balance',
];

const FIELDS_TO_MAKE_TEXT_ARRAY = ['entry_region_restriction', 'available_officials_sanctionings'];

const JSON_FIELD_KEYS_TO_DELETE_ON_COPYING = {
    'tickets_settings': ['age_date', 'require_coupon', 'allow_point_of_sales'],
    'teams_settings': ['use_payment_intents', 'use_payment_hub'],
};

const JSON_FIELD_KEYS_TO_MAKE_TRUE_ON_CREATING = {
    'teams_settings': ['use_payment_intents']
};

const JSON_FIELD_KEYS_TO_MAKE_TRUE_ON_COPYING = {
    'team_members_validation': ['one_team_per_staffer_required'],
    'teams_settings': ['use_payment_intents']
};


const OMIT_FROM_UPDATE_DATA = [
    'event_id',
    'private_club_reg_link',
    'private_encoded_key',
    'eoemail',
    'has_matches',
    'is_all_region_restriction'
];

const OMIT_FROM_CREATE_DATA = ['is_all_region_restriction'];

const DEFAULT_OFFICIALS_NOTIFICATIONS = [
    { field: 'official_accepted_email_template_id'      , email_template_id: OFFICIALS_TEMPLATES_DEFAULTS.ACCEPTED   },
    { field: 'official_applied_email_template_id'       , email_template_id: OFFICIALS_TEMPLATES_DEFAULTS.APPLIED    },
    { field: 'official_declined_email_template_id'      , email_template_id: OFFICIALS_TEMPLATES_DEFAULTS.DECLINED   },
    { field: 'official_waitlisted_email_template_id'    , email_template_id: OFFICIALS_TEMPLATES_DEFAULTS.WAITLISTED }
];

module.exports      = {
    // get /api/event/:event/update/info
    info: function (req, res) {
        const   $event_id       = parseInt(req.params.event, 10),
                $eventOwnerId   = eventOwnerService.findId($event_id, req.session.passport.user);

        if(!$event_id) {
            return res.validation('Invalid event id');
        }
        if(!$eventOwnerId) {
            return res.validation('Not an Event Owner');
        }

        const clothesRequirements = eventOwnerService.getClothesRequirements($event_id);

        Db.query(GET_INFO_SQL, [$event_id, $eventOwnerId])
        .then(async result => {
            let tournament = _.omit(result.rows[0], ['allow_point_of_sales']);

            if (!tournament) {
                return res.status(200).json({ tournament: {} });
            }

            tournament.esw_link                 = ESW_LINK + tournament.esw_id;

            if (!tournament.club_private_reg_code) {
                let regCode = await EventUtils.getPrivateClubRegCode();

                await setClubPrivateRegCode($event_id, regCode);

                tournament.club_private_reg_code = regCode;
            }

            tournament.private_club_reg_link = EventUtils.getPrivateClubRegLink(tournament.club_private_reg_code);

            tournament.clothes_requirements = await clothesRequirements;

            let paymentMethod = await EventPaymentMethodService.getEventPaymentMethod($event_id);

            tournament.payment_method_id = paymentMethod && paymentMethod.payment_method_id;

            res.status(200).json({ tournament });
        }).catch(res.customRespError.bind(res));
    },
    // get /api/sport/:sport/info
    getEventSportInfo: function (req, res) {
        var $sportId = parseInt(req.params.sport, 10);
        if(!$sportId) return res.validation('Invalid sport indentifier passed');

        Promise.all([
            Db.query(
                `SELECT ss.sport_sanctioning_id "id", ss."name" 
                 FROM "sport_sanctioning" ss 
                 WHERE ss.sport_id = $1`,
                [$sportId]
            ),
            Db.query(
                `SELECT sv."sport_variation_id" "id", sv."name" 
                 FROM "sport_variation" sv 
                 WHERE sv.sport_id = $1`,
                [$sportId]
            )
        ]).then(result => {
            res.status(200).json({
                sanctionings    : result[0].rows,
                variations      : result[1].rows
            })
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/sport/:sport/variations
    getEventSportVariations: function (req, res) {
        var $sportId = parseInt(req.params.sport, 10);
        if(!$sportId) return res.validation('Invalid sport indentifier passed');
        Db.query(
            `SELECT sv."sport_variation_id" "id", sv."name" 
             FROM "sport_variation" sv 
             WHERE sv.sport_id = $1`,
            [$sportId]
        ).then(result => {
            res.status(200).json({ variations: result.rows })
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/sport/:sport/sanctionings
    getEventSportSanctionings: function (req, res) {
        var $sportId = parseInt(req.params.sport, 10);
        if(!$sportId) return res.validation('Invalid sport indentifier passed');

        Db.query(
            `SELECT ss.sport_sanctioning_id "id", ss."name"
            FROM "sport_sanctioning" ss
            WHERE ss.sport_id = $1
            ORDER BY CASE
                WHEN ss.sport_id = $1 AND ss.sport_sanctioning_id = 3 THEN 0 -- USAV
                WHEN ss.sport_id = $1 AND ss.sport_sanctioning_id = 1 THEN 1 -- JVA
                WHEN ss.sport_id = $1 AND ss.sport_sanctioning_id = 2 THEN 2 -- AAU
                WHEN ss.sport_id = $1 AND ss.sport_sanctioning_id = 9 THEN 3 -- 9 Man
                WHEN ss.sport_id = $1 AND ss.sport_sanctioning_id = 7 THEN 4 -- Other
                ELSE 0
            END`,
            [$sportId]
        ).then(result => {
            res.status(200).json({ sanctionings: result.rows })
        }).catch(res.customRespError)
    },
    // post /api/event/create
    create: async (req, res) => {
        let $tournament     = _.omit(JSON.parse(req.body.tournament), OMIT_FROM_CREATE_DATA),
            filesMainLogo   = req.file('images_main_logo'),
            filesCoverImage = req.file('images_cover_image'),
            $eventOwnerId   = parseInt(req.session.passport.user.event_owner_id, 10),
            userID          = Number(req.session.passport.user.user_id),
            $location;

        if(_.isEmpty($tournament)) return res.validation('Empty tournament object');
        if(!$eventOwnerId) return res.validation('Not an Event Owner');

        $location = _.clone($tournament.location);
        if(_.isEmpty($location)) return res.validation('No Main Location provided');

        if( stripeService.statementDescriptorIsNotValid($tournament.stripe_statement) ) {
            return res.validation(`Teams Stripe Statement Description should not use forbidden letters < > " ' \\ * and only numbers`);
        }

        if( stripeService.statementDescriptorIsNotValid($tournament.exhibitors_stripe_statement) ) {
            return res.validation(`Exhibitors Stripe Statement Description should not use forbidden letters < > " ' \\ * and only numbers`);
        }

        const { error } = eventCreateSchema.validate($tournament);

        if (error) {
            return res.validation(error.details[0].message);
        }

        $tournament.event_owner_id = $eventOwnerId;

        $tournament.season = sails.config.sw_season.current;

        let clothesRequirements;
        try {
            clothesRequirements = await _validateClothesRequirements($tournament.clothes_requirements);
            delete $tournament.clothes_requirements;
        }
        catch(err) {
            return res.customRespError(err);
        }

        let tr;
        try {
            // createBarcode
            if($tournament.allow_ticket_sales) {
                $tournament.event_tickets_code = await EventUtils.uniqueTicketsBarcode()
            }
            // createESWId
            $tournament.esw_id = await EventUtils.generateESWID();
            // createPrivateClubCode
            $tournament.club_private_reg_code = await EventUtils.getPrivateClubRegCode();
            // setDefaultOfficialsTemplates
            if($tournament.has_officials) {
                DEFAULT_OFFICIALS_NOTIFICATIONS.forEach(item => {
                    $tournament[item.field] = item.email_template_id;
                });
            }

            delete $tournament.location;
            // stringifyValidationRules
            $tournament.team_members_validation = $tournament.validation_rules;
            delete $tournament.validation_rules;
            $tournament.tickets_settings = {
                require_recipient_name_for_each_ticket: true,
                use_vertical_insurance: true
            };
            // stringifyJSONFields
            JSON_FIELDS_TO_STRINGIFY.forEach(field => {
                if ($tournament[field]) {
                    const keysToSetTrue = JSON_FIELD_KEYS_TO_MAKE_TRUE_ON_CREATING[field];

                    if(!_.isEmpty(keysToSetTrue)) {
                        keysToSetTrue.forEach(key => {
                            $tournament[field][key] = true;
                        })
                    }

                    $tournament[field] = JSON.stringify($tournament[field]);
                }
            });
            // setTrueFields
            FIELDS_TO_MAKE_TRUE.forEach(field => {
                $tournament[field] = true;
            });
            // makeTextArrayFields
            FIELDS_TO_MAKE_TEXT_ARRAY.forEach(field => {
                if ($tournament[field]) {
                    $tournament[field] = `{${$tournament[field]}}`;
                }
            });

            await findStripeKeys($tournament);
            tr = await Db.begin();
            // createTournament
            const createEventSQL =
                        squel.insert({
                            replaceSingleQuotes     : true,
                            singleQuoteReplacement  : "''"
                        })
                        .returning('*')
                        .into('event')
                        .setFields(
                            _.omit($tournament, DATE_FIELDS)
                        );

            for(const dateField of DATE_FIELDS) {
                if($tournament[dateField]) {
                    createEventSQL.set(
                        dateField,
                        squel.str('?::TIMESTAMP', $tournament[dateField])
                    )
                }
            }

            const id = await tr.query(createEventSQL.toString())
            .then(function (result) {
                const createdTournament = _.first(result.rows);
                if(!createdTournament) {
                        throw new Error('Error creating event');
                }
                return createdTournament.event_id;
            });
            loggers.debug_log.info('Created Tournament with id', id);


            const coverImageFile = filesCoverImage._files[0];

            if (coverImageFile)
                await EventMediaService.saveEventImage(
                    coverImageFile.stream,
                    EventMediaService.imagesTypes.COVER_IMAGE,
                    id,
                    tr
                );
            else filesCoverImage.noMoreFiles();

            const mainLogoFile = filesMainLogo._files[0];

            if (mainLogoFile)
                await EventMediaService.saveEventImage(
                    mainLogoFile.stream,
                    EventMediaService.imagesTypes.MAIN_LOGO,
                    id,
                    tr
                );
            else filesMainLogo.noMoreFiles();

            await EventSettingsService.location.upsertLocation(
                tr,
                id,
                $location,
                EventSettingsService.location.MAIN_LOCATION_NUMBER,
                sails.config.isLocalEnvironment,
                sails.config.defaultEventCoordinates
            );
            // createClubs
            if($tournament.registration_method === 'doubles') {
                await __createDoublesClubs($tournament, id, tr);
            }

            await _updateClothesRequirements(tr, id, clothesRequirements, {deleteUnchecked: false});
            await tr.commit();
            // addAccessToEvent
            await RedisService.setUserDataMonitorKeysDirty(userID)
                .catch(err => {
                    loggers.errors_log.error(err);
                });
            await addEventIDToSession(req, id, $eventOwnerId);
            ErrorSender.eventCreated({
                id: id,
                long_name: $tournament.long_name || $tournament.name,
                name: $tournament.name,
                date_start: $tournament.date_start
            });

            return res.status(200).json({ id: id })
        }
        catch(err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            return res.customRespError(err);
        }
    },
    // put /api/event/:event/update
    update: async (req, res) => {
        LogRequestService.logEventUpdate(req);

        let $tournament         = _.omit(JSON.parse(req.body.tournament), OMIT_FROM_UPDATE_DATA),
            $eventId            = parseInt(req.params.event),
            $eventOwnerId       = eventOwnerService.findId($eventId, req.session.passport.user),
            user                = req.session.passport.user,
            $userId             = Number(req.user.user_id),
            paymentMethodID,
            $location;

        if(_.isEmpty($tournament)) {
            return res.validation('Empty tournament object');
        }

        if(!$eventOwnerId) {
            return res.validation('Not an Event Owner');
        }

        if(!$eventId) {
            return res.validation('Invalid Tournament Identifier')
        }

        $location = _.clone($tournament.location);
        if(_.isEmpty($location)) {
            return res.validation('No Main Location provided');
        }

        if( stripeService.statementDescriptorIsNotValid($tournament.stripe_statement) ) {
            return res.validation(`Teams Stripe Statement Description should not use forbidden letters < > " ' \\ * and only numbers`);
        }

        if( stripeService.statementDescriptorIsNotValid($tournament.exhibitors_stripe_statement) ) {
            return res.validation(`Exhibitors Stripe Statement Description should not use forbidden letters < > " ' \\ * and only numbers`);
        }

        const { error } = eventUpdateSchema.validate($tournament);

        if (error) {
            return res.validation(error.details[0].message);
        }

        let clothesRequirements;
        try {
            clothesRequirements = await _validateClothesRequirements($tournament.clothes_requirements);
            delete $tournament.clothes_requirements;
        }
        catch(err) {
            return res.customRespError(err);
        }
        let tr;
        try {
            const initialTournament = await Db.query(GET_INFO_SQL, [$eventId, $eventOwnerId])
            .then(
                (result) => {
                    const initialTournament = _.first(result.rows);
                    if(_.isEmpty(initialTournament)) {
                        throw { validation: 'Tournament not found' };
                    }
                    return initialTournament;
                }
            );

            if(
                initialTournament.extra_fee_collection_mode === EXTRA_FEE_COLLECTION_MODE.CUSTOM_PAYMENT ||
                initialTournament.teams_settings.do_not_collect_sw_fee
            ) {
                if(!$tournament.payment_method_id) {
                    throw { validation: 'Event Custom Payment Method required' };
                }

                paymentMethodID = $tournament.payment_method_id;
            }

            delete $tournament.payment_method_id;

            // generateBarcode
            if($tournament.allow_ticket_sales && !initialTournament.allow_ticket_sales) {
                $tournament.event_tickets_code = await EventUtils.uniqueTicketsBarcode();
            }

            // addOfficialsTemplates
            if($tournament.has_officials && !initialTournament.has_officials) {
                DEFAULT_OFFICIALS_NOTIFICATIONS.forEach(item => {
                    $tournament[item.field] = item.email_template_id;
                });
            }

            delete $tournament.location;

            // stringifyValidationRules
            $tournament.team_members_validation = $tournament.validation_rules;
            delete $tournament.validation_rules;

            // stringifyJSONFields
            JSON_FIELDS_TO_STRINGIFY.forEach(field => {
                if ($tournament[field]) {
                    $tournament[field] = JSON.stringify($tournament[field]);
                }
            });

            // makeTextArrayFields
            FIELDS_TO_MAKE_TEXT_ARRAY.forEach(field => {
                if ($tournament[field]) {
                    $tournament[field] = `{${$tournament[field]}}`;
                }
            });

            if (initialTournament.block_teams_keys_edit) {
                delete $tournament.teams_stripe_account_id;
            }
            if (initialTournament.block_exhibitors_keys_edit) {
                delete $tournament.exhibitors_stripe_account_id;
            }

            const imagesToRemove = _.values($tournament.images)
                .filter(({ remove }) => remove)
                .map(({ id }) => id)

            const images = _.pickBy($tournament.images, ({remove}) => !remove)

            delete $tournament.images;

            await findStripeKeys($tournament);

            tr = await Db.begin({skipErrAboutCommittedTr: true});
            await Promise.all([
                (async () => {

                    const updateEventSQL =
                        squel.update({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
                        .table('event')
                        .where('event_id = ?', $eventId)
                        .where('event_owner_id = ?', $eventOwnerId)
                        .setFields(_.omit($tournament, DATE_FIELDS, 'esw_id', 'esw_link'))
                        .returning('*');

                    for(const dateField of DATE_FIELDS) {
                        if($tournament[dateField]) {
                            updateEventSQL.set(
                                dateField,
                                squel.str('?::TIMESTAMP', $tournament[dateField])
                            )
                        }
                    }
                    const result = await tr.query(updateEventSQL);
                    if(result.rowCount === 1) {
                        loggers.debug_log.info('Updated Tournament with id', $eventId);
                    }
                })(),
                (async ()=> {
                    if (!_.isEmpty(imagesToRemove))
                        await EventMediaService.removeEventImages(
                            $eventId,
                            imagesToRemove,
                            tr
                        );
                })(),
                EventSettingsService.location.upsertLocation(
                    tr,
                    $eventId,
                    $location,
                    EventSettingsService.location.MAIN_LOCATION_NUMBER,
                    sails.config.isLocalEnvironment,
                    sails.config.defaultEventCoordinates
                ),
                await _updateClothesRequirements(tr, $eventId, clothesRequirements),
            ]);

            const filesCoverImage     = req.file('images_cover_image');
            const coverImageFile = filesCoverImage._files[0];

            if (coverImageFile)
                images[EventMediaService.imagesTypes.COVER_IMAGE] =
                    await EventMediaService.saveEventImage(
                        coverImageFile.stream,
                        EventMediaService.imagesTypes.COVER_IMAGE,
                        $eventId,
                        tr
                    );
            else filesCoverImage.noMoreFiles();

            const filesMainLogo       = req.file('images_main_logo');
            const mainLogoFile = filesMainLogo._files[0];

            if (mainLogoFile)
                images[EventMediaService.imagesTypes.MAIN_LOGO] =
                    await EventMediaService.saveEventImage(
                        mainLogoFile.stream,
                        EventMediaService.imagesTypes.MAIN_LOGO,
                        $eventId,
                        tr
                    );
            else filesMainLogo.noMoreFiles();


            if( $tournament.registration_method === 'doubles' &&
                initialTournament.registration_method !== 'doubles') {
                await __createDoublesClubs($tournament, $eventId, tr);
            }

            if(paymentMethodID) {
                const isNotPaymentMethodOwner = user.has_god_role || user.shared_events?.[$eventId]?.role_co_owner;
                await addEventPaymentMethod(tr, $eventId, $userId, paymentMethodID, isNotPaymentMethodOwner);
            }

            await tr.commit();

            if(initialTournament.allow_point_of_sales) {
                await SalesHubService.sync.syncPointOfSales($eventId, POINT_OF_SALES_TYPE.TICKETS);
            }

            delete $tournament.allow_point_of_sales;

            // updClubHousing
            let curNightsRequired   = Number(initialTournament.housing_nights_required);
            let curNightsThreshold  = Number(initialTournament.housing_nights_threshold);
            let updNightsRequired   = Number($tournament.housing_nights_required);
            let updNightsThreshold  = Number($tournament.housing_nights_threshold);

            let updateHousingMethodName = null;
            let updateTarget            = '';

            let updHousingRoomingListDueDate = $tournament.housing_rooming_list_due_date &&
                moment($tournament.housing_rooming_list_due_date, 'MM/DD/YYYY hh:mm:a').format('YYYY-MM-DD');

            if ((curNightsRequired !== updNightsRequired) || (curNightsThreshold !== updNightsThreshold)) {
                updateHousingMethodName = 'update_event_clubs_housing';
                updateTarget            = 'clubs';

            } else if(initialTournament.housing_rooming_list_due_date !== updHousingRoomingListDueDate){
                updateHousingMethodName = 'updateEventHousing';
                updateTarget            = 'teams';
            }

            if(updateHousingMethodName) {
                try {
                    const qty = await HousingService[updateHousingMethodName]($eventId);
                    loggers.debug_log.verbose('Updated housing for', qty, updateTarget);
                }
                catch(err) {
                    ErrorSender.defaultError(err);
                    throw err;
                }
            }

            //new event notify by copy event
            if(!initialTournament.date_start && !initialTournament.date_end) {
                ErrorSender.eventCreated({
                    id: $eventId,
                    long_name: $tournament.long_name || $tournament.name,
                    name: $tournament.name,
                    date_start: $tournament.date_start
                });
            }

            // cleadRedisCache
            const { esw_id } = initialTournament;

            const removeCacheByMaskList = [
                EswCache.clearEvent(esw_id),
                EswCache.clearEventsList(),
                Cache.removeByMask(`/api/esw/events`),
                Cache.removeByMask(`/api/esw/${$eventId}`),
                Cache.removeByMask(`/api/esw/${$eventId}/*`),
                Cache.removeByMask(`/api/esw/${esw_id}`),
                Cache.removeByMask(`/api/esw/${esw_id}/*`),
            ];

            const divisionListDependencies = [
                'public_teams_visibility',
                'show_team_entry_status',
            ];
            if (divisionListDependencies.some(field => initialTournament[field] !== $tournament[field])) {
                removeCacheByMaskList.push(Cache.removeByMask(`/api/esw/club/event/${$eventId}/divisions_list`));
            }

            await Promise.all(removeCacheByMaskList)
                .catch(err => loggers.errors_log.error(err));

            return res.status(200)
                .json({
                    tournament: {
                        images,
                        private_encoded_key: $tournament.private_encoded_key,
                    }
                });
        }
        catch(err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            return res.customRespError(err);
        }
    },
    // get /api/event/:event/clothes-types
    getClothesTypes: function(req, res) {
        eventOwnerService.getClothesTypes()
            .then(r=>res.json(r))
            .catch(res.customRespError);
    },
    // get /api/event/:event/locations
    getLocationsList: function (req, res) {
        const $eventId = parseInt(req.params.event, 10);

        Db.query(
            `SELECT 
                el.address, el.city, el.state, el.zip, el.courts_from, el.courts_to, el.name, 
                el.short_name, el.number, el.event_location_id "id", s.name "state_name" 
            FROM "event_location" el 
            LEFT JOIN "state" s 
                ON s.state = el.state 
            WHERE el.event_id = $1 
            ORDER BY el.number`,
            [$eventId]
        ).then(result => {
            res.status(200).json({ locations: result.rows });
        }).catch(res.customRespError)
    },
    // get /api/event/:event/locations/:location
    getLocationInfo: function (req, res) {
        const $locationId = parseInt(req.params.location, 10);
        const $eventId = parseInt(req.params.event, 10);
        const $eventOwnerId = eventOwnerService.findId($eventId, req.session.passport.user);

        if(!$locationId) return res.validation('Invalid location passed');

        Db.query(
            `SELECT 
                el.address, el.city, el.state, el.zip, 
                el.courts_from, el.courts_to, el.name, el.short_name 
            FROM "event_location" el 
            INNER JOIN "event" e 
                ON e.event_id = el.event_id 
                AND e.event_owner_id = $3 
            WHERE el.event_id = $2 
                AND el.event_location_id = $1`,
            [$locationId, $eventId, $eventOwnerId]
        ).then(result => {
            res.status(200).json({ location: result.rows[0] || {} });
        }).catch(res.customRespError)
    },
    // put /api/event/:event/locations/:location
    updateLocation: async function (req, res) {
        const $locationId = parseInt(req.params.location, 10);
        const $eventId = parseInt(req.params.event, 10);
        const $location = req.body.location;

        if(!$locationId) return res.validation('Invalid location passed');
        if(_.isEmpty($location)) return res.validation('Invalid location passed');

        try {
            const { error } = locationSchema.validate(
                $location
            );
            if(error) {
                throw { validationErrors: error.details };
            }
            await EventSettingsService.location.updateLocationById($locationId, $eventId, $location);
            return res.ok();
        }
        catch(err) {
            return res.customRespError(err);
        }
    },
    // post /api/event/:event/locations
    createLocation: async function (req, res) {
        const $eventId = parseInt(req.params.event, 10);
        const $location = req.body.location;

        if(_.isEmpty($location)) return res.validation('Invalid location passed');

        try {
            const { error } = locationSchema.validate(
                $location
            );
            if(error) {
                throw { validationErrors: error.details };
            }
            await EventSettingsService.location.createLocation($eventId, $location);
            return res.ok();
        }
        catch(err) {
            return res.customRespError(err);
        }
    },
    // delete /api/event/:event/locations/:location
    removeLocation: async function (req, res) {
        const $locationId = parseInt(req.params.location, 10);
        const $eventId = parseInt(req.params.event, 10);
        const $eventOwnerId = eventOwnerService.findId($eventId, req.session.passport.user);

        try {
            let result = await Db.query(
                `SELECT el.event_location_id, el.number::INTEGER 
                 FROM "event_location" el 
                 INNER JOIN "event" e 
                     on e.event_id = el.event_id 
                     and e.event_owner_id = $3 
                 WHERE el.event_id = $1  
                     and el.event_location_id = $2`,
                [$eventId, $locationId, $eventOwnerId]
            );
            const location = result.rows[0];

            if(_.isEmpty(location)) {
                throw { validation: 'Location not found' }
            }

            if(location.number === 1) {
                throw { validation: 'Main Location cannot be removed' }
            }

            result = await Db.query(
                'DELETE FROM "event_location" WHERE event_location_id = $1 and event_id = $2',
                [$locationId, $eventId]
            );

            loggers.debug_log.verbose('Removed', result.rowCount, 'locations');

            return res.ok();
        }
        catch(err) {
            return res.customRespError(err);
        }
    },
    // post /api/event/:event/copy
    copyEvent: async function (req, res) {
        let $eventID        = req.params.event,
            $copyDivs       = req.body.duplicate_divisions,
            $copyLocs       = req.body.duplicate_locations,
            $copyEmailTemplates = req.body.duplicate_email_templates,
            eventOwnerID    = eventOwnerService.findId($eventID, req.session.passport.user),
            userID          = Number(req.session.passport.user.user_id);

        let tr;
        try {
            let eventToCopy = await getEvent($eventID, eventOwnerID);

            if (_.isEmpty(eventToCopy)) {
                return Promise.reject({ validation: 'Event not found' });
            }

            // Fields Details:
            // https://docs.google.com/spreadsheets/d/1NDe4fjufkFRyNMCLxHoEOcfjtP7XpiIThEMT2p6OcIs/edit#gid=0

            eventToCopy.long_name   = 'Copy of ' + eventToCopy.long_name;
            eventToCopy.name        = 'Copy ' + eventToCopy.name;

            eventToCopy.tickets_settings = {
                ...eventToCopy.tickets_settings,
                use_vertical_insurance: true
            };

            JSON_FIELDS_TO_STRINGIFY.forEach(field => {
                if (eventToCopy[field]) {
                    let keysToDelete = JSON_FIELD_KEYS_TO_DELETE_ON_COPYING[field];
                    let keysToSetTrue = JSON_FIELD_KEYS_TO_MAKE_TRUE_ON_COPYING[field];

                    if (keysToDelete) {
                        keysToDelete.forEach(key => {
                            eventToCopy[field][key] = undefined;
                        })
                    }

                    if(!_.isEmpty(keysToSetTrue)) {
                        keysToSetTrue.forEach(key => {
                            eventToCopy[field][key] = true;
                        })
                    }

                    eventToCopy[field] = JSON.stringify(eventToCopy[field]);
                }
            });

            FIELDS_TO_MAKE_FALSE.forEach(field => {
                eventToCopy[field] = false;
            });

            FIELDS_TO_SET_NULL.forEach(field => {
                eventToCopy[field] = null;
            });

            FIELDS_TO_MAKE_TRUE.forEach(field => {
                eventToCopy[field] = true;
            });

            FIELDS_TO_REMOVE_ON_COPYING.forEach(field => {
                delete eventToCopy[field];
            });

            FIELDS_TO_MAKE_TEXT_ARRAY.forEach(field => {
                if (eventToCopy[field]) {
                    eventToCopy[field] = `{${eventToCopy[field]}}`;
                }
            });

            DEFAULT_OFFICIALS_NOTIFICATIONS.forEach(item => {
                eventToCopy[item.field] = item.email_template_id;
            });

            if (eventToCopy.allow_ticket_sales) {
                eventToCopy.event_tickets_code = await EventUtils.uniqueTicketsBarcode();
            }

            eventToCopy.esw_id = await EventUtils.generateESWID();

            eventToCopy.club_private_reg_code = await EventUtils.getPrivateClubRegCode();

            tr = await Db.begin();

            let createdEventID = await createEvent(tr, eventToCopy);
loggers.debug_log.verbose('createdEventID', createdEventID);
            await RedisService.setUserDataMonitorKeysDirty(userID);

            if (req.session.passport.user.event_owner_id !== eventToCopy.event_owner_id) {
                await EventUserService.setUserAsCoOwner(tr, eventToCopy.event_owner_id, createdEventID, userID);
            }

            const clothesRequirementsCopyingPromise = copyClothesRequirements(tr, $eventID, createdEventID);

            if (eventToCopy.registration_method === 'doubles') {
                await __createDoublesClubs(eventToCopy, createdEventID, tr);
            }

            if ($copyDivs) {
                await copyDivisions(tr, $eventID, createdEventID);
            }
loggers.debug_log.verbose('copyDivs');
            if ($copyLocs) {
                await copyLocations(tr, $eventID, createdEventID);
            }
loggers.debug_log.verbose('copyLocs');
            if ($copyEmailTemplates) {
                await EventService.duplication.copyEmailTemplates($eventID, createdEventID, eventOwnerID);
            }
loggers.debug_log.verbose('copyEmailTemplates');
            await clothesRequirementsCopyingPromise;
            await tr.commit();
loggers.debug_log.verbose('addEventIDToSession');
            await addEventIDToSession(req, createdEventID, eventOwnerID);



            const event_id = createdEventID;

            res.status(200).json({ event_id });
        }
        catch(err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            return res.customRespError(err);
        }

        async function getEvent (eventID, eventOwnerID) {
            const result = await Db.query(
                `SELECT e.* 
                 FROM "event" e
                 WHERE e."event_id" = $1 
                    AND e."event_owner_id" = $2`,
                [eventID, eventOwnerID]
            );
            return result.rows[0];
        }

        async function createEvent (tr, event) {
            delete event.event_id;
            delete event.created;
            delete event.modified;

            event.season = sails.config.sw_season.current;

            const result = await tr.query(
                squel.insert().into('event').setFields(event).returning('event_id')
            );
            const createdEvent = result.rows[0];

            if (!createdEvent) {
                return Promise.reject({ validation: 'Internal Error on Event Duplication' });
            }

            return createdEvent.event_id;
        }

        async function copyDivisions (tr, oldEventID, newEventID) {
            // should I copy deleted/declined divisions?
            const oldDivisions = await tr.query(
                squel.select().field('*').from('division').where('event_id = ?', oldEventID)
            ).then(result => result.rows);

            if (oldDivisions.length === 0) {
                return;
            }

            oldDivisions.forEach(division => {
                delete division.created;
                delete division.modified;
                delete division.division_id;
                delete division.has_flow_chart;
                delete division.reg_fee;
                delete division.credit_surcharge;
                division.closed = null;
                division.locked = false;
                division.date_reg_close = null;
                division.roster_deadline = null;
                division.event_id = newEventID;
                if (division.swb_settings)  {
                    division.swb_settings = JSON.stringify(division.swb_settings)
                }
            });

            await tr.query(
                squel.insert().into('division').setFieldsRows(oldDivisions)
            );
        }

        async function copyLocations (tr, oldEventID, newEventID) {
            const oldLocations = await tr.query(
                squel.select().field('*').from('event_location').where('event_id = ?', oldEventID)
            ).then(result => result.rows);

            if (oldLocations.length === 0) {
                return;
            }

            oldLocations.forEach(location => {
                delete location.created;
                delete location.modified;
                delete location.event_location_id;
                location.event_id = newEventID;
            });

            await tr.query(
                squel.insert().into('event_location').setFieldsRows(oldLocations)
            );
        }

        function copyClothesRequirements (tr, oldEventID, newEventID) {
            const newFields = [
                'common_item_id',
                'event_id',
                'gender',
                'member_type',
            ];
            const newEventClothesQuery = squel.select()
                .from('event_clothes')
                .where('event_id = ? AND deleted IS NULL', oldEventID)
                .fields(['common_item_id'])
                .field(squel.str('?', newEventID), 'event_id')
                .fields(['gender', 'member_type']);
            return tr.query(
                squel.insert()
                    .into('event_clothes')
                    .fromQuery(newFields, newEventClothesQuery)
            ).then(() => {})
        }
    },

    // get /api/event/:event/template-types
    getTemplateTypes: function (req, res) {
        let eventID      = Number(req.params.event);
        let eventOwnerID = Number(eventOwnerService.findId(eventID, req.session.passport.user));

        return AEMService.getTemplatesAndTriggerTypes(eventID, eventOwnerID)
            .then( result => res.status(200).json(result))
            .catch( err => res.customRespError(err) );
    },

    //post /api/event/:event/assign-templates;
    assignTmplToTrigger: function (req, res) {
        let eventID      = Number(req.params.event);
        let data         = req.body.data;

        return AEMService.assignTemplatesToTriggers(eventID, data)
            .then( () => res.status(200).json({}) )
            .catch( err => res.customRespError(err) );
    },

    // get /api/official-additional-roles
    getOfficialAdditionalRoles: function (req, res) {
        Db.query(
            `SELECT
                oar."official_additional_role_id" "id",
                oar."name",
                oar."description",
                oar."abbreviation"
             FROM "official_additional_role" oar`
        ).then(result => {
            res.status(200).json(result.rows)
        }).catch(err => {
            res.customRespError(err);
        });
    },
};

function setClubPrivateRegCode (eventID, code) {
    return Db.query(
        squel.update({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
            .table('event')
            .set('club_private_reg_code', code)
            .where('event_id = ?', eventID)
    ).then(() => {})
}
async function findStripeKeys (tournament) {
    await Promise.all(
        [
            { private_key: 'stripe_teams_private_key', account_id: 'teams_stripe_account_id' },
            { private_key: 'stripe_exhibitors_private_key', account_id: 'exhibitors_stripe_account_id' },
        ]
            .filter(
                ({ account_id }) => account_id in tournament
            )
            .map(
                async ({ private_key, account_id }) => {
                    const stripe_account_id = tournament[account_id];
                    delete tournament[account_id];
                    const account = await (
                        stripe_account_id
                        ? StripeService.account.getStripeKeys({ id: stripe_account_id })
                            .then(accounts => accounts[0])
                        : null
                    );
                    if(typeof account === 'undefined') {
                        throw { validation: 'Selected Stripe Account is not available.' };
                    }

                    tournament[private_key] = account && account.secret_key;
                }
            )
    );
}

async function addEventPaymentMethod (tr, eventID, userID, paymentMethodID, isNotPaymentMethodOwner) {
    let paymentMethodOwningUserID = userID;

    if(isNotPaymentMethodOwner) {
        paymentMethodOwningUserID = await StripeService
            .paymentCard
            .paymentMethodUser
            .getUserIDOwningPaymentMethod(paymentMethodID);
    }

    return EventPaymentMethodService.savePaymentMethod(tr, eventID, paymentMethodOwningUserID, paymentMethodID);
}

async function addEventIDToSession (req, eventID, eventOwnerID) {

    /* We have 2 possibilities:
        1. eventOwnerID === req.session.passport.user.event_owner_id
        this means we should add eventID to user.events array

        2. eventOwnerID !== req.session.passport.user.event_owner_id
        this means we should add eventID to user.shared_events object
    */

    let user = req.session.passport.user;

    if (eventOwnerID === user.event_owner_id) {
        if (user.events) {
            user.events.push(eventID);
        } else  {
            user.events = [eventID];
        }
    } else {
        const permissions = await EventUserService.getEventUserPermissions(eventID, user.user_id);
        user.shared_events[eventID] = {role_co_owner: true, event_owner_id: eventOwnerID, deleted: false, permissions};
    }

    return new Promise(resolve => {
        req.session.save(err => {
            if(err) {
                loggers.errors_log.error(err);
            }
            resolve();
        });
    });
}

async function _validateClothesRequirements(requirements) {
    const commonItemIds = (await eventOwnerService.getClothesTypes())
        .reduce(
            (r, v) => {
                r.add(v.common_item_id);
                return r;
            }, new Set()
        );

    const { error, value: clothesRequirements } = clothesRequirementsSchema.validate(requirements);
    if (error) {
        throw { validationErrors: error.details };
    }

    const _commonItemIds = await commonItemIds;
    for (const memberType in clothesRequirements) {
        for (const gender in clothesRequirements[memberType]) {
            if(clothesRequirements[memberType][gender].some(v => !_commonItemIds.has(v.common_item_id))) {
                throw {validation: 'Unknown common_item_id in clothes_requirements'};
            }
        }
    }
    return clothesRequirements;
}

async function _updateClothesRequirements(tr, eventId, requirements, {deleteUnchecked=true} = {}) {
    let queries = [];

    let uncheckQuery = squel.update()
        .table('event_clothes')
        .set('deleted', squel.str('NOW()'))
        .where('deleted IS NULL AND event_id = ?', eventId);
    let conditions = squel.expr();
    let newRows = [];
    let hasConditions = false;
    for (const memberType in requirements) {
        for (const gender in requirements[memberType]) {
            requirements[memberType][gender].filter(v => v.required).forEach(({common_item_id}) => {
                newRows.push({
                    common_item_id,
                    event_id: eventId,
                    gender,
                    member_type: memberType,
                    deleted: null
                });
            });
            if(deleteUnchecked) {
                const notRequiredItemsIds = requirements[memberType][gender].filter(v => !v.required).map(v => v.common_item_id);
                if (notRequiredItemsIds.length < 1) {
                    continue;
                }
                conditions.or(
                    `member_type = ? AND gender = ? AND common_item_id IN ?`,
                    memberType, gender, notRequiredItemsIds
                );
                hasConditions = true;
            }
        }
    }
    if (deleteUnchecked && hasConditions) {
        uncheckQuery.where(conditions);
        queries.push(tr.query(
            uncheckQuery
        ));
    }

    if (newRows.length > 0) {
        const uniqueFields = [
            'common_item_id',
            'event_id',
            'gender',
            'member_type',
        ];
        const query = tr.query(
            squel.insert()
                .into('event_clothes')
                .setFieldsRows(newRows)
                .onConflict(
                    uniqueFields,
                    { deleted:null }
                )
        );
        queries.push(
            query
        );
    }
    await Promise.all(queries);
}

async function __createDoublesClubs ($tournament, id, tr) {
    let result = await (tr.query(
        squel.insert().into('master_club')
        .set('club_name', '{0} Club'.format($tournament.name))
        .set('has_coed_teams', $tournament.has_coed_teams)
        .set('has_male_teams', $tournament.has_male_teams)
        .set('has_female_teams', $tournament.has_female_teams)
        .set('sport_id', +$tournament.sport_id)
        .returning('master_club_id, club_name')
    ));

    const mc = result.rows[0];

    loggers.debug_log.info('Created Master Club with id', mc.master_club_id);

    result = await (tr.query(
        squel.insert().into('roster_club')
        .set('club_name', mc.club_name)
        .set('master_club_id', mc.master_club_id)
        .set('address', $tournament.hosting_org_address)
        .set('city', $tournament.hosting_org_city)
        .set('code', '00000')
        .set('country', 'US')
        .set('region', 'region')
        .set('event_id', id)
        .returning('roster_club_id')
    ));

    const rc = result.rows[0];

    loggers.debug_log.info('Created Roster Club with id', rc.roster_club_id);
}

var GET_INFO_SQL =
`SELECT
    COALESCE(e.teams_settings, '{}'::JSONB) AS teams_settings, e.teams_use_clubs_module, e.official_qr_code_enable,
    e.event_id, e.mincount_enter, e.email, e.mincount_accept, e.has_coed_teams, e.has_female_teams, e.has_male_teams, 
    e.long_name, e.name, e.reg_fee, e.sport_id::TEXT, e.website, e.rules_website, e.sport_variation_id, e.has_rosters,   
    e.region, e.has_status_housing, e.payment_address, e.payment_name, e.payment_city, e.payment_state, e.hide_seeds, 
    e.payment_zip, e.payment_country, e.hosting_org_address, e.hosting_org_city, e.hosting_org_name, e.housing_teams_access_level, 
    e.hosting_org_state, e.hosting_org_zip, e.housing_company_id, e.custom_housing_company, e.officials_hotel_comp,
    e.event_notes, e.schedule_published, e.score_entry_allowed, e.housing_local_teams_distance, e.published,
    e.housing_nights_required, e.housing_nights_threshold, e.sales_manager_id, e.enable_officials_reg,  
    e.notify_frequency, e.notify_emails, e.has_officials, e.timezone, e.registration_method, e.credit_surcharge, 
    e.usav_required, e.has_match_barcodes, e.admin_security_pin, e.stripe_statement, e.exhibitors_stripe_statement, e.event_type,
    TO_CHAR(e.officials_hotel_date_end, 'YYYY-MM-DD') "officials_hotel_date_end", e.online_team_checkin_mode,
    TO_CHAR(e.officials_hotel_date_start, 'YYYY-MM-DD') "officials_hotel_date_start", e.team_fees_notification_email,
    e.sport_sanctioning_id, e.tickets_published, e.tickets_purchase_passcode, e.tickets_refund_passcode,  
    e.tickets_visible, e.allow_ticket_sales, e.live_to_public, COALESCE(e.social_links, '{}'::JSON) "social_links", 
    e.allow_teams_registration, sa_teams.id "teams_stripe_account_id", e.enable_hotel_for_officials, e.score_entry_live,
    FORMAT('%s (%s)', sa_teams.account_name, sa_teams.account_email) "teams_stripe_account_name",
    FORMAT('%s (%s)', sa_exhibitors.account_name, sa_exhibitors.account_email) "exhibitors_stripe_account_name",
    (e.date_start AT TIME ZONE 'UTC') "date_start", (e.roster_deadline AT TIME ZONE 'UTC') "roster_deadline", 
    (e.date_end AT TIME ZONE 'UTC') "date_end", (e.date_reg_open AT TIME ZONE 'UTC') "date_reg_open", 
    (e.date_reg_close AT TIME ZONE 'UTC') "date_reg_close", e.allow_card_payments, e.allow_check_payments,
    (e.date_official_reg_open AT TIME ZONE 'UTC') "date_official_reg_open", e."club_private_reg_code",
    (e.date_exhibitors_reg_open AT TIME ZONE 'UTC') "date_exhibitors_reg_open",
    (e.date_exhibitors_reg_close AT TIME ZONE 'UTC') "date_exhibitors_reg_close",
    (e.date_official_reg_close AT TIME ZONE 'UTC') "date_official_reg_close", e."club_private_reg_active" :: BOOLEAN,
    TO_CHAR(e.staff_hotel_date_start, 'YYYY-MM-DD') "staff_hotel_date_start",
    TO_CHAR(e.staff_hotel_date_end, 'YYYY-MM-DD') "staff_hotel_date_end",
    (e.date_staff_reg_open AT TIME ZONE 'UTC') "date_staff_reg_open",
    (e.date_staff_reg_close AT TIME ZONE 'UTC') "date_staff_reg_close", 
    e.has_staff, e.enable_staff_reg, e.staff_payment_method, e.enable_hotel_for_staff, e.staff_hotel_comp,
    e.has_exhibitors, e.enable_exhibitors_reg, sa_exhibitors.id "exhibitors_stripe_account_id",
    e.hosting_org_phone, TO_CHAR(e.housing_rooming_list_due_date, 'YYYY-MM-DD') "housing_rooming_list_due_date",
    e.extra_fee_collection_mode,
    e.available_officials_sanctionings,
    (SELECT COALESCE(ROW_TO_JSON("l"), '{}'::JSON) FROM ( 
        SELECT  el.event_location_id, el.name, el.short_name, el.courts_to, 
                el.address, el.city, el.state, el.zip, el.courts_from 
    ) "l") "location", ( 
        SELECT d.* FROM ( 
            SELECT COUNT(d.division_id) FROM division d 
            WHERE d.event_id = e.event_id 
                AND d.published = TRUE 
        ) "d" 
    )::INTEGER "divisions_count", 
    (e.tickets_settings->>'allow_point_of_sales')::BOOLEAN IS TRUE "allow_point_of_sales",
    (SELECT COALESCE(jsonb_object_agg(file_type, event_media), '{}'::jsonb) as images
        FROM (
            SELECT file_type, json_build_object('id', event_media_id, 'path', concat(file_path, '.', file_ext)) as event_media
                FROM event_media
                WHERE event_id = e.event_id AND file_type <> 'small-logo'
        ) AS event_images) as "images",
    e.public_teams_visibility, e.clubs_teams_visibility, e.esw_id,
    e.maxcount_accept, e.maxcount_staff_accept,
    e.online_team_checkin_available, (e.online_team_checkin_end AT TIME ZONE 'UTC') "online_team_checkin_end",
    (e.online_team_checkin_start AT TIME ZONE 'UTC') "online_team_checkin_start",
    e.online_team_checkin_disclaimer, COALESCE(e.team_members_validation, '{}'::JSONB) "validation_rules",
    e.require_match_end_time, e.official_payment_method, e.online_team_checkin_info,
    (EXISTS(
        SELECT TRUE
        FROM purchase pt
        WHERE pt.event_id = e.event_id
            AND pt.payment_for = 'teams'
            AND pt.type IN('card', 'ach')
            AND pt.canceled_date IS NULL 
            AND pt.status <> 'canceled'
        LIMIT 1
    )) "block_teams_keys_edit",
    (EXISTS(
        SELECT TRUE
        FROM purchase pe
        WHERE pe.event_id = e.event_id
            AND pe.payment_for = 'booths'
            AND pe.type = 'card'
            AND pe.canceled_date IS NULL 
            AND pe.status <> 'canceled'
        LIMIT 1
    )) "block_exhibitors_keys_edit",
    u.email as eoemail,
    (
        SELECT COUNT(*) > 0
        FROM matches
        WHERE matches.event_id = e.event_id 
    ) "has_matches",
    (
        CASE 
            WHEN e.entry_region_restriction IS NULL
                THEN TRUE
                ELSE FALSE
        END
    ) "is_all_region_restriction",
    e.entry_region_restriction,
    e.show_on_home_page,
    e.show_number_of_teams_for_cd,
    e.show_number_of_teams_for_public,
    e.show_team_entry_status,
    e.use_clinic,
    e.official_additional_role_enable
FROM "event" e
JOIN "event_owner" eo ON eo.event_owner_id = e.event_owner_id
JOIN "user" u ON u.user_id = eo.user_id
LEFT JOIN "event_location" el 
    ON el.event_id = e.event_id 
    AND el.number = 1 
LEFT JOIN "stripe_account" sa_teams 
    ON sa_teams."secret_key" = e."stripe_teams_private_key"
LEFT JOIN "stripe_account" sa_exhibitors
    ON sa_exhibitors."secret_key" = e."stripe_exhibitors_private_key"
WHERE e.event_id = $1 
    AND e.event_owner_id = $2`;



