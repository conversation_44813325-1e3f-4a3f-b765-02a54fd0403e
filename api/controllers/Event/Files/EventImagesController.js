const co    = require('co');
const utils = require('../../../lib/swUtils');

const MAX_FILE_SIZE = 5120;

const VALIDATION_RULES = {
    'main-logo': {
        size: MAX_FILE_SIZE,
    },
    'cover-image': {
        size: MAX_FILE_SIZE,
    },
    sharedRules: {
        fileTypes: ['image/jpeg', 'image/png'],
    },
};

const FORM_ELEMENT_NAME = 'file';
const S3_FOLDER = 'images/';

module.exports = {
    // POST /api/event/:event/image
    uploadImage: co.wrap(function * (req, res) {
       try {
           const { type: imageType }            = JSON.parse(req.body.imageInfo);
           const image                          = req.file(FORM_ELEMENT_NAME)._files[0].stream;
           const eventId                        = req.params.event;
           const filename                       = EventMediaService.formatFileName(image.filename);
           const validationRules                = VALIDATION_RULES[imageType];
           const fileType                       = image.headers['content-type'];
           const { fileNameWithoutExt, ext }    = EventMediaService.getFileData(filename);
           const s3Path                         = `${S3_FOLDER}${eventId}/${filename}`;
           const DBPath                         = `/${S3_FOLDER}${eventId}/${fileNameWithoutExt}`;

           if (utils.getNextBinaryPrefixValue(image.byteCount) > validationRules.size) {
               return res.validation(`Maximum size for this image type is ${
                   utils.getNextBinaryPrefixValue(validationRules.size)}MB`);
           }

           if (!VALIDATION_RULES.sharedRules.fileTypes.includes(fileType)) {
               return res.validation('Allowable PNG or JPEG types');
           }

           const imagesData     = EventMediaService.generateImageData(eventId, filename);
           const result         = yield EventMediaService.uploadImages(image, imageType , imagesData, s3Path, fileNameWithoutExt);

           const imagesDataForDB    = _.filter(result, type => type);
           const _saveImage         = EventMediaService.saveImage.bind(EventMediaService, ext, eventId);
           const images             = EventMediaService.setDBPathToImageData(eventId, imagesDataForDB);

           yield Promise.all(images.map(_saveImage));

           res.ok();
       } catch (err) {
           res.customRespError(err);
       }
    }),

    // GET /api/event/:event/image
    getImages: co.wrap(function * (req, res) {
        try {
            const eventId = req.params.event;

            const images = yield EventMediaService.getImages(eventId);

            res.json({ images });
        } catch (err) {
            res.customRespError(err);
        }
    }),

    // DELETE /api/event/:event/image
    removeImage: co.wrap(function * (req, res) {
        try {
            const imageId = req.body.id;
            const eventId = req.params.event;

            const result        = yield EventMediaService.removeImage(imageId);
            const removedImage  = _.first(result.rows);

            if (removedImage.file_type === EventMediaService.imagesTypes.MAIN_LOGO) {
                yield EventMediaService.removeSmallLogo(eventId);
            }

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    })
};
