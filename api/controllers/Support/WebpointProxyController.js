'use strict';

const request     = require('request-promise');
const querystring = require('querystring');

const CODE   = '1EC2BED67EA7';
const WP_URI = 'https://webpoint.usavolleyball.org/wp15/API/MbrData.wp';

module.exports = {
    //get /api/webpoint/:code
    getWebpointData: function (req, res) {
        let code = req.params.code;

        if(code !== CODE) {
            return res.status(200).json({result: "error", "code": "23","message": "Code validation is not passed"})
        }

        return request({
            method  : req.method,
            url     : `${WP_URI}?${querystring.stringify(req.query)}`,
        })
            .then(response  => res.send(response))
            .catch(err      => res.send(err))
    }
};
