'use strict';


const QRTIcketsUtils = require('../../lib/QRTicketsGenerator');
const validationSchemas = require('../../validation-schemas/tickets');
const { FEE_PAYER } = require('../../constants/payments');
const PG_CURRENCY_FORMAT = 'FM$999,999,999,990D00';

module.exports = {
    // get /api/sales/event/:event/ticket/:code/payment/info
    paymentInfo: function (req, res) {
        let $event_id = parseInt(req.params.event, 10),
            $code = parseInt(req.params.code, 10);

        if (!$event_id) return res.validation('event id required');
        if (!$code) return res.validation('No ticket barcode provided');

        let query =
            `SELECT 
                e.event_tickets_code,
                p.is_ticket,
                p.is_payment,
                p.wristband_serial,
                p.linked_purchase_id,
                p.net_profit,
                p.registration_status,
                (e.tickets_settings ->> 'require_covid_test_for_each_ticket')::BOOLEAN IS TRUE require_covid_test_for_each_ticket,
                e.long_name "event_name", TO_CHAR(e.date_start, 'Dy, Mon DD') "event_date",
                el.name "event_location", e.name "event_short_name", e.email "event_email",
                TO_CHAR( (p.created::timestamptz AT TIME ZONE e.timezone), 'Dy, Mon DD, YYYY HH12:MI:SS AM') purchased, 
                TO_CHAR(p.ticket_barcode, '999-999-999') ticket_barcode, p.ticket_barcode "barcode",
                TO_CHAR( 
                    (p.scanned_at::timestamptz AT TIME ZONE e.timezone), 
                    'HH12:MI AM on Mon DD'
                ) scanned_at_timezone,
                TO_CHAR( 
                    (p.date_refunded::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) date_refunded, 
                TO_CHAR( 
                    (p.canceled_date::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) canceled_date, 
                TO_CHAR( 
                    (COALESCE(p.dispute_created, p2.dispute_created)::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) dispute_created, 
                TO_CHAR( 
                    (p.received_date::timestamptz AT TIME ZONE e.timezone), 
                    'Mon DD, YYYY HH12:MI:SS, AM'
                ) received_date,
                (   
                    CASE
                        WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                            AND COALESCE((e.tickets_settings ->> 'require_coupon')::BOOLEAN, false) IS TRUE
                        THEN
                        (SELECT ROW_TO_JSON("c_row")
                            FROM (
                                SELECT UPPER(tc.code) "code", rt.team_name, 
                                ARRAY_TO_JSON(
                                    ARRAY_AGG(tcr.first || ' ' || tcr.last || ' <' || tcr.email || '>')
                                        FILTER ( WHERE tcr.email IS NOT NULL )
                                ) receivers
                                FROM purchase_ticket pt
                                    JOIN ticket_coupon_receiver tcr ON pt.ticket_coupon_id = tcr.ticket_coupon_id
                                    JOIN ticket_coupon tc ON tc.ticket_coupon_id = tcr.ticket_coupon_id
                                    LEFT JOIN roster_team rt 
                                        ON rt.roster_team_id = tcr.roster_team_id 
                                        AND rt.event_id = p.event_id                                   
                                WHERE pt.purchase_id = p.purchase_id
                                GROUP BY tc.code, rt.team_name
                            ) "c_row"
                        )
                        WHEN  (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                            AND (
                                SELECT eec.is_active 
                                FROM event_ticket_buy_entry_code_settings eec 
                                WHERE eec.event_id = p.event_id
                            )::BOOLEAN IS TRUE
                        THEN
                        (
                            SELECT ROW_TO_JSON("c_row")
                            FROM (
                                SELECT UPPER(pt.ticket_buy_entry_code) "code", rt.team_name
                                FROM purchase_ticket pt
                                 LEFT JOIN roster_team rt 
                                    ON LOWER(rt.organization_code) = LOWER(pt.ticket_buy_entry_code)
                                    AND rt.event_id = p.event_id
                                    AND rt.deleted IS NULL
                                WHERE pt.purchase_id = p.purchase_id 
                            ) "c_row"
                        )    
                        ELSE '{}'::JSON
                    END                         
                ) "coupon",
                (
                    CASE
                        WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS NOT TRUE
                            THEN '{}'::JSON
                        ELSE 
                        (SELECT ROW_TO_JSON("p_row")
                            FROM (
                                   SELECT
                                     payment_row.purchase_id,
                                     (payment_row.stripe_payment_type = 'connect') "use_connect",
                                     payment_row.stripe_charge_id                  "charge_id",
                                     payment_row.amount,
                                     payment_row.type,
                                     payment_row.stripe_fee,
                                     payment_row.collected_sw_fee,
                                     COALESCE(payment_row.amount_refunded, 0)      "amount_refunded",
                                     payment_row.user_id,
                                     payment_row.additional_fee_amount,
                                     e.stripe_tickets_fee_payer "stripe_fee_payer",
                                     e.tickets_sw_fee_payer "sw_fee_payer"
                                   FROM purchase "payment_row"
                                   WHERE payment_row.purchase_id = p.linked_purchase_id
                                    AND payment_row.is_payment IS TRUE
                                 ) "p_row") 
                    END             
                                                  
                ) "payment_row",
                ( CASE WHEN (e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE THEN
                    (
                        SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t")))
                          FROM (
                              SELECT FORMAT('%s %s', p1.first, p1.last) "name", p1.ticket_barcode "barcode", et.label,
                              pt.purchase_ticket_id, et.event_ticket_id "id",
                                pt.available, pt.quantity, 
                                pt.amount, pt.ticket_price, pt.discount, 
                                COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0)::REAL "app_fee",
                                (
                                    CASE 
                                        WHEN (pt.quantity = 0) AND (pt.amount > 0)
                                            THEN pt.amount
                                        ELSE 0
                                    END
                                ) "cancellation",
                                p1.status = 'canceled' "is_canceled",
                                pt.ticket_discount_id "discount_id"
                          FROM purchase p1
                            LEFT JOIN purchase_ticket pt
                              ON p1.purchase_id = pt.purchase_id
                            LEFT JOIN event_ticket et
                              ON et.event_ticket_id = pt.event_ticket_id
                          WHERE p1.linked_purchase_id = p.linked_purchase_id
                            AND p1.is_ticket IS TRUE
                          ORDER BY p1.first, p1.last
                          ) "t"
                    )
                    ELSE null
                    END
                ) "tickets_in_payment",
                p.first, p.last, p.status, p.type, p.email, p.zip, p.phone, p.source, 
                COALESCE(p.dispute_status, p2.dispute_status) "dispute_status",
                COALESCE(p.debt, 0)::NUMERIC "debt", p.scanner_id, p.scanner_location, 
                (EXTRACT(EPOCH FROM (p.scanned_at)) * 1000)::BIGINT "scanned_at",
                (REGEXP_REPLACE(p.tickets_scan, '(?:\r\n|\r|\n)+', '<br/>', 'g'))  history, 
                p.amount, p.tickets_additional additional_fields, p.purchase_id, p.user_id, p.event_id, 
                TO_CHAR(p.amount::NUMERIC, '${PG_CURRENCY_FORMAT}') amount_formatted, ( 
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(t))) 
                    FROM ( 
                        SELECT  pt.purchase_ticket_id, et.label, et.event_ticket_id "id",
                                pt.available, pt.quantity, et.short_label, et.ticket_type,
                                TO_CHAR(
                                    pt.has_covid_test::TIMESTAMPTZ AT TIME ZONE e.timezone, 'Mon DD, YYYY HH12:MI AM'
                                ) has_covid_test,
                                pt.amount, pt.ticket_price, pt.discount, pt.registration_status,
                                to_char(pt.ticket_price::NUMERIC, '${PG_CURRENCY_FORMAT}') price_formatted,
                                COALESCE(NULLIF(et.application_fee, 0), e.tickets_sw_fee, 0)::REAL "app_fee",
                                ec.event_camp_id "camp_id", ec.name "camp_name", (
                                    CASE 
                                        WHEN (pt.quantity = 0) AND (pt.amount > 0)
                                            THEN pt.amount
                                        ELSE 0
                                    END
                                ) "cancellation",
                                pt.ticket_discount_id "discount_id"
                        FROM purchase_ticket pt 
                        LEFT JOIN event_ticket et 
                            ON et.event_ticket_id = pt.event_ticket_id 
                        LEFT JOIN "event_camp" ec 
                            ON ec.event_camp_id = et.event_camp_id
                        WHERE pt.purchase_id = p.purchase_id 
                        ORDER BY et.sort_order, et.event_ticket_id 
                    ) t 
                ) tickets, (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(h))) 
                    FROM (
                        SELECT 
                            TO_CHAR((ph.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI:SS, AM')
                                                                                                             "created",
                            TO_CHAR((ph.operation_date::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI:SS, AM')
                                                                                                             "op_date",
                            ph.description, ph.notes, ph.amount, ph.action, ph.check_num, (
                                CASE
                                    WHEN ph.action = 'debt.paid' THEN TRUE
                                    ELSE FALSE
                                END
                            ) "is_income"
                        FROM "purchase_history" ph 
                        WHERE ph.purchase_id = p.purchase_id 
                            AND ph.action IN ('debt.paid', 'debt.refunded')
                        ORDER BY ph.created DESC
                    ) "h"
                ) "debt_history",
                e.tickets_purchase_additional_fields event_fields, (
                    CASE 
                        WHEN e.ticket_camps_registration IS TRUE THEN 'camps'
                        ELSE 'tickets'
                    END
                ) "sales_type",
                e.tickets_has_barcodes "scan_available", (
                    CASE
                        WHEN (p.is_payment IS TRUE AND p.stripe_percent > 0)
                            THEN ROUND(p.stripe_percent / 100, 4)
                        WHEN (p.is_payment IS NOT TRUE AND p2.stripe_percent > 0)
                            THEN ROUND(p2.stripe_percent / 100, 4)    
                        ELSE 0
                    END
                 )::REAL "stripe_percent",
                COALESCE(e.stripe_tickets_fixed, 0)::REAL "stripe_fixed",
                e.stripe_tickets_fee_payer "stripe_fee_payer",
                e.tickets_sw_fee_payer "sw_fee_payer",
                COALESCE((e.tickets_settings->>'require_recipient_name_for_each_ticket')::BOOLEAN, false) AS assigned_tickets_mode,
                p.deactivated_at IS NOT NULL AS is_ticket_deactivated,
                (
                    SELECT notes
                    FROM purchase_history
                    WHERE action IN ('ticket.deactivated', 'ticket.activated')
                    AND purchase_id = p.purchase_id
                    ORDER BY created DESC
                    LIMIT 1
                ) AS deactivate_reason,
                p.collected_sw_fee,
                p.stripe_fee,
                CONCAT(TO_CHAR((arr.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MIam'),
                FORMAT(' (%s)', (SELECT abbrev FROM pg_timezone_names WHERE name = e.timezone))
              ) as refund_request_at,
              arr.reason refund_request_reason
            FROM purchase p 
            LEFT JOIN "event" e 
                ON e.event_id = p.event_id
            LEFT JOIN "event_location" el 
                ON e.event_id = el.event_id 
                AND el.number = 1
            LEFT JOIN purchase p2 ON p.linked_purchase_id = p2.purchase_id
            LEFT JOIN admission_refund_request arr on arr.purchase_id = p.purchase_id
            WHERE p.ticket_barcode = $1 
            AND p.event_id = $2 
            AND p.payment_for = 'tickets'`;

        Db.query(query, [$code, $event_id])
            .then(result => {
                let payment = _.first(result.rows);
                if (_.isEmpty(payment)) {
                    res.status(200).json({
                        payment: {}
                    });
                } else {

                    if (payment.source !== 'api') {
                        generateHash(payment, req, $code);
                    } else {
                        payment.receipt_hash = null
                    }

                    payment.amount = __parse(payment.amount);

                    if (payment.type !=='waitlist') {
                        return findPurchaserInfo(res, payment, req, $code);
                    }

                    res.status(200).json({
                        payment: _.omit(payment, 'user_id')
                    });
                }
            }).catch(res.customRespError.bind(res));
    },

    // post /api/sales/event/:event/ticket/:code/payment/update
    updatePaymentInfo: async function (req, res) {
        let eventID         = Number(req.params.event);
        let ticketBarcode   = req.params.code;

        if (!eventID) {
            return res.customRespError({validation: 'Event ID required'})
        }

        if (!ticketBarcode) {
            return res.customRespError({validation: 'No ticket barcode provided'})
        }

        try {
            const validationResult = validationSchemas.editPayerInfo.validate(req.body);
            if (validationResult.error) {
                throw { validationErrors: validationResult.error.details };
            }
            const payerInfo = validationResult.value;

            const purchaseID = await PaymentService.tickets.editPayerInfo(eventID, ticketBarcode, payerInfo, req.user, true);

            try{
                let paymentData = await PaymentService.getPaymentDataForQRContent(purchaseID, eventID);
                await ApplePassService.updateApplePassForTicket(paymentData)
            }catch(err){
                loggers.errors_log.error('Error updating apple pass err:', err)
            }


            res.status(204).send();
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/sales/event/:event/ticket/:code/resend
    resendTicketReceipt: function (req, res) {
        let eventID = req.params.event,
            code = +req.params.code,
            type = req.query.type; // email/phone

        const isAppleDevice = /iPad|iPhone|iPod|Macintosh/gm.test(req.get('User-Agent'));

        if (!code) {
            return res.validation('Ticket Code required');
        }

        return SWTReceiptService.resendTickets({ eventID, code, type, isAppleDevice })
            .then(() => res.ok())
            .catch(res.customRespError.bind(res));
    },

    // post /api/sales/event/:event/ticket/:code/action/:action
    deactivateTicketBarcode: async function(req, res) {
        try {
            const ticketBarcode                     = Number(req.params.code);
            const action                            = req.params.action;
            const eventID                           = Number(req.params.event);
            const userName                          = `${req.user.first} ${req.user.last}`;
            const userID                            = req.user.user_id;
            const { reason, datetime }              = req.body;

            if (![TicketsService.ACTION.ACTIVATE, TicketsService.ACTION.DEACTIVATE].includes(action)) {
                throw new Error('Invalid Action');
            }

            if (!eventID) {
                throw new Error('Invalid event identifier');
            }

            if (!ticketBarcode || ticketBarcode <= 0) {
                throw new Error('Invalid ticket barcode')
            }

            const validation = validationSchemas.deactivateTicketBarcodeSchema.validate(req.body);

            if (validation.error) {
                throw { validationErrors: validation.error.details };
            }

            const rows = await TicketsService.getTicketInfo(ticketBarcode);

            if (!rows.length) {
                throw new Error('Ticket not found')
            }

            const {
                status,
                purchase_id: purchaseID,
                deactivated_at: deactivatedAt,
            } = rows[0];

            TicketsService.barcode.validateTicket({ action, deactivatedAt, status });

            if (action === TicketsService.ACTION.ACTIVATE) {
                await TicketsService.barcode.activate({
                    ticketBarcode,
                    userName,
                    activatedAt: datetime,
                    reason,
                    eventID
                });
            } else {
                await TicketsService.barcode.deactivate({
                    ticketBarcode,
                    userName,
                    userID, deactivatedAt: datetime,
                    reason,
                    eventID
                });
            }

            await TicketsService.insertPurchaseHistoryRow({
                purchaseID,
                userID,
                action,
                notes: reason,
            });


            try{
                let paymentData = await PaymentService.getPaymentDataForQRContent(purchaseID, eventID);
                await ApplePassService.updateApplePassForTicket(paymentData)
            }catch(err){
                loggers.errors_log.error('Error updating apple pass err:', err)
            }

            res.ok();
        } catch (e) {
            res.customRespError(e);
        }
    },
}

function __parse(n) {
    return (parseFloat(n) || 0);
}

function generateHash(payment, req, $code) {
    payment.receipt_hash = QRTIcketsUtils.generateHash({
        ticket_barcode: $code,
        purchase_id: payment.purchase_id,
        user_id: payment.user_id,
        event_id: payment.event_id
    }, true);
    payment.typeChangeLink = SWTReceiptService.getTypeChangeLink(
        req, payment.event_tickets_code, payment.receipt_hash);
    delete payment.event_tickets_code;
}

function findPurchaserInfo(res, payment, req, $code) {
    const query = squel.select()
        .from('purchase', 'p')
        .field('p.first')
        .field('p.last')
        .field('p.card_name')
        .field('p.email')
        .field('p.phone')
        .field(`
            CASE
              WHEN
                kiosk IS NOT NULL
                AND kiosk <> '{}'::JSONB
                AND status = 'paid'
                THEN TRUE
              ELSE FALSE
            END
        `, 'is_pending_payment')
        .field(`
            CASE
              WHEN
                kiosk IS NOT NULL
                AND kiosk <> '{}'::JSONB
                THEN TRUE
              ELSE FALSE
            END
        `, 'is_kiosk_payment')
        .field(`json_build_object(
            'charge_id', p.stripe_charge_id,
            'card_id', p.stripe_card_id,
            'fingerprint', p.stripe_card_fingerprint
        )`, 'stripe_info')
        .where('p.purchase_id = ?', payment.linked_purchase_id || payment.purchase_id);

    Db.query(query).then(response => {
        const purchaserInfo = _.first(response.rows);
        const omitFields = ['user_id'];

        if (purchaserInfo.is_pending_payment) {
            generateHash(payment, req, $code);
        }

        Object.assign(payment, { purchaser_info:  purchaserInfo });

        if (!needSendFees(payment)) {
            omitFields.push(...['collected_sw_fee', 'stripe_fee']);
        }

        res.status(200).json({
            payment: _.omit(payment, omitFields)
        });
    })
}

function needSendFees({ sw_fee_payer, stripe_fee_payer, assigned_tickets_mode }) {
    const rules = [
        sw_fee_payer === FEE_PAYER.BUYER,
        stripe_fee_payer === FEE_PAYER.BUYER,
        !assigned_tickets_mode
    ];

    return rules.every(rule => rule);
}


