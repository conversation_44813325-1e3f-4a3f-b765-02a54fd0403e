// Check whether event is available
module.exports = function isEventActive (req, res, next) {
    Db.query(
        `SELECT e.event_id
         FROM "event" e
         WHERE e.event_id = $1
            AND e.date_end > (NOW() AT TIME ZONE e.timezone)`, 
        [req.params.event]
    ).then(result => {
        if(result.rowCount === 1) {
            next();
        } else {
            res.validation('Event is deactivated', 403)
        }
    }).catch(err => {
        res.serverError(err);
    })
}
