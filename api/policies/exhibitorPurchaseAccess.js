const knex = require('knex')({client: 'pg'});

/**
 * Checks that request parameters have correct event_id and purchase_id
 * and corresponding purchases sponsor_id value is present in passport.user.sponsor_ids
 * @param req
 * @param res
 * @param next
 * @returns {Promise<*>}
 */
module.exports = async function (req, res, next) {
    const eventID       = Number(req.body.payment.event_id);
    const purchaseID    = Number(req.body.payment.purchase_id);
    const sponsorIDS    = req.session.passport.user.sponsor_ids;

    let forbiddenRules = [
        !eventID,
        !purchaseID,
        !Array.isArray(sponsorIDS),
        _.isEmpty(sponsorIDS)
    ];

    if(forbiddenRules.some(rule => rule)) {
        return res.forbidden();
    }
    try {
        const purchase = await Db.query(
            knex('purchase as p')
                .where('p.event_id', eventID)
                .where('p.purchase_id', purchaseID)
                .whereIn('p.sponsor_id', sponsorIDS)
        ).then(r => r.rows[0]);

        if(purchase) {
            return next();
        }

        return res.forbidden();
    }
    catch(err) {
        res.customRespError(err);
    }
};
