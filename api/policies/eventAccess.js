'use strict';

// TODO: redo, improve validation

/**
 * Create policy middleware for checking access to specific event features
 *
 * @param {(string|string[])} permission one/array of values from api/services/event/operations
 * @param {number=} eventId id of event to check access to. when not defined req.params.event is used
 * @returns {Function}
 */
module.exports = function (permission, eventId) {
    return function (req, res, next) {
        let $event_id       = getEventId(eventId, req),
            $events         = req.user.events,
            $sharedEvents   = req.user.shared_events;

        if(req.user.has_god_role) {
            return next();
        }

        if(!$event_id) {
            return res.validation('No event identifier provided');
        }

        if($events && $events.length && $events.indexOf($event_id) >= 0) {
            return next();
        }

        if($sharedEvents) {
            let eventAccessOpts = $sharedEvents[$event_id];

            // List of permissions check
            if(Array.isArray(permission)) {
                let hasAccess = _.some(permission, p => {
                    return eventAccessOpts && eventAccessOpts.permissions && eventAccessOpts.permissions[p];
                });

                if(!hasAccess) {
                    return res.forbidden('Access denied');
                }

            // One permission check
            } else if(permission && (
                !eventAccessOpts || !eventAccessOpts.permissions || !eventAccessOpts.permissions[permission])
            ) {
                return res.forbidden('Access denied');
            }

            // list of roles here
            if(eventAccessOpts && eventAccessOpts.role_co_owner) {
                return next()
            }
        }

        return res.forbidden('Access denied');
    }
};

function getEventId(eventId, req) {
    let result;
    if(typeof eventId !== 'undefined') {
        result = Number(eventId);
    }
    else {
        result = Number(req.params.event || req.query.event);
    }
    if(Number.isNaN(result)) {
        return null;
    }
    return result;
}
