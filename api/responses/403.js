const nodeutil = require('util');

module.exports = function (error, viewFilePath) {
  var statusCode = 403,
    i, errorToLog, errorToJSON, res = this.res,
    req = res.req;

  var result = {
    status: statusCode
  };

  if (error) {
    var errorsToDisplay = SailsUtilService.normalizeErrors([error]);
    for (i in errorsToDisplay) {

      if (errorsToDisplay[i].original) {
        errorToLog = nodeutil.inspect(errorsToDisplay[i].original);
      } else {
        errorToLog = errorsToDisplay[i].stack;
      }

      errorToJSON = errorsToDisplay[i].original || errorsToDisplay[i].message;
      errorsToDisplay[i] = errorToJSON;
    }
  }

  if (error) {
    result.error = {
      msg: error.message
    }
  }

  if (sails.config.environment === 'development') {
    result.errorRaw = error;
  }

  if (req.wantsJSON) {
    return res.status(result.status).json(result);
  }

  res.status(result.status);

  for (var key in result) {
    res.locals[key] = result[key];
  }

  res.render(viewFilePath, result, function(err) {
    if (err) {
      return res.status(result.status).json(result);
    }

    res.render('500', result);
  });
}
