'use strict';

const redis = require('redis');
const session = require('express-session');
const RedisStore = require('connect-redis')(session);

/**
 * Creates and manages Redis session store
 */
module.exports = {
    /**
     * Get Redis store instance for session middleware
     * @param {Object} sessionConfig - Session configuration
     * @returns {Object|null} Redis store instance or null if not using Redis
     */
    getStore: function(sessionConfig) {
        if (sessionConfig.adapter !== 'redis') {
            return null;
        }

        let clientOptions;

        if (sessionConfig.url) {
            clientOptions = sessionConfig.url;
        } else {
            clientOptions = { path: sessionConfig.socket };
        }

        const redisClient = redis.createClient(clientOptions);

        redisClient.on('ready', () => loggers.debug_log.info('Redis session store client ready'));
        redisClient.on('error', err => loggers.debug_log.info('Redis session store client error', err));

        return new RedisStore({
            client: redisClient,
            ttl: sessionConfig.ttl,
            db: sessionConfig.db,
            pass: sessionConfig.pass,
            prefix: sessionConfig.prefix,
        });
    }
};
