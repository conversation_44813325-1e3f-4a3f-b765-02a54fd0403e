const redis = require('redis');
const swUtils = require('../lib/swUtils');

const client = redis.createClient(sails.config.connections.redis);
swUtils.promisifyAll(redis.RedisClient.prototype);

const HashesService = require('./redis/_RedisHashesService');
const ListsService  = require('./redis/_RedisListsService');

const KEY = {
    prefix: {
        'loggedin': 'logggedin:'
    },
    ttl: 60 * 60 * 24
}

module.exports = {
    getKey: function (key, cb) {
        client.get(key, cb);
    },
    getByPattern: function (pattern, cb) {
        client.keys(pattern, cb);
    },

    // set user's data monitor key: loggedin:userID:sessionID = 0
    // called once in SinginController auth() method
    setUserDataMonitorKey: function (userID, sessionID) {
        let key = KEY.prefix.loggedin + userID + ':' + sessionID;
        return client.setexAsync(key, KEY.ttl, 0);
    },

    // delete user's data monitor key: loggedin:userID:sessionID
    // called once in SinginController signout() method
    delUserDataMonitorKey: function (userID, sessionID) {
        let key = KEY.prefix.loggedin + userID + ':' + sessionID;
        return client.delAsync(key);
    },

    // returns user's data monitor key value if exists otherwise  - null
    // called in passport.deserializeUser() to determine 
    // if user needs to update it's user object: 0 - no update, >0 - update
    getUserDataMonitorKey: function (userID, sessionID) {
        let key = KEY.prefix.loggedin + userID + ':' + sessionID;
        return client.getAsync(key);
    },

    // sets all users datas keys to values > 0 - dirty (needs to update)
    // key pattern: loggedin:userID:*
    // called every time when smth changed in user's data
    // using redis incr to preserve key ttl
    setUserDataMonitorKeysDirty: function (userID) {
        let pattern = KEY.prefix.loggedin + userID + ':*';
        return client.keysAsync(pattern)
            .then((keys) => {
                // Debug logging for Redis key updates
                if (process.env.NODE_ENV === 'development') {
                    console.log(`[DEBUG] Setting dirty keys for user ${userID}, pattern: ${pattern}`);
                    console.log(`[DEBUG] Found ${keys ? keys.length : 0} keys to mark dirty:`, keys);
                }

                if (keys && keys.length > 0) {
                    return Promise.all(keys.map((key) => {
                        return client.incrAsync(key);
                    }))
                } else {
                    if (process.env.NODE_ENV === 'development') {
                        console.log(`[DEBUG] No active sessions found for user ${userID} - user will get fresh data on next login`);
                    }
                    return Promise.resolve();
                }

            });
    },

    // sets user data key to 0 - clear (does not need to update)
    // called after user's updated its user object
    // to avoid further updates from db
    // using redis decrby to preserve key ttl
    setUserDataMonitorKeyClear: function (userID, sessionID) {
        let key = KEY.prefix.loggedin + userID + ':' + sessionID;
        return client.getAsync(key)
            .then((val) => {
                if (val) {
                    return client.decrbyAsync(key, val);
                }
            });
    },

    createTransaction () {
        // multi also need to be promisifed with the promisified conn above
        return swUtils.promisifyAll(client.multi());
    },

    executeTransaction(transaction) {
        return transaction.execAsync();
    },

    hashes: new HashesService(client),
    lists : new ListsService(client),

    async deleteSessionKeys (pattern = '*') {
        let count = 0;
        let cursor = 0;
        const clientSessionsDb = redis.createClient(sails.config.connections.redis);
        await clientSessionsDb.selectAsync(sails.config.session.db);
        do {
            const [_cursor, keys] = await clientSessionsDb.scanAsync(
                cursor,
                'MATCH',
                `${sails.config.session.prefix}${pattern}`,
                'COUNT',
                100
            );
            cursor = Number(_cursor);
            if(keys.length>0) {
                count = await clientSessionsDb.delAsync(...keys) + count;
            }
        } while (cursor)
        await clientSessionsDb.quitAsync();
        return count;
    },
}
