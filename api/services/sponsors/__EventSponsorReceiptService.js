const { APPLICATION_STATUS } = require("../../constants/event-exhibitor");

class EventSponsorReceiptService {
    async getReceipt(eventId, sponsorIds, receiptId) {
        const receiptData = await getReceiptData(eventId, sponsorIds, receiptId);

        return formatReceipt(receiptData);
    }

    async updateReceipt(eventId, sponsorIds, receiptId, receiptData) {
        let tr;

        try {
            tr = await Db.begin();

            const {
                sponsor_id: sponsorId,
                event_exhibitor_invoice_id: exhibitorInvoiceId,
                booths: oldEventBooths
            } = await getEventSponsorReceiptData(eventId, sponsorIds, receiptId, tr);

            const eventBoothsChanged = !_.isEqual(
                _.sortBy(oldEventBooths),
                _.sortBy(receiptData.booths)
            );

            if(eventBoothsChanged) {
                const purchaseData = await EventExhibitorInvoiceService.updatePurchaseData(tr, eventId, sponsorId, receiptId, receiptData);

                receiptData.booths = EventExhibitorInvoiceService.preparePurchaseBoothsData(purchaseData);
            }

            await EventExhibitorInvoiceService.updateEventExhibitorInvoice(
                tr, eventId, sponsorId, exhibitorInvoiceId, receiptData
            );

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }

    async createReceipt(eventId, sponsorId, receiptData) {
        let tr;

        try {
            tr = await Db.begin();

            const { event_exhibitor_id: eventExhibitorId, status } = await getEventExhibitorId(tr, eventId, sponsorId);

            if (!eventExhibitorId) {
                throw { validation: 'Exhibitor application not found' };
            }

            if (status !== APPLICATION_STATUS.APPROVED) {
                throw { validation: 'The Application Status must be approved to generate an invoice' };
            }

            const userAccessIds = { sponsor_id: sponsorId };
            await EventExhibitorService.createEventExhibitorPurchase(tr, eventId, sponsorId, eventExhibitorId, userAccessIds, receiptData);

            await tr.commit();
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    }
}

async function getReceiptData (eventId, sponsorIds, receiptId) {
    const query = knex('purchase AS p')
        .select([
            'p.purchase_id',
            'p.status AS purchase_status',
            'p.type AS purchase_type',
            {
                purchase_booths: knex.raw(`(
                    SELECT COALESCE(array_to_json(array_agg(b)), '[]')
                    FROM(
                        SELECT
                            FORMAT('Custom Booth, $%s', pb.fee) AS label,
                            pb.purchase_booth_id as id,
                            pb.fee AS amount,
                            pb.title,
                            pb.description,
                            pb.quantity,
                            pb.fee,
                            pb.event_booth_id,
                            pb.notes,
                            pb.booth_label
                        FROM purchase_booth AS pb
                        WHERE pb.purchase_id = exi.purchase_id
                            AND pb.event_booth_id IS NULL
                    ) as b
                )`)
            },
            {
                comment: knex.raw(`COALESCE(exi.comment, '')`)
            },
            {
                event_dates: knex.raw(`(
                    SELECT json_object_agg(d.date, false)
                    FROM (
                        SELECT generate_series(e.date_start, e.date_end, '1 day'::interval) as date
                    ) as d
                )`)
            },
            {
                chosen_event_dates: knex.raw(`COALESCE(exi.event_dates, '{}'::JSONB)`)
            },
            {
                event_booths: knex.raw(`(
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(b))), '[]')
                    FROM (
                        SELECT 
                            FORMAT('%s, $%s', eb.title, eb.fee) as label, 
                            eb.event_booth_id AS id, 
                            eb.fee AS amount,
                            eb.title,
                            eb.description
                        FROM event_booth AS eb
                        WHERE eb.event_id = e.event_id
                            AND eb.is_enabled IS TRUE
                    ) AS b
                )`)
            },
            {
                chosen_event_booths: knex.raw(`(
                    SELECT COALESCE(array_to_json(array_agg(b)), '[]')
                    FROM(
                        SELECT
                            FORMAT('%s, $%s', eb.title, eb.fee) AS label,
                            bth.id,
                            eb.fee AS amount,
                            eb.title,
                            eb.description
                        FROM (
                            SELECT unnest(exi.booths) AS id
                        ) as bth
                        JOIN event_booth AS eb ON eb.event_booth_id = bth.id
                    ) as b
                )`)
            },
        ])
        .join('event_exhibitor_invoice as exi', (join) => {
            join.on('exi.purchase_id', 'p.purchase_id')
                .andOnNull('exi.deleted_at')
        })
        .join('event_exhibitor AS ex', 'ex.event_exhibitor_id', 'exi.event_exhibitor_id')
        .join('event as e', 'e.event_id', 'ex.event_id')
        .where('e.event_id', eventId)
        .where('p.purchase_id', receiptId)
        .whereIn('ex.sponsor_id', sponsorIds);

    const {rows: [receiptData]} = await Db.query(query);

    if (_.isEmpty(receiptData)) {
        throw { validation: 'Invoice not found.' };
    }

    return receiptData;
}

function formatReceipt (receiptData) {
    if (_.isEmpty(receiptData)) {
        return {};
    }

    receiptData.event_dates = Object.assign({}, receiptData.event_dates, receiptData.chosen_event_dates);

    if(!_.isEmpty(receiptData.purchase_booths)) {
        receiptData.purchase_booths = EventExhibitorInvoiceService.separateOtherBoothsByBoothQuantity(receiptData.purchase_booths);
        receiptData.chosen_event_booths = receiptData.chosen_event_booths.concat(receiptData.purchase_booths);
    }

    return _.omit(receiptData, ['chosen_event_dates', 'purchase_booths']);
}

async function getEventSponsorReceiptData(eventId, sponsorIds, receiptId, tr) {
    const query = knex
        .select(
            'exi.event_exhibitor_invoice_id',
            'exi.booths',
            'ex.sponsor_id',
        )
        .from('event_exhibitor_invoice AS exi')
        .join('event_exhibitor AS ex', 'ex.event_exhibitor_id', 'exi.event_exhibitor_id')
        .where('ex.event_id', eventId)
        .whereIn('ex.sponsor_id', sponsorIds)
        .whereNull('exi.deleted_at')
        .where('exi.purchase_id', receiptId);

    const {rows: [receiptData = {}]} = await tr.query(query);

    if (_.isEmpty(receiptData)) {
        throw { validation: 'Invoice cannot be updated. Please try again later.' };
    }

    return receiptData;
}

async function getEventExhibitorId(tr, eventId, sponsorId) {
    const query = knex('event_exhibitor')
        .select('event_exhibitor_id', 'status')
        .where('event_id', eventId)
        .where('sponsor_id', sponsorId);

    const { rows } = await tr.query(query);

    return rows[0] || {};
}

module.exports = new EventSponsorReceiptService();
