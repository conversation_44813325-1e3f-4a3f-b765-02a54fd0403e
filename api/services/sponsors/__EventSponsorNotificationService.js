class EventSponsorNotificationService {
    constructor() {}

    get APPLICATION_STATUS () {
        return {
            DECLINED: 'declined',
            APPROVED: 'approved',
            PENDING : 'pending',
        }
    }

    get EXHIBITORS_GROUP_TYPE () {
        return {
            APPLIED: 'exhibitor.applied',
            APPROVED: 'exhibitor.approved',
            DECLINED: 'exhibitor.declined'
        }
    }

    async sendStatusChangeNotification(eventID, exhibitorID, status) {
        let isApproved = status === this.APPLICATION_STATUS.APPROVED;
        let isDeclined = status === this.APPLICATION_STATUS.DECLINED;

        if(isApproved) {
            await this.__sendApprovedNotification(eventID, exhibitorID);
        }

        if(isDeclined) {
            await this.__sendDeclinedNotification(eventID, exhibitorID);
        }
    }

    sendAppliedNotification(eventID, exhibitorID) {
        const templateType  = this.EXHIBITORS_GROUP_TYPE.APPLIED;

        return this.__sendNotification(eventID, exhibitorID, templateType);
    }

    __sendApprovedNotification(eventID, exhibitorID) {
        const templateType  = this.EXHIBITORS_GROUP_TYPE.APPROVED;

        return this.__sendNotification(eventID, exhibitorID, templateType);
    }

    __sendDeclinedNotification(eventID, exhibitorID) {
        const templateType  = this.EXHIBITORS_GROUP_TYPE.DECLINED;

        return this.__sendNotification(eventID, exhibitorID, templateType);
    }

    __sendNotification(eventID, exhibitorID, templateType) {
        const templateGroup = AEMService.EXHIBITORS_GROUP;
        const filters       = { exhibitorID };

        return AEMSenderService.sendTriggerNotification(templateGroup, templateType, eventID, filters).catch(() => {});
    }
}

module.exports = new EventSponsorNotificationService();
