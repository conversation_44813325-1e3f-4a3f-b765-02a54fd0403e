'use strict';

// NOTE: this service is deprecated. Need to be removed.

var purchaseDataManager = {
    saveNote: function (purchase_id, note, cb) {
        var query = 
            'UPDATE purchase SET notes = $1 ' +
            ' WHERE purchase_id = $2';
        return Db.query(query, [note, purchase_id]).then(function () {
            if(cb) {
                cb();
            }
        }).catch(err => {
            if(cb) {
                cb(err);
            } else {
                throw err;
            }
        })
    }
};

module.exports = purchaseDataManager;
