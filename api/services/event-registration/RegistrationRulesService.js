
const CustomFormsRulesCheck = require('./registration-rules/CustomFormsRuleCheck');

class RegistrationRulesService {
    customForms = CustomFormsRulesCheck;
    async checkAdditionalRules ({ eventID, masterClubID }) {
        const eventSettings = await this.#getEventRulesSettings(eventID);

        await this.customForms.check(eventSettings?.custom_forms_requires_submitting, eventID, masterClubID);    }


    async #getEventRulesSettings (eventID) {
        let query = `
            SELECT COALESCE(
                    (SELECT JSONB_AGG(JSONB_BUILD_OBJECT(
                        'custom_form_event_id', cfe.custom_form_event_id,
                        'type', cfe.type
                    ))
                FROM custom_form_event cfe
                WHERE cfe.type IN ('team_assign_for_event')
                  AND cfe.published IS NOT NULL
                  AND cfe.event_id = $1), '[]'::JSONB) custom_forms_requires_submitting
            FROM event
            WHERE event_id = $1`;

        const { rows: [settings] } = await Db.query(query, [eventID]);

        return settings;
    }
}

module.exports = new RegistrationRulesService();
