'use strict';
/* jshint eqnull: true */

const ClubsCustomActions 		= require('./custom_actions/_ClubsCustom');
const SWTCustomActions 			= require('./custom_actions/_SWTCustom');
const OfficialsCustomActions 	= require('./custom_actions/_OfficialsCustom');
const GeneralCustomActions      = require('./custom_actions/_GeneralCustom');
const TicketsRefundsVariablesCustomAction = require('./custom_actions/_TicketsRefundsCustom');
const TicketsPaymentsCustomAction = require('./custom_actions/_TicketsPaymentCustom');
const TeamsUncollectedFeePaymentCustomAction = require('./custom_actions/_TeamsUncollectedFeePaymentCustom');
const TicketsUncollectedFeePaymentCustomAction = require('./custom_actions/_TicketsUncollectedFeePaymentCustom');
const UpcomingUncollectedFeePaymentCustom = require('./custom_actions/_UpcomingUncollectedFeePaymentCustom');

const TMPL_FIELDS_CONTAINING_VARIABLES = [
	{ field: 'html', 			'use_raw': false },
 	{ field: 'text', 			'use_raw': true },
 	{ field: 'subject', 		'use_raw': true }
];
const TEMPLATE_VARIABLE_REG_EXP = /{\w+}/g;

let getCustomActionPerformer = tmplGroup => {
	switch (tmplGroup) {
		case AEMService.CLUB_GROUP:
			return ClubsCustomActions;
		case AEMService.TICKET_GROUP:
		case AEMService.CAMP_GROUP:
			return SWTCustomActions;
		case AEMService.OFFICIAL_GROUP:
			return OfficialsCustomActions;
		case AEMService.TICKETS_REFUNDS_GROUP:
			return TicketsRefundsVariablesCustomAction;
        case AEMService.TICKETS_PAYMENTS_GROUP:
            return TicketsPaymentsCustomAction;
        case AEMService.TEAMS_UNCOLLECTED_FEE_PAYMENTS_GROUP:
            return TeamsUncollectedFeePaymentCustomAction;
        case AEMService.TICKETS_UNCOLLECTED_FEE_PAYMENTS_GROUP:
            return TicketsUncollectedFeePaymentCustomAction;
        case AEMService.UPCOMING_UNCOLLECTED_FEE_PAYMENTS_GROUP:
            return UpcomingUncollectedFeePaymentCustom;
		default:
			throw new Error(`Unknown group "${tmplGroup}"`);
	}
}

module.exports.getVariableValues = function (
    tmplGroup, valuesObj, variableObj
) {
    let value;
    let performer;

    if (variableObj.custom_action) {

        if(GeneralCustomActions.isGeneralCustomAction(variableObj.field)) {
            performer = GeneralCustomActions.generalVariablesCustomAction;
        } else {
            performer = getCustomActionPerformer(tmplGroup);
        }

        let {raw, formatted} = performer(variableObj, valuesObj);

        return {raw, formatted};
    } else {
        value = valuesObj[variableObj.field];
        return {raw: value, formatted: _.escape(value)};
    }
}

module.exports.getVariablesValues = function (tmplGroup, template, variablesList, valuesObj) {
    let variables = {};

    if (Array.isArray(variablesList) && (variablesList.length > 0)) {
        for (let variableObj of variablesList) {
            const {raw, formatted} = this.getVariableValues(tmplGroup, valuesObj, variableObj);
            variables[variableObj.pattern] = {
                raw,
                formatted,
            };
        }
    }

    return variables;
}

module.exports.hasVariables = function (str) {
	return TEMPLATE_VARIABLE_REG_EXP.test(str);
}

/**
 * Check if the input string contains unrecognized variables
 * 
 * @param {string} inputStr - The input string to check for unrecognized variables
 * @param {string[]} variablesPatternsList - The list of allowed variables
 */
module.exports.checkForUnknownVariables = function (inputStr, variablesPatternsList) {
    if (toString.call(inputStr) !== '[object String]') {
        throw new Error('Expecting "input" to be a string');
    }

    if (!Array.isArray(variablesPatternsList)) {
        throw new Error('Expecting "variables" to be an array of strings');
    }

    if (inputStr.length === 0) {
        return false;
    }

    if (variablesPatternsList.length === 0) {
        return this.hasVariables(inputStr);
    }

    let foundVariables = inputStr.match(TEMPLATE_VARIABLE_REG_EXP);

    if (foundVariables == null) {
        return false;
    }

    for (let _foundVar of foundVariables) {
        if (variablesPatternsList.indexOf(_foundVar) === -1) {
            return true;
        }
    }

    return false;
}

module.exports.checkTemplateForUnknownVariables = function (template, variables) {
	let variablesPatternsList = variables.map(v => v.pattern);

	for (let _fieldObj of TMPL_FIELDS_CONTAINING_VARIABLES) {

		let _res = this.checkForUnknownVariables(template[_fieldObj.field], variablesPatternsList);

		if (_res) {
			return true;
		}
	}

	return false;
}

/**
 * Get the template's html, text and subject and replaces existing 
 * variables in them whith corresponding values
 * 
 * @param  {Object} template                - An object containing template's data (see below)
 * @param  {string} template.html           - Template's html version
 * @param  {string} template.text           - Template's text version
 * @param  {string} template.subject        - Template's subject
 * @param  {Object[]} variablesList         - Array of objects representing variables
 * @param  {string} variablesList[].field   - A field name in valuesObj
 * @param  {string} variablesList[].title   - A variable's name
 * @param  {string} variablesList[].pattern - A placeholder to be replaced with a value
 * @param  {Object} valuesObj               - Values of variables
 * @param  {string} tmplGroup               - A group of the template (e.g. clubs, officials, teams)
 * 
 * @return {Object}                         - Result of the substitution (html, text, subject)
 */
module.exports.transformVariables = function (tmplGroup, template, variablesList, valuesObj) {
    let transformedContent = {};

    for (let _fieldObj of TMPL_FIELDS_CONTAINING_VARIABLES) {
        let _field = _fieldObj.field;
        transformedContent[_field] = template[_field];
    }

    for(const {field, use_raw} of this.TMPL_FIELDS_CONTAINING_VARIABLES) {
        if(!transformedContent[field]) {
            continue;
        }
        for (let variableObj of variablesList) {
            const {raw, formatted} = this.getVariableValues(tmplGroup, valuesObj, variableObj);
            for (let fieldObj of TMPL_FIELDS_CONTAINING_VARIABLES) {
                transformedContent[field] = transformedContent[field].replace(
                    new RegExp(variableObj.pattern, 'g'),
                    use_raw ? raw : formatted
                )
            }
        }
    }

    return transformedContent;
}

module.exports.checkVariablesInTemplateSubject = function (subject, variables) {
    if(!Array.isArray(variables)) {
        throw new Error('Variables should be array');
    }

    if(!_.isString(subject)) {
        throw new Error('Subject should be string');
    }

    for(let variable of variables) {
        if(__variableIsInSubject(subject, variable)) {
            if(!variable.is_available_for_subject) {
                throw { validation: `Variable ${variable.title} is not allowed to be in subject!` };
            }

            if(__subjectHasVariablesDuplicates(subject, variable)) {
                throw { validation: `Variable ${variable.title} has duplicates in subject!` };
            }
        }
    }

    function __subjectHasVariablesDuplicates (subject, variable) {
        let regex = new RegExp(`(${variable.pattern}).*\\1`,"g");

        return regex.test(subject);
    }

    function __variableIsInSubject (subject, variable) {
        return subject.includes(variable.pattern);
    }
}

module.exports.general = GeneralCustomActions;

module.exports.TMPL_FIELDS_CONTAINING_VARIABLES = TMPL_FIELDS_CONTAINING_VARIABLES;
