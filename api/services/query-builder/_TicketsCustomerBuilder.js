'use strict';

const QueryFilters  = require('./tickets-customer-builder/QueryFilters');

const PG_CURRENCY_FORMAT 	= 'FM$999,999,999,990D00';

class TicketsCustomerQueryBuilder {
	constructor (commonQueryParts) {
	    this.commonQueryParts = commonQueryParts;
    }

    get __filters () {
        return QueryFilters;
    }

    get TEST_RECEIVER() {
        return SQLQueryBuilderUtils.markTestData({
            event_name: 'Test Event Name',
            participant_name: '<PERSON>',
            payer: '<PERSON>',
            camps_names_list: 'Libero Camp, Hitter Camp, All Skills Camp, Prospects Camp, Serve and Pass Camp',
            tickets_names_list: 'Weekend Passes, Daily Passes',
            unform_barcode: 123456789,
            user_id: 100,
            event_id: 100,
            purchase_id: 100,
        })
    }

    _isAgeSearchStr (searchStr) {
        searchStr = searchStr.trim();

        return !_.isNaN(searchStr) && Number(searchStr) > 0 && String(searchStr).length === 2;
    }

    _addFiltersToQuery (query, data, isCamp) {
        this.__filters.add(query, data, isCamp);
    }

    list (data, aliases, isCamp) {
        let _aliases = _.isEmpty(aliases)?{}:aliases;
        let query = squel.select().from('purchase', 'p')

            .field(`TO_CHAR(p.ticket_barcode, '999-999-999')`, _aliases.barcode || 'ticket_barcode')
            .field(`TO_CHAR(p.amount::NUMERIC, '${PG_CURRENCY_FORMAT}')`, _aliases.amount || 'amount')
            .field(`TO_CHAR((p.created::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI AM')`,
                                                                    _aliases.purchased || 'purchased')
            .field(`COALESCE(p.first,'')`, 'buyer_first')
            .field(`COALESCE(p.last,'')`, 'buyer_last')
            .field('p.first', _aliases.first || 'first')
            .field('p.last', _aliases.last || 'last')
            .field(`COALESCE(p.tickets_additional->>'first_name','')`, 'player_first')
            .field(`COALESCE(p.tickets_additional->>'last_name','')`,  'player_last')
            .field(`COALESCE(p.tickets_additional->>'position','')`,  'player_position')
            .field('p.email', _aliases.email || 'email')
            .field('p.zip', _aliases.zip || 'zip')
            .field('p.phone', _aliases.phone || 'phone')
            .field('p.status', _aliases.status || 'status')
            .field('COALESCE(p.dispute_status, p2.dispute_status)', _aliases.status || 'dispute_status')
            .field('p.registration_status')
            /**
             * Is this nedeed?
             * AT TIME ZONE e.timezone
             */
            .field(`TO_CHAR((p.scanned_at::TIMESTAMPTZ AT TIME ZONE e.timezone), 'Mon DD, YYYY HH12:MI AM')`,
                                                                    _aliases.scanned_at || 'scanned_at')
            .field('p.scanner_id', _aliases.scanner_id || 'scanner_id')
            .field('p.scanner_location', _aliases.scanner_location || 'scanner_location')
            .field(`
                    (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(w))) 
                    FROM ( SELECT ptt.purchase_ticket_id as "id", ptt.quantity as "qty",
                                  ptt.available as "available", ptt.canceled as "canceled",
                                  ptt.registration_status
                           FROM purchase_ticket ptt
                           INNER JOIN event_ticket ett ON ett.event_ticket_id = ptt.event_ticket_id
                           INNER JOIN event_camp ecp ON ett.event_camp_id = ecp.event_camp_id
                           WHERE p.purchase_id = ptt.purchase_id and ptt.registration_status = 
                          '${PaymentService.tickets.participation.REGISTRATION_STATUS.ACTIVE}'
                            AND ecp.visibility IN ('eo', 'published')) w
                    )`,'checkin')

            .field(`
                    (SELECT CASE COUNT(pt.purchase_ticket_id) FILTER (WHERE pt.quantity > 0)
                                 WHEN 1 THEN SUM(pt.purchase_ticket_id) FILTER (WHERE pt.quantity > 0)
                                 ELSE 0
                            END :: INT as purchase_ticket_id
                    )`,'purchase_ticket_id')

            .field(`
               (
                   SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(y)))
                   FROM
                   (
                    SELECT  COALESCE(NULLIF(v.short_label,''), v.label) as column_label,
                            v.field,
                            p.tickets_additional->>v.field as value
                    FROM v_swt_tickets_purchase_additional_as_table v
                    WHERE v.event_id = p.event_id AND v.payment_list = true
                   ) y
               )`, 'additional')

            .where(`p.payment_for = 'tickets'`)
            .where(`
                CASE WHEN e.ticket_camps_registration IS TRUE 
                    THEN (
                            SELECT ec.visibility IN ('eo', 'published') 
                            FROM event_camp ec 
                            WHERE et.event_camp_id = ec.event_camp_id
                         )
                    ELSE TRUE
                END`
            )
            .group('p.purchase_id, e.timezone, e.event_id, p2.purchase_id');

        this._addFiltersToQuery(query, data, isCamp);

        if(isCamp) {
            query.field(`
               (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(u)))
                    FROM
                    (
                    SELECT age, ARRAY_AGG(age_for) as age_for
                    FROM
                            (SELECT purchase_id, age, name || ', On Date: ' || TO_CHAR(age_date ,'Mon DD, YYYY') as age_for
                            FROM v_swt_participant_age
                            WHERE purchase_id = p.purchase_id
                            ) v
                    WHERE purchase_id = p.purchase_id
                    GROUP BY age
                    ORDER BY age
                    ) u
               )`,'ages')
        }

        query
            .join('event'              , 'e'   , 'e.event_id = p.event_id')
            .join('purchase_ticket'    , 'pt'  , `pt.purchase_id = p.purchase_id`)
            .join('event_ticket'       , 'et'  , 'et.event_id = p.event_id AND pt.event_ticket_id = et.event_ticket_id')
            .left_join('purchase'      , 'p2'  , 'p.linked_purchase_id = p2.purchase_id');
        
        if(data.has_refund_request) {
            query.join('admission_refund_request', 'adm_ref', 'adm_ref.purchase_id = p.purchase_id')
        }
        
        return query;
    }

	emailReceiversList (eventID, receiversData, { templateID, emailSendingID, emailCategory }) {
	    let filters = Object.assign({}, {event_id: eventID}, receiversData);

	    filters.camp = Number(filters.camp);
	    filters.type = Number(filters.type);

		let query = this.list(filters, null, filters.is_camp)
            .join('event_owner', 'eo', 'eo.event_owner_id = e.event_owner_id')
            .join('user', 'u', 'u.user_id = eo.user_id')
            .field(
                `(
                    SELECT 
                        FORMAT(
                            '{ "camps": "%s", "tickets": "%s" }',
                            STRING_AGG(ec.name, ', '),
                            STRING_AGG(et.label, ', ')
                        )::JSON
                    FROM "event_ticket" et
                    LEFT JOIN "event_camp" ec   
                        ON ec.event_camp_id = et.event_camp_id
                        AND ec.visibility IN ('eo', 'published')
                    INNER JOIN "purchase_ticket" pt 
                        ON pt.event_ticket_id = et.event_ticket_id
                        AND pt.purchase_id = p.purchase_id 
                    WHERE et.event_id = e.event_id
                    ${receiversData.purchase_ticket ? `AND pt.purchase_ticket_id = '${+receiversData.purchase_ticket}'` : ''}
                )`, 'types_names')
            .field('p.purchase_id')
            .field(
                `FORMAT('%s %s', p.tickets_additional->>'first_name', p.tickets_additional->>'last_name')`,
                'participant_name'
            )
            .field('p.ticket_barcode', 'unform_barcode')
            .field('p.user_id')
            .field(`FORMAT('%s %s', p.first, p.last)`, 'payer')
            .field(`FORMAT('"%s %s" <%s>', p.first, p.last, p.email)`, 'receiver')
            .field('p.event_id')
            .field('e.long_name', 'event_name')
            .field(`FORMAT('"%s %s" <%s>', u.first, u.last, u.email)`, 'replyto')
            .field('p.first')
            .field('p.last')
            .field('p.email')
            .group('u.user_id')
            .where('p.email IS NOT NULL');

        if (templateID > 0) {
        	query.where(
                `NOT EXISTS(
                  SELECT ee.event_email_id
                  FROM "event_email" ee 
                  WHERE ee."event_id" = e.event_id 
                    AND ee."tickets_customer_user_id" = p."user_id"
                    AND ee."email_template_id" = ?
                )`, 
                templateID
            );
        }

        if(emailSendingID) {
            query
                .left_join(
                    'email_sending_history',
                    'esh',
                    `esh.email_sending_id = '${emailSendingID}' AND esh.email_address = u.email`
                )
                .field('esh.email_address IS NULL', 'is_unique_recipient')
                .group('esh.email_address');
        }

        if(EmailService.emailCategory.hasUnsubscribeLink(emailCategory)) {
            query = this.commonQueryParts.excludeUnsubscribedEmails(query, 'p.email', 'e.event_id');
        }

        let resultQuery =
            squel.select()
                .field('*')
                .field(`"types_names"->>'camps'`    , 'camps_names_list')
                .field(`"types_names"->>'tickets'`  , 'tickets_names_list')
            .from(query, 'd')

        return resultQuery;
	}

    getTestReceiver (eventID) {
	    if (!eventID) {
            return this.TEST_RECEIVER;
        }

        const query = squel.select()
            .from('event', 'e')
            .field('e.long_name', 'event_name')
            .field(`FORMAT('%s %s', p.tickets_additional->>'first_name', p.tickets_additional->>'last_name')`, 'participant_name')
            .field(`FORMAT('%s %s', p.first, p.last)`, 'payer')
            .field('p.ticket_barcode', 'unform_barcode')
            .field('p.user_id')
            .field('e.event_id')
            .field('p.purchase_id')
            .field(
                squel.select()
                    .field(`STRING_AGG(et.label, ', ')`)
                    .from('event_ticket', 'et')
                    .where('et.event_id = e.event_id')
            , 'tickets_names_list')
            .field(
                squel.select()
                    .field(`STRING_AGG(ec.name, ', ')`)
                    .from('event_ticket', 'et')
                    .left_join('event_camp', 'ec', 'ec.event_camp_id = et.event_camp_id')
                    .where('et.event_id = e.event_id')
            , 'camps_names_list')
            .left_join('purchase', 'p',
                squel.expr()
                    .and('p.purchase_id = ?',
                        squel.select()
                            .from('purchase')
                            .field('purchase_id')
                            .where('event_id = e.event_id')
                            .where('ticket_barcode IS NOT NULL')
                            .where('first IS NOT NULL')
                            .where('last IS NOT NULL')
                            .limit(1)
                    )
            )
            .where('e.event_id = ?', eventID);

        return Db.query(query).then(({ rows: [receiver] }) => {
            if (!receiver) {
                return this.TEST_RECEIVER;
            }

            return SQLQueryBuilderUtils.replaceReceiverEmptyValuesWithTestData(receiver, this.TEST_RECEIVER);
        })
    }
}


module.exports = TicketsCustomerQueryBuilder;
