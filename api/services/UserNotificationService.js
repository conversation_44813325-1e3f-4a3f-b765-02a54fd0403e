class UserNotificationService {
    constructor() {
        this.TABLE_NAME = 'user_notifications';
    }

    /**
     * Creates a new notification for a user
     * @param {number} userId - The ID of the user
     * @param {string} messageType - Type of notification message
     * @param {string} messageText - The notification message
     * @param {Object} additionalData - Additional JSON data for the notification
     * @returns {Promise<number>} The created notification ID
     */
    async create({ userId, messageType, messageText, additionalData = {} }) {
        try {
            const additionalDataPrepared = this.#prepareAdditionalData(additionalData);
            const query = knex(this.TABLE_NAME)
                .insert({
                    user_id: userId,
                    message_type: messageType,
                    message_text: messageText,
                    additional_data: additionalDataPrepared
                })
                .returning('user_notification_id');

            const result = await Db.query(query);
            return result?.rows[0];
        } catch (error) {
            throw error;
        }
    }

    /**
     * Gets the last unread notification for a user
     * @param {number} userId - The ID of the user
     * @returns {Promise<Object|null>} The notification object or null if none found
     */
    async getLastUnreadNotification(userId) {
        try {
            const query = knex(this.TABLE_NAME)
                .select(
                    'user_notification_id',
                    'message_type',
                    'message_text',
                    'additional_data'
                )
                .where({
                    user_id: userId,
                    is_read: false
                })
                .orderBy('created_at', 'desc')
                .limit(1);

            const result = await Db.query(query);
            return result?.rows[0] || null;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Marks a notification as read for a user
     * @param {number} userId - The ID of the user
     * @param {number} notificationId - The ID of the notification
     * @returns {Promise<boolean>} True if notification was marked as read
     */
    async markAsRead(userId, notificationId) {
        try {
            const query = knex(this.TABLE_NAME)
                .update({ is_read: true })
                .where({
                    user_notification_id: notificationId,
                    user_id: userId
                })
                .returning('user_notification_id');

            await Db.query(query);
        } catch (error) {
            throw error;
        }
    }

    #prepareAdditionalData(additionalData) {
        return knex.raw('COALESCE(?::JSONB, \'{}\' ::JSONB)', [
            _.isObject(additionalData)
                ? JSON.stringify(additionalData)
                : `${additionalData}`
        ]);
    }
}

module.exports = new UserNotificationService();
