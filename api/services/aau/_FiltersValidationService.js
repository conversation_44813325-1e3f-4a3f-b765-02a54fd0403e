const moment = require('moment');

const {filterSchema} = require("../../validation-schemas/aau-filters");
const AAUUtils = require("../../lib/AAUUtilsService");

class AauFiltersValidationService {
    constructor (UtilsService) {
        this.Utils = UtilsService;
    }

    validate (filters) {
        if(_.isEmpty(filters)) {
            throw { validation: 'Filters are required' };
        }

        const { error } = filterSchema.validate(filters);

        if (error) {
            throw { validation: error.details[0].message };
        }

        this.__validateDateOfBirth(filters);
        this.__validateLastName(filters);
        this.__validateMembershipIdentifier(filters);
        this.__validateClubCode(filters);
        this.__validateZipCode(filters);
    }

    __validateDateOfBirth (filters) {
        if(filters[this.Utils.AAU_FIELDS.BIRTH_DATE]) {
            if(!moment(filters[this.Utils.AAU_FIELDS.BIRTH_DATE], 'YYYY-MM-DD').isValid()) {
                throw { validation: 'Birth Date is not in valid format' };
            }

            if(!filters[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]) {
                throw { validation: 'Membership Identifier required' };
            }
        }
    }

    __validateLastName (filters) {
        if(filters[this.Utils.AAU_FIELDS.LAST_NAME]) {
            if(!filters[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]) {
                throw { validation: 'Membership Identifier required' };
            }
        }
    }

    __validateZipCode (filters) {
        if(filters[this.Utils.AAU_FIELDS.ZIP_CODE]) {
            if(!filters[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]) {
                throw { validation: 'Membership Identifier required' };
            }
        }
    }

    __validateClubCode (filters) {
        if(filters[this.Utils.AAU_FIELDS.CLUB_CODE]) {
            if(!filters[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]) {
                throw { validation: 'Membership Identifier required' };
            }

            if(!filters[this.Utils.AAU_FIELDS.ZIP_CODE]) {
                throw { validation: 'Zip Code required' };
            }
        }
    }

    __validateMembershipIdentifier (filters) {
        if(filters[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]) {
            if(filters[this.Utils.AAU_FIELDS.CLUB_CODE] && !filters[this.Utils.AAU_FIELDS.ZIP_CODE]) {
                throw { validation: 'Zip Code required' };
            }

            if(!filters[this.Utils.AAU_FIELDS.CLUB_CODE]) {
                if(!filters[this.Utils.AAU_FIELDS.ZIP_CODE] &&
                    !filters[this.Utils.AAU_FIELDS.LAST_NAME] &&
                    !filters[this.Utils.AAU_FIELDS.BIRTH_DATE]) {
                    throw {validation: 'One of the fields Club Code, Zip Code, Birth Date, Last Name required'};
                }
            }
        }
    }
}

module.exports = new AauFiltersValidationService(AAUUtils);
