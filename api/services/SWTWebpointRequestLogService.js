'use strict';

const CALLER = 'SWT';

const crypto = require('crypto');

module.exports = {
    XHashHeader: function (res, $eventTicketsCode) {
        const hash = crypto.randomBytes(32).toString('hex') + '.' + $eventTicketsCode;
        res.header('X-hash', hash);
    },

    extractEventCode : function(hash) {
        let idx = (hash || '').lastIndexOf('.');

        if (idx != -1) {
            let event_tickets_code = hash.slice(idx + 1);
            return event_tickets_code;
        }
        return null;
    },

    log: function (usavCode, wpResponse, client ) {
        
        client = client || {};

        let userAgent = client.userAgent || null,
            ip = client.ip|| null,
            hash = client.hash || null,
            event_tickets_code = this.extractEventCode(hash);
        
        let sql = `
                INSERT INTO webpoint_request_log 
                (hash, usav_code, response, event_id, ip, user_agent, caller, created, modified)
                SELECT $1 , $2 , $3, e.event_id, $5, $6, $7, LOCALTIMESTAMP, LOCALTIMESTAMP
                FROM event e WHERE e.event_tickets_code = $4
            `,
            params = [hash, usavCode, JSON.stringify(wpResponse), event_tickets_code, ip, userAgent, CALLER];

        Db.query(sql, params)
            .catch(err => loggers.errors_log.error(err));

    }
};
