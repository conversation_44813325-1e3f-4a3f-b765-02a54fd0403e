const TYPE_KEY = '__t';
const VALUE_KEY = 'v';

const replacer = (key, value) => {
    if(typeof value === 'undefined') {
        return {
            [TYPE_KEY]: 'undefined',
        };
    }
    if(_.isNaN(value)) {
        return {
            [TYPE_KEY]: 'NaN',
        };
    }
    if(value instanceof Date) {
        return {
            [TYPE_KEY]: 'Date',
            [VALUE_KEY]: value.getTime(),
        };
    }
    return value;
};

const reviver = (key, value) => {
    if(_.isObject(value) && TYPE_KEY in value) {
        switch(value[TYPE_KEY]) {
            case 'undefined':
                return void 0;
            case 'NaN':
                return Number.NaN;
            case 'Date':
                return new Date(value[VALUE_KEY]);
        }
    }
    return value;
};

class Serializer {
    static serialize(value) {
        return JSON.stringify(value, replacer);
    }

    static deserialize(text) {
        return JSON.parse(text, reviver);
    }
}

module.exports = Serializer;
