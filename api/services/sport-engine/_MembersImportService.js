const ImportProcessService = require('./members-import/_ImportProcessService');
const {CA_COUNTRY_CODE} = require("../../constants/common");

class MembersImportService {
    constructor(DataService, QueueService, SportEngineUtils) {
        this.DataService = DataService;
        this.QueueService = QueueService;
        this.Utils = SportEngineUtils;

        this.process = new ImportProcessService(this.DataService, this.QueueService, this.Utils);
        this.queue = this.QueueService;
    }

    async create (masterClubID, clubOwnerID, params) {
        if(!masterClubID) {
            throw { validation: 'Master Club ID required' };
        }

        if(!clubOwnerID) {
            throw { validation: 'Club Owner ID required' };
        }

        if(
            !params ||
            !params.option ||
            ![this.Utils.IMPORT_MODE.DEFAULT, this.Utils.IMPORT_MODE.INSERT].includes(params.option)
        ) {
            throw { validation: 'Invalid option value' };
        }

        const clubHasSEActiveImport = await this.QueueService.getClubImport(masterClubID);

        if(!_.isEmpty(clubHasSEActiveImport)) {
            throw { validation: 'Sport Engine Import is already running' };
        }

        const clubHasAAUActiveImport = await AauMemberService.import.queue.getClubImport(masterClubID);

        if(!_.isEmpty(clubHasAAUActiveImport)) {
            throw { validation: 'AAU Import is already running' };
        }

        let clubData = await this.getUSAVClubData(masterClubID, clubOwnerID);

        if(_.isEmpty(clubData)) {
            throw { validation: 'Club not found' };
        }

        await this.__validateClubData(clubData, masterClubID);

        return this.QueueService.add(masterClubID, params.option);
    }

    getUSAVClubData (masterClubID, clubOwnerID) {
        let query = knex('master_club AS mc')
            .select({
                country: 'mc.country',
                [this.Utils.SE_FIELDS.CLUB_CODE]: 'mc.code',
                [this.Utils.SE_FIELDS.USAV_REGION]: 'mc.region',
                [this.Utils.SE_FIELDS.USAV_CODE]: 'mc.director_usav_code',
                [this.Utils.SE_FIELDS.BIRTHDATE]: knex.raw(`TO_CHAR(mc.director_birthdate, 'YYYY-MM-DD')`)
            })
            .where('mc.master_club_id', masterClubID)
            .where('mc.club_owner_id', clubOwnerID)

        return Db.query(query).then(result => result && result.rows[0] || null);
    }

    async __validateClubData (clubData, masterClubID) {
        const { memberData: sportEngineData, validationError } = await this.__processMember(clubData);

        if(!_.isEmpty(validationError)) {
            throw { validation: validationError.validation };
        }

        if(_.isEmpty(sportEngineData)) {
            throw { validation:
                    `You're trying to import members from SportEngine but member with USAV 
                    ${clubData[this.Utils.SE_FIELDS.USAV_CODE]} and Role=Coach not found in SportEngine for club 
                    ${clubData[this.Utils.SE_FIELDS.CLUB_CODE]} from region ${clubData[this.Utils.SE_FIELDS.USAV_REGION]}.
                    Please contact SW support for assistance.`
            }
        }

        return this.__updateClubData(sportEngineData, masterClubID);
    }

    __processMember(clubData) {
        if(clubData.country === CA_COUNTRY_CODE) {
            return this.__getCanadianMemberData(clubData);
        } else {
            return this.__getMemberData(clubData);
        }
    }

    __getCanadianMemberData(clubData) {
        let params = {
            club_code: clubData[this.Utils.SE_FIELDS.CLUB_CODE],
            region: clubData[this.Utils.SE_FIELDS.USAV_REGION]
        };

        return SportEngineMemberService.validation.processCanadianMember(params);
    }

    __getMemberData(clubData) {
        const params = {
            usav_code: clubData[this.Utils.SE_FIELDS.USAV_CODE],
            birthdate: { date: clubData[this.Utils.SE_FIELDS.BIRTHDATE] },
            club_code: clubData[this.Utils.SE_FIELDS.CLUB_CODE],
            region: clubData[this.Utils.SE_FIELDS.USAV_REGION]
        };

        return SportEngineMemberService.validation.processMember(
            params,
            SportEngineMemberService.validation.MEMBER_TYPE.clubDirector
        );
    }

    __updateClubData (sportEngineData, masterClubID) {
        let query = knex('master_club AS mc')
            .update({ sportengine_club_id: sportEngineData[this.Utils.SE_FIELDS.CLUB_ID] })
            .where('mc.master_club_id', masterClubID)

        return Db.query(query);
    }
}

module.exports = MembersImportService;
