'use strict';

/**
 * SessionService
 * Handles common session management operations
 */
module.exports = {
    /**
     * Clears user session completely - clears cookies, removes Redis keys, logs out user
     * @param {Object} req - Request object
     * @param {Object} res - Response object
     * @returns {Promise<void>}
     */
    clearSession: async function(req, res) {
        await sails.helpers.clearAuthCookies(res)
            .catch(err => loggers.errors_log.error(err));

        if (req.user?.user_id && req.sessionID) {
            RedisService.delUserDataMonitorKey(req.user.user_id, req.sessionID)
                .catch(ErrorSender.defaultError.bind(ErrorSender));
        }

        await new Promise(resolve => {
            req.logout(err => {
                if (err) loggers.errors_log.error(err);
                resolve();
            });
        });

        if (req.session) {
            await new Promise(resolve => {
                req.session.destroy(err => {
                    if (err) loggers.errors_log.error(err);
                    resolve();
                });
            });
        }
    }
};
