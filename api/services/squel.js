'use strict';

var squel = require('squel').useFlavour('postgres');
var _     = require('lodash');

const moment = require('moment');

var select = squel.select;
var update = squel.update;
var insert = squel.insert;
var remove = squel.remove;
var delete_ = squel.delete;

var defaults = {
    tableAliasQuoteCharacter    : '"',
    autoQuoteTableNames         : true,
    nameQuoteCharacter          : '"',
    replaceSingleQuotes         : true, 
    singleQuoteReplacement      : "''",

    // https://github.com/hiddentao/squel/issues/373
    numberedParameters          : false,
};

// https://github.com/hiddentao/squel/issues/373
const _oldToParamString = squel.cls.QueryBuilder.prototype._toParamString;
squel.cls.QueryBuilder.prototype._toParamString = _toParamString;


squel.select = function(opts) {
    var defs = _.clone(defaults);
    return select(_.assign(defs, opts));
};
squel.insert = function(opts) {
    var defs = _.clone(defaults);
    return insert(_.assign(defs, opts));
};
squel.update = function(opts) {
    var defs = _.clone(defaults);
    return update(_.assign(defs, opts));
};
squel.delete = function(opts) {
    var defs = _.clone(defaults);
    return delete_(_.assign(defs, opts));
};
squel.remove = function(opts) {
    var defs = _.clone(defaults);
    return remove(_.assign(defs, opts));
};

squel.registerValueHandler(Date, function (date) {
    /**
     * Docs: https://momentjs.com/docs/#/parsing/utc/
     */
    return moment.utc(date).format('YYYY-MM-DD');
});

module.exports = squel;

function _toParamString (options = {}) {
    const _options = { ...this.options, ...options };

    let result = _oldToParamString.bind(this)(options);

    if (!_options.nested) {
        // If default behaviour is disabled, run updated code
        if(!_options.numberedParameters) {
            let i = (undefined !== options.numberedParametersStartAt)
                ? options.numberedParametersStartAt
                : 1;

            result.text = result.text
                .split(_options.parameterCharacter)
                .reduce(
                    (result, part, index) => {
                        if (index > 0) {
                            // all quotes are closed
                            if (result.quotes % 2 === 0) {
                                result.parts.push(`${_options.numberedParametersPrefix}${i++}`);
                            }
                            else {
                                result.parts.push(_options.parameterCharacter);
                            }
                        }

                        result.parts.push(part);
                        result.quotes += part.split(`'`).length - 1;

                        return result;
                    },
                    {parts: [], quotes: 0}
                )
                .parts
                .join('');
        }
    }

    return result;
}
