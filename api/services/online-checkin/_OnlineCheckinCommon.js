'use strict';

const crypto = require('crypto');
const path = require('path');
const QRCodeGenerator = require('../../lib/QRCodeGenerator');
const { CHECKIN_BARCODE: { MAX_ATTEMPTS, ID_LENGTH } } = require('../../constants/online-checkin');

const qrCodeGenerator = new QRCodeGenerator({
    s3Path: '/images/qrcode/',
    localPath: path.resolve(__dirname, '..', '..', '..', '..', 'data', 'tickets', 'qrcodes'),
});

class OnlineCheckinCommon {
    constructor () {}

    async generateCheckinBarcode(season) {
        loggers.debug_log.verbose('Generating barcode ...');

        let attempts = 0;

        while (attempts < MAX_ATTEMPTS) {
            const barcode = this.#generateBarcode(ID_LENGTH);

            const isBarcodeExists = await this.#barcodeExists(barcode, season);

            if (!isBarcodeExists) {
                return barcode;
            }

            attempts++;
        }

        throw new Error('Exceed iterations limit on Checkin Barcode Gen');
    }

    async generateQRCode (imageName) {
        loggers.debug_log.verbose('Generating QR Code ...');
        const maxUploadAttempts = 2;
        const imageFileName = `${imageName}.png`;

        await qrCodeGenerator.createQRCode(imageName, imageFileName, maxUploadAttempts);
    }

    #generateBarcode(length) {
        return crypto
            .randomBytes(Math.ceil(length / 2))
            .toString('hex')
            .slice(0, length);
    }

    async #barcodeExists(barcode, season) {
        const result = await Db.query(
            `SELECT "master_staff_id" 
             FROM "master_staff" ms 
             WHERE ms.checkin_barcode = $1 
                 AND ms.season = $2 
                 AND ms.deleted IS NULL`,
                [barcode, season]
        );

        return result.rowCount > 0;
    }
}

module.exports = new OnlineCheckinCommon();
