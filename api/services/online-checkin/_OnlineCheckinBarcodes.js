'use strict';

const Hashids     = require("hashids");
const hashids     = new Hashids(sails.config.swt.receiptSault);
const argv 		  = require('optimist').argv;
const co          = require('co');

class OnlineCheckinBarcodes {
    constructor () {}

    get QR_CONTENT_PREFIX () {
        return 'SWTm'
    }

    get QR_CODE_PATH () {
        return 'images/qrcode/';
    }

    get IMAGE_LINK () {
        return `${sails.config.urls.home_page.baseUrl}/${this.QR_CODE_PATH}`;
    }

    get DESCRIPTION_PAGE_LINK () {
        return `http://localhost:3000/online-checkin/`;
    }

    getEmailSubject (eventName) {
        return 'Your Entry Pass for ' + eventName;
    }

    resendPrimaryStaffEmail (rosterTeamID, masterStaffID, contacts) {
        if(!rosterTeamID) {
            return Promise.reject({ validation: 'Roster Team ID required' });
        }

        if(!masterStaffID) {
            return Promise.reject({ validation: 'Master Staff ID required' });
        }

        return this.__getCheckinEmailData__(rosterTeamID, masterStaffID)
            .then(this.__setStafferBarcode__.bind(this))
            .then(this.__addCheckinLinks__.bind(this))
            .then(this.__generateNotificationContacts__.bind(this, contacts))
            .then(data => {
                const notifications = [];

                if (data.recipient) {
                    notifications.push(this.__sendCheckinLetter__({
                        masterStaffID,
                        eventID: data.event_id,
                        recipient: data.recipient
                    }));
                }

                if(data.phone) {
                    notifications.push(this.__sendCheckinSms__(data))
                }

                return Promise.all(notifications);
            });
    }

    getOnlineCheckin (hash) {
        if(!hash) {
            return Promise.reject({ validation: 'There is no Checkin Identifier' });
        }

        let decodedData = hashids.decode(hash);


        if(decodedData.length !== 3) {
            return Promise.reject({ validation: 'Hash Identifier is not recognized' });
        }

        let eventTeamCheckinID  = decodedData[0],
            eventID             = decodedData[1],
            created             = decodedData[2];

        if(!eventTeamCheckinID) {
            return Promise.reject({ validation: 'Online Checkin Identifier has no team checkin identifier' });
        }

        if(!eventID) {
            return Promise.reject({ validation: 'Online Checkin Identifier has no event' });
        }

        if(!created) {
            return Promise.reject({ validation: 'Online Checkin Identifier has no reference to check in' });
        }
        return this.__getOnlineCheckinData__(eventTeamCheckinID, eventID, created)
            .then(this.__addCheckinLinks__.bind(this));
    }

    getDescriptionLink({barcode, event_id}) {
        return `${this.DESCRIPTION_PAGE_LINK}${barcode}/event/${event_id}`;
    }

    getImageLink({barcode, event_id}) {
        return `${this.IMAGE_LINK}${this.QR_CONTENT_PREFIX}${barcode}.png`;
    }

    __getOnlineCheckinData__ (eventTeamCheckinID, eventID, created) {
        let query =
            `SELECT
              e.long_name AS "event_name",
              FORMAT('%s %s', ms.first, ms.last) AS staff_name,
              e.website AS "event_website",
              rt.team_name,
              ms.checkin_barcode AS "barcode",
              e.name AS event_short_name
            FROM event_team_checkin etc
            LEFT JOIN master_staff ms 
              ON etc.master_staff_id = ms.master_staff_id
            LEFT JOIN roster_team rt
              ON etc.roster_team_id = rt.roster_team_id
            LEFT JOIN event e
              ON e.event_id = etc.event_id
            WHERE etc.event_team_checkin_id = $1
             AND etc.event_id = $2
             AND extract(epoch from etc."created")::INTEGER = $3`;

        return Db.query(query, [eventTeamCheckinID, eventID, created])
            .then(result => result.rows[0] || null);
    }

    __addCheckinLinks__ (data) {
        if(!data) {
            throw { validation: 'No data found' }
        }

        data.descriptionLink    = this.getDescriptionLink(data);
        data.imageLink          = this.getImageLink(data);

        return data;
    }

    __setStafferBarcode__ (staffer) {
        return co(function* () {
            if (!staffer) {
                /* I think, we should throw an error here as this is not a valid case */
                return staffer;
            }

            let barcode = staffer.barcode;
            if (!barcode) {
                barcode         = yield (OnlineCheckinService.common.generateCheckinBarcode());
                const imageName = `${this.QR_CONTENT_PREFIX}${barcode}`;

                let query = squel.update().table('master_staff', 'ms')
                    .set('checkin_barcode', barcode)
                    .where('ms.master_staff_id = ?', staffer.master_staff_id)
                    .where('ms.deleted IS NULL');

                const doesStafferUpdated = yield (Db.query(query).then(result => result.rowCount > 0));

                if(!doesStafferUpdated) {
                    throw { validation: `Staffer's barcode is not set`}
                }

                yield (OnlineCheckinService.common.generateQRCode(imageName));

                staffer.barcode = barcode;
            } else {
                /*
                * To handle a bug when "barcode" exists but QR image is not generated,
                * we send request to generate QR image here.
                * s3 does not throw error if image already exists.
                */
                yield (OnlineCheckinService.common.generateQRCode(
                                                            `${this.QR_CONTENT_PREFIX}${barcode}`));
            }

            staffer.barcode_border_color = 'green';
            if(Number(staffer.role_id) === OnlineCheckinService.team.CHAPERONE_ROLE_ID) {
                if(staffer.chaperone_qr_code_border) {
                    staffer.barcode_border_color = staffer.chaperone_qr_code_border_color;
                }
                else {
                    staffer.barcode_border_color = null;
                }
            }

            return staffer
        }.bind(this));
    }

    __getCheckinEmailData__ (rosterTeamID, masterStaffID) {
        let query =
            squel.select().from('master_staff', 'ms')
                .field(`e.event_id`)
                .field(`FORMAT('%s %s', ms.first, ms.last)`, 'recepient_name')
                .field(`FORMAT('%s %s', ms.first, ms.last)`, 'staff_name')
                .field('ms.checkin_barcode', 'barcode')
                .field('e.website', 'event_website')
                .field('e.long_name', 'event_name')
                .field('e.online_team_checkin_mode', 'checkin_mode')
                .field('rt.team_name')
                .field('ms.master_staff_id')
                .field('e.name', 'event_short_name')
                .field('ms.first')
                .field('ms.last')
                .field('rsr.role_id')
                .field(`(e.teams_settings->>'chaperone_qr_code_border')::BOOLEAN`, 'chaperone_qr_code_border')
                .field(`e.teams_settings->>'chaperone_qr_code_border_color'`, 'chaperone_qr_code_border_color')
                .left_join('roster_team', 'rt', `rt.roster_team_id = ${rosterTeamID}`)
                .left_join('event', 'e', 'e.event_id = rt.event_id')
                .left_join('roster_staff_role', 'rsr', 'rsr.roster_team_id = rt.roster_team_id AND rsr.master_staff_id = ms.master_staff_id')
                .where('rsr.deleted IS NULL')
                .where('rsr.deleted_by_user IS NULL')
                .where('ms.master_staff_id = ?', masterStaffID);

        return Db.query(query).then(result => result.rows[0] || null);
    }

    __sendCheckinLetter__ (data) {
        const templateType =
            AEMService.CLUBS_GROUP_TYPE.PRIMARY_STAFF_ONLINE_CHECKIN_TYPE;

        const templateGroup = AEMService.CLUB_GROUP;

        const receiversFilters = {
            master_staff_id: data.masterStaffID,
            template_type: templateType,
            recipient: data.recipient
        };

        return AEMSenderService.sendTriggerNotification(
            templateGroup,
            templateType,
            Number(data.eventID),
            receiversFilters
        ).catch((err) => {
            loggers.errors_log.error('Error resending checkin letter', JSON.stringify(err, null, 2));
            throw err;
        });
    }

    __sendCheckinSms__ (data) {
        loggers.debug_log.verbose(`Sending sms to ${data.phone} ...`);

        return SmsService.sendSms(data.descriptionLink, data.phone)
    }

    __generateNotificationContacts__ ({ phone, email }, staffer) {
        if (email) {
            staffer.recipient = `"${staffer.first} ${staffer.last}" <${email}>`;
        }

        if (phone) {
            staffer.phone = phone;
        }

        return _.omit(staffer, ['first', 'last']);
    }
}

module.exports = new OnlineCheckinBarcodes();
