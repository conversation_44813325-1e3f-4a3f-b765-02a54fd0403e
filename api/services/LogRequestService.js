'use strict'

class RequestLogger {
	constructor () {}

	get SWT_API_TYPE () {
		return 'swt_api_access';
	}

	get SWT_API_ERROR_TYPE () {
		return 'swt_api_error';
	}

	get STRIPE_CONNECT_TYPE () {
		return 'stripe_connect';
	}

	get EVENT_UPDATE_TYPE () {
		return 'event_update';
	}

	get RESULTS_SAVING_TYPE () {
		return 'results_save';
	}

	get CLUB_UPDATING_TYPE () {
		return 'club_update';
	}

	get CLUB_CREATION_TYPE () {
		return 'club_create';
	}

	// covered 😄👍
	_stringifyObject (obj) {
		return _.isEmpty(obj)?null:JSON.stringify(obj).replace(/'/g, "''");
	}

	// covered 😄👍
	_formatReqData (req) {
		let data;

		let method = req.method && req.method.toLowerCase();

		switch (method) {
	        case 'get': 
	            data = this._stringifyObject(req.query);
	            break;
	        case 'put':
	        case 'post':
	            data = this._stringifyObject(req.body);
	            break;
	        default:
	            data = null;
	            break;
	    }

	    return data;
	}

	// covered 😄👍
	log (type, req) {
		if (!type) {
			return Promise.reject({ validation: 'Request Type required' });
		}

		if (_.isEmpty(req)) {
			return Promise.reject({ validation: 'Request object required' });
		}

		let ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || null;

		let userAgent = req.headers['user-agent'] || null;

		let data = this._formatReqData(req);

		return Db.query(
	        `INSERT INTO "public"."request_log" ("type", "request_url", "verb", "remote_ip", "user_agent", "data")
	         VALUES (
	            $1, $2, $3, $4, $5, $6
	         )`,
	        [type, req.path, req.method, ip, userAgent, data]
	    ).then(() => {});
	}

	// covered 😄👍
	logSWT (req) {
		return this.log(this.SWT_API_TYPE, req);
	}

	// covered 😄👍
	logSWTError (req) {
		return this.log(this.SWT_API_ERROR_TYPE, req);
	}

	// covered 😄👍
	logStripeConnectHook (req) {
		return this.log(this.STRIPE_CONNECT_TYPE, req);
	}

	// covered 😄👍
	logEventUpdate (req) {
		return this.log(this.EVENT_UPDATE_TYPE, req);
	}

	// covered 😄👍
	logResultsSaving (req) {
		return this.log(this.RESULTS_SAVING_TYPE, req);
	}

	// covered 😄👍
	logClubUpdating (req) {
		return this.log(this.CLUB_UPDATING_TYPE, req);
	}

	// covered 😄👍
	logClubCreation (req) {
		return this.log(this.CLUB_CREATION_TYPE, req);
	}

}

module.exports = new RequestLogger();