const EMAIL_CATEGORIES = {
    transactional: 'transactional',
    informational: 'informational',
    marketing: 'marketing',
};
const DEFAULT_SENDER = '"SportWrench" <<EMAIL>>';

const EMAIL_CATEGORY_SETTINGS = {
    marketing: {
        sender: '"SportWrench" <<EMAIL>>',
        hasReplyTo: true,
        hasUnsubscribeLink: true,
    },
};

module.exports = {
    EMAIL_CATEGORIES,

    getEmailSender(emailCategory) {
        return EMAIL_CATEGORY_SETTINGS[emailCategory]?.sender ?? DEFAULT_SENDER;
    },

    hasReplyTo(emailCategory) {
        return EMAIL_CATEGORY_SETTINGS[emailCategory]?.hasReplyTo ?? false;
    },

    hasUnsubscribeLink(emailCategory) {
        return EMAIL_CATEGORY_SETTINGS[emailCategory]?.hasUnsubscribeLink ?? false;
    },
};
