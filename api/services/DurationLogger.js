class DurationLogger {
    constructor(processName) {
        this.processName = processName;
    }

    start() {
        this.startTime = new Date();
        loggers.debug_log.verbose(`${this.processName} started at ${this.startTime.toISOString()}`);
    }

    end() {
        this.endTime = new Date();
        loggers.debug_log.verbose(`${this.processName} finished at ${this.endTime.toISOString()} (${this.formatDuration()})`);
    }

    formatDuration() {
        const duration = (this.endTime.getTime() - this.startTime.getTime()) / 1000;
        return `${duration.toFixed(3)}s`;
    }
}

module.exports = DurationLogger;
