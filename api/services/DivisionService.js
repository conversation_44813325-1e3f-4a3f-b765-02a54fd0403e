
const { USAV_SANC_BODY, ENTRY_STATUSES } = require('../constants/teams');
const { HOUSING_STATUS_CODES } = require('../constants/housing');
const {divisionSchema} = require("../validation-schemas/division");

const IMAGE_TYPE = 'flowchart';

module.exports = {
    getPath: (eventId, divisionId, fileName) => `/images/${eventId}/flow_charts/${divisionId}/${fileName}`,
    createDivision: async function(eventId, data) {
        const division = _validateDivisionData(data);

        await _checkDivisionNamingUniqueness(eventId, null, division);

        const createdDivision = await _insertDivisionRow(division, eventId);

        if(_.isEmpty(createdDivision)) {
            throw { validation: 'Internal error, please, try again later' };
        }

        return _.omit(createdDivision, 'created', 'modified')
    },
    updateDivision: async function(eventId, divisionId, params) {
        const division = _validateDivisionData(params.division);

        await _checkDivisionNamingUniqueness(eventId, divisionId, division);

        await _seasonalityIsAllowedToChange(eventId, divisionId, division.seasonality);

        await _saveDivisionFlowChart(eventId, divisionId, params.fileName);

        const updatedDivision = await _updateDivisionRow(division, eventId, divisionId);

        if(_.isEmpty(updatedDivision)) {
            throw { validation: 'Division not found' };
        }

        return _.omit(updatedDivision, 'created', 'modified');
    },
    getEventDivisionsQuery: function (params) {
        let { order, sortDirection, search, short, eventID} = params;

        let sql = squel.select().from('division', 'd').where('d.event_id = ?', eventID);

        let isFull = `(CASE WHEN d.max_teams <= 
                        (
                            SELECT COUNT(rtm.*) FROM "roster_team" rtm 
                            WHERE rtm.division_id = d.division_id AND rtm.event_id = ${eventID}
                                AND rtm.status_entry = 12 AND rtm.deleted IS NULL
                        )
                      THEN TRUE ELSE FALSE
                      END) "is_full"`;
        let accepted = `(
                        SELECT COUNT(rts.roster_team_id) FROM roster_team rts 
                        WHERE rts.division_id = d.division_id 
                           AND rts.deleted IS NULL 
                           AND rts.status_entry = 12 
                    )::INTEGER`;

        if (short) {
            sql.field('d.division_id').field('d.name').field('d.event_id').field(isFull).field('d.max_teams')
                .field(accepted, 'accepted').field('d.reg_fee::INTEGER', 'reg_fee')

        } else {
            sql.field('d.division_id').field('d.name').field('d.event_id')
                .field('d.max_teams').field('d.gender').field('d.reg_fee')
                .field('d.published').field('d.closed').field('d.short_name')
                .field('d.is_qualifying').field('d.seasonality')
                .field(accepted, 'accepted')
                .field(
                    `(
                        SELECT COUNT(rts.roster_team_id) FROM "roster_team" rts 
                        WHERE rts.division_id = d.division_id 
                            AND rts.deleted IS NULL 
                            AND rts.status_entry <> 11 
                     )::INTEGER`, 'entered')
                .field(
                    `(
                        SELECT count(rts.status_entry) FROM roster_team rts 
                        WHERE rts.division_id = d.division_id 
                           AND rts.deleted IS NULL 
                           AND rts.status_entry = 14
                    )::INTEGER`, 'wait')
                .field(
                    `(
                        SELECT count(rts.status_paid) FROM roster_team rts 
                        WHERE rts.division_id = d.division_id 
                           AND rts.deleted IS NULL 
                           AND rts.status_paid <> 22
                           AND rts.status_entry <> 11
                    )::INTEGER`, 'unpaid')
                .field(
                    `(
                        SELECT count(rts.status_housing) FROM roster_team rts 
                        WHERE rts.division_id = d.division_id 
                            AND rts.deleted IS NULL 
                            AND rts.status_housing IN (
                                ${HOUSING_STATUS_CODES.VERIFIED}, ${HOUSING_STATUS_CODES.ISSUED}, ${HOUSING_STATUS_CODES.FAULTY}, ${HOUSING_STATUS_CODES.BELOW}
                            )
                            AND rts.status_entry <> 11
                    )::INTEGER`, 'housing')
                .field(isFull)
                .field(`count(d.*) OVER()::INT`, 'count');
        }

        if(!order) {
            sql.order('d.sort_order')
                .order('d.gender')
                .order('d.max_age', false)
                .order('d.level_sort_order')
                .order('d.level')
        }

        if(search) {
            let formattedSearch = '%' + search + '%';

            sql.where('d.name ILIKE ?', formattedSearch);
        }

        let query = squel.select().from(sql, 'divisions');

        if (order && (sortDirection === 'asc' || sortDirection === 'desc')) {
            query.order(order, sortDirection==='asc');
        }

        return query;
    },
    // NOTE: if newDivisionID is empty - EO tries to add accepted teams to full division,
    // else it is division change action
    checkDivisionEntrance: function (teamsFilters, newDivisionID = null) {
        let query = squel.select().from('roster_team', 'rt')
            .field('rt.division_id')
            .field('COUNT(rt.roster_team_id)', 'teams')
            .group('rt.division_id');

        // if change division, need to get only "accepted" teams
        if(newDivisionID && !teamsFilters.teams) {
            teamsFilters.entry = [12];
        }

        // skip teams ordering
        teamsFilters.order = null;

        // generate teams selection query with filters from frontend (division, entry, ... , housing filter)
        let teamsQuery      = SQLQueryBuilder.clubs.list(query, teamsFilters);

        // get event divisions with teams count number in each division
        let divisionsQuery  = this.getEventDivisionsQuery({eventID: teamsFilters.event});

        return Promise.all([
            Db.query(teamsQuery)    .then(result => result.rows),
            Db.query(divisionsQuery).then(result => result.rows)
        ]).then(data => {
            let teams     = data[0];
            let divisions = data[1];

            let groupedTeams = [];

            if(!newDivisionID) {
                /* [{ division_id, teamsQty }, ... ] -> { division_id: [{ division_id, teamsQty }] } */
                groupedTeams = _.groupBy(teams, 'division_id');
            } else {
                groupedTeams = { [newDivisionID]: [{ teams: teams.length}] };
            }

            // determine full divisions
            let fullDivisions = findFullDivisions(groupedTeams, divisions);

            if (!fullDivisions.length) {
                return getFullDivisionErrorMessage(null, newDivisionID);
            }

            return getFullDivisionErrorMessage(fullDivisions[0].name, newDivisionID);
        })
    },
    getFreeDivisionCapacity: function ({ eventID, divisionID, statusEntry }) {
      const query = 
          `SELECT
              d.name,
              (d.max_teams - d.accepted_teams) "free_capacity"
            FROM (
              SELECT
                d.name,
                d.max_teams,
                (
                  SELECT COUNT(rt.*) FROM roster_team rt
                  WHERE rt.event_id = d.event_id
                        AND d.division_id = rt.division_id
                        AND rt.status_entry = $3
                        AND rt.deleted IS NULL
                ) "accepted_teams"
              FROM division d
              WHERE d.event_id = $1 AND d.division_id = $2
              GROUP BY d.max_teams, d.division_id
            ) d`;
              
      const params = [eventID, divisionID, statusEntry];
      
      return Db.query(query, params).then(result => {
        return {
          divisionID,
          name: result.rows[0].name,
          freeCapacity: result.rows[0].free_capacity,
        };
      });
    }
}

function findFullDivisions(teamDivisions, divisions) {
    let fullDivisions = [];

    divisions.forEach(division => {
        let teamsToAssign = teamDivisions[division.division_id] && Number(teamDivisions[division.division_id][0].teams);

        if(teamsToAssign) {
            if((teamsToAssign + division.accepted) > division.max_teams) {
                fullDivisions.push({
                    name            : division.name,
                    max_teams       : division.max_teams,
                    accepted        : division.accepted,
                    addedTeamsCount : teamsToAssign
                })
            }
        }
    });

    return fullDivisions;
}

function getFullDivisionErrorMessage (divisionName, isDivisionChanged) {
    if (!divisionName) {
        return isDivisionChanged
            ? `Can't change division.`
            : `Can't change team entry status to Accepted.`;
    }

    return Boolean(isDivisionChanged)
        ? `Can't change division. Teams limit in ${divisionName} division is reached.`
        : `Can't change team entry status to Accepted. Max Teams per division ${divisionName} is reached.`;
}

function _validateDivisionData (division) {
    const { error, value } = divisionSchema.validate(division);

    if (error) {
        loggers.errors_log.error(error.details);

        throw { validation: error.details[0].message };
    }

    return value;
}

function _saveDivisionFlowChart (eventId, divisionId, fileName) {
    if (!fileName) {
        return;
    }

    const { fileNameWithoutExt, ext } = EventMediaService.getFileData(fileName);
    const DBPath = DivisionService.getPath(eventId, divisionId, fileNameWithoutExt);

    return EventMediaService.saveFlowChart({
        fileType: IMAGE_TYPE,
        filePath: DBPath,
        fileName: fileNameWithoutExt,
        fileExt: ext,
        eventId,
        divisionId
    });
}

function _checkNameUniqueness (event_id, division_id, name, gender) {
    let query =
            'SELECT d.division_id \
            FROM division d \
            WHERE d.event_id = $1 \
                AND trim(from lower(d.name)) = trim(from lower($2)) \
                AND d.gender = $3',
        params= [event_id, name, gender];

    if (division_id) {
        query += ' AND d.division_id <> $4';
        params.push(division_id)
    }

    return Db.query(query, params).then(({rowCount}) => rowCount > 0);
}

function _checkShortNameUniqueness (event_id, division_id, short_name) {
    let query =
            'SELECT d.division_id \
            FROM division d \
            WHERE d.event_id = $1 \
                AND trim(from lower(d.short_name)) = trim(from lower($2))',
        params= [event_id, short_name];

    if (division_id) {
        query += ' AND d.division_id <> $3';
        params.push(division_id);
    }

    return Db.query(query, params).then(({rowCount}) => rowCount > 0);
}

async function _checkDivisionNamingUniqueness (eventId, divisionId, division) {
    const {name, gender, short_name} = division;

    const [hasNameDuplicate, hasShortNameDuplicate] = await Promise.all([
        _checkNameUniqueness(eventId, divisionId, name, gender),
        _checkShortNameUniqueness(eventId, divisionId, short_name)
    ]);

    if(hasNameDuplicate) {
        throw { validation: `Division "${name} - ${gender}" already exists` };
    }

    if(hasShortNameDuplicate) {
        throw { validation: `Short name "${short_name}" exists` };
    }
}

function _insertDivisionRow (division, $event_id) {
    const source = _.omit(division, 'closed'),
        q = squel.insert({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
            .into('division')
            .setFields(source)
            .set('event_id', $event_id)
            .returning('*');

    if (division.closed) {
        q.set('closed', 'NOW()');
    }

    return Db.query(q).then(({rows}) => rows[0] || {});
}

function _updateDivisionRow (division, $event_id, $division_id) {
    const query = squel.update({ replaceSingleQuotes: true, singleQuoteReplacement: "''" })
        .table('division')
        .where('division_id = ?', $division_id)
        .where('event_id = ?', $event_id)
        .setFields(_.omit(division, 'closed'))
        .returning('*');

    if (division.closed) {
        query.set('closed', '(case when closed is null then NOW() else closed end)', {dontQuote: true});
    } else {
        query.set('closed', null);
    }

    return Db.query(query.toString()).then(({rows}) => rows[0] || {});
}

async function _seasonalityIsAllowedToChange (eventID, divisionID, newSeasonalityValue) {
    if(!newSeasonalityValue) {
        return;
    }

    const query = knex('division as d')
        .select({
            has_entered_teams: knex.raw(`COUNT(rt.roster_team_id) > 0`)
        })
        .join('roster_team AS rt', (table) => {
            table.on('rt.division_id', 'd.division_id')
                .andOnNull('rt.deleted')
                .andOn(knex.raw('rt.status_entry <> ?', ENTRY_STATUSES.DECLINED))
        })
        .join('event as e', 'e.event_id', 'd.event_id')
        .where('d.division_id', divisionID)
        .where('d.event_id', eventID)
        .where('d.seasonality', '<>', newSeasonalityValue)
        .where(knex.raw('e.sport_sanctioning_id = ?', USAV_SANC_BODY.USAV));

    const hasEnteredTeams = await Db.query(query).then(result => result?.rows?.[0]?.has_entered_teams);

    if (hasEnteredTeams) {
        throw { validation: `Seasonality can't be changed if there are entered teams` };
    }
}
