'use strict';

const fs        = require('fs');
const path      = require('path');
const argv      = require('optimist').argv;
const numeral   = require('numeral');
const moment    = require('moment');
const utils     = require('../../lib/swUtils');


const IS_PROD = Boolean(argv.prod);
const SW_STRIPE_ACC_STATS_SQL_PATH  = 
                        path.resolve(__dirname, 'sql', 'sw-stripe-account-statistics.sql');
const SW_STRIPE_ACC_STATS_SQL       = fs.readFileSync(SW_STRIPE_ACC_STATS_SQL_PATH, { encoding: 'utf8' });

const ADJUSTMENTS_COL       = '[6] Adjustments';
const INCOME_COL            = '[4.C] - [2] + [4.B] + [6] Income';
const EVENT_ID_COL          = 'ID';
const EVENT_NAME_COL        = 'Name';
const PAYMENT_FOR_COL       = 'For';
const STRIPE_ACC_ID_COL     = 'stripe_account_id';
const STRIPE_ACC_NAME_COL   = 'Stripe Name';

const SW_STRIPE_ACC_STATS_COLS = {
    [EVENT_ID_COL]                      : {     needs_sum: false, width: 49   },
    'Event Owner'                       : {     needs_sum: false, width: 89   },
    [EVENT_NAME_COL]                    : {     needs_sum: false, width: 198  },
    'For'                               : {     needs_sum: false, width: 54   },
    '[1] total'                         : {     needs_sum: true , width: 73   },
    '[2] Stripe Fee'                    : {     needs_sum: true , width: 71   },
    '[4] App Fee'                       : {     needs_sum: true , width: 65   },
    '[4.A] SW Fee Teams'                : {     needs_sum: true , width: 103  },
    '[4.B] SW Fee Tickets'              : {     needs_sum: true , width: 106  },
    '[4.C] EO Stripe Fee'               : {     needs_sum: true , width: 99   },
    'Lost Disputes'                     : {     needs_sum: true , width: 74   },
    '[5] Returned Escrow'               : {     needs_sum: true , width: 106  },
    'Escrow Balance'                    : {     needs_sum: true , width: 85   },
    [ADJUSTMENTS_COL]                   : {     needs_sum: true , width: 95   },
    [INCOME_COL]                        : {     needs_sum: true , width: 139  }
}

const REFUNDED_CHARGES = require('./previous_year_charges_refunded_in_2017');

const NUMBER_FORMAT = '0,0.00';

const DEFAULT_DATE_FORMAT = 'YYYY-MM-DD';

const CURRENT_UTC_YEAR = moment.utc().format('YYYY');

const DEFAULT_AFTER_DATE = `${CURRENT_UTC_YEAR}-01-01`;

const DEFAULT_BEFORE_DATE = `${CURRENT_UTC_YEAR}-12-31`;



module.exports.getSWAccountStats = function (params) {
    params = params || {};

    if (params.after && !utils.isDateValid(params.after, DEFAULT_DATE_FORMAT)) {
        return Promise.reject({ validation: `Invalid date ${params.after}` });
    }

    if (params.before && !utils.isDateValid(params.before, DEFAULT_DATE_FORMAT)) {
        return Promise.reject({ validation: `Invalid date ${params.before}` });
    }

    let purchaseCreatedAfter    = params.after  || DEFAULT_AFTER_DATE;
    let purchaseCreatedBefore   = params.before || DEFAULT_BEFORE_DATE;

    return StripeService.account.getPlatformKeys(IS_PROD)
    .then(({secret_key: sk}) => {
        return Promise.all([
            this.findLostDisputesPenalty(purchaseCreatedAfter, purchaseCreatedBefore),
            Db.query(
                SW_STRIPE_ACC_STATS_SQL, 
                [purchaseCreatedAfter, purchaseCreatedBefore, sk]
            )
        ])
    })
    .then(([ disputesPenalty, dbRes ]) => {
        let lastYearChargesRefunds              = this.getRefundsAdjustments();
        let [
            rows, addedAdjQty, 
            addedAdjAmount, notMappedCharges, adjToMapToStripeAcc
        ] = this.addRefundsAdjustments(dbRes.rows);

        this.addStripeAccAdjustmentsRows(rows, adjToMapToStripeAcc);

        let totalAdjustmentsQty     = REFUNDED_CHARGES.length;
        let totalAdjustmentsAmount  = Math.abs(lastYearChargesRefunds.totals);

        let diff = utils.normalizeNumber(totalAdjustmentsAmount - Math.abs(addedAdjAmount));

        let adjustmentsMsg = `Added ${addedAdjQty} of ${
                totalAdjustmentsQty
            } last year refunds adjustments. Missing ${numeral(diff).format(NUMBER_FORMAT)}`

        return [
            disputesPenalty, 
            rows,
            adjustmentsMsg,
            lastYearChargesRefunds,
            notMappedCharges,
            diff
        ]
    })
    .then(([ 
        disputesPenalty, rows, adjustmentsMsg, lastYearChargesRefunds, notMappedCharges
    ]) => {

        let columns = Object.keys(SW_STRIPE_ACC_STATS_COLS);

        let sutotalsRes = this.getStripeAccSubtotals(columns, rows, SW_STRIPE_ACC_STATS_COLS);
        rows                    = sutotalsRes[0];
        let subtotalRowsIndexes = sutotalsRes[1];

        let totals = 0;
        [rows, totals] = this.cutPropsAndCountTotals(
                columns, rows, SW_STRIPE_ACC_STATS_COLS, NUMBER_FORMAT, subtotalRowsIndexes);

        let adjustedTotals = utils.normalizeNumber(totals[INCOME_COL]);

        adjustedTotals = utils.normalizeNumber(adjustedTotals);
        
        return {
            columns, rows, adjustedTotals,

            settings                        : SW_STRIPE_ACC_STATS_COLS, 
            disputes                        : numeral(disputesPenalty).format(NUMBER_FORMAT), 
            totals                          : this.prepareTotals(totals, columns, 
                                                        SW_STRIPE_ACC_STATS_COLS, NUMBER_FORMAT),
            last_year_charges_refunds       : numeral(lastYearChargesRefunds.totals)
                                                                            .format(NUMBER_FORMAT),
            last_year_charges_refunds_list  : this.sortLastYearRefunds(lastYearChargesRefunds.list),
            
            added_refund_adjustments        : adjustmentsMsg,
            not_mapped_charges              : notMappedCharges
        };
    });
}

module.exports.sortLastYearRefunds = function (refunds) {
    return refunds.sort((a, b) => {
        if (a.stripe_account_name > b.stripe_account_name) {
            return -1;
        } else if (a.stripe_account_name < b.stripe_account_name) {
            return 1;
        } else {
            return 0
        }
    });
}

module.exports.addRefundsAdjustments = function (rows) {

    let addedAdjustments        = 0;
    let addedAdjustmentsAmount  = 0;
    let notMappedCharges        = [];
    let stripeAccAdjsNotInEvent  = new Map();

    const approx = utils.normalizeNumber.bind(utils);

    function findRow (rows, charge) {
        return rows.filter(row => {
            return (Number(row[EVENT_ID_COL]) === charge.event_id) && 
                                                (row[PAYMENT_FOR_COL] === charge.payment_for);
        })[0];
    }

    for (let charge of REFUNDED_CHARGES) {

        let row = findRow(rows, charge);

        if (row) {

            ++addedAdjustments;

            let adjustments = Number(row[ADJUSTMENTS_COL]) || 0;
                adjustments = approx(adjustments + Number(charge.adjustment));

            row[ADJUSTMENTS_COL] = adjustments;

            addedAdjustmentsAmount = approx(Number(addedAdjustmentsAmount) + Number(charge.adjustment));

            let income = Number(row[INCOME_COL]) + Number(charge.adjustment);

            row[INCOME_COL] = approx(income);
        } else {
            notMappedCharges.push(charge.id);

            let stripeAccID = charge[STRIPE_ACC_ID_COL];
            let value       = stripeAccAdjsNotInEvent.get(stripeAccID) || 0;
                value       = approx(value + Number(charge.adjustment));

            stripeAccAdjsNotInEvent.set(stripeAccID, value);
        }
    }

    let adjToMapToStripeAcc = [];
    for (let [accID, value] of stripeAccAdjsNotInEvent.entries()) {
        adjToMapToStripeAcc.push({
            [EVENT_ID_COL]      : '', /* Not null row won't be highlighted */
            [EVENT_NAME_COL]    : 'Adjustments',
            [STRIPE_ACC_ID_COL] : accID,
            [ADJUSTMENTS_COL]   : value,
            [INCOME_COL]        : value,
            is_acc_adjustments  : true
        });
    }

    return [
        rows, addedAdjustments, addedAdjustmentsAmount, notMappedCharges, adjToMapToStripeAcc];
}

module.exports.addStripeAccAdjustmentsRows = function (rows, adjToMapToStripeAcc) {
    for (let adjRow of adjToMapToStripeAcc) {
        let index = _.findLastIndex(rows, {
          [STRIPE_ACC_ID_COL]: adjRow[STRIPE_ACC_ID_COL]
        });

        if (index >= 0) {
            rows.splice(index + 1, 0, adjRow);
        } else {
            rows.push(adjRow);
        }
    }
}

/**
 * Assuming "rows" ordered by "stripe_account_id"
 */
module.exports.getStripeAccSubtotals = function (columns, rows, columnsInfo) {
    let accSubtotalsRowsMap = new Map();
    let accSubtotalsRow     = this.__initTotalsObj__(columns, columnsInfo);
    let colsForSummation    = columns.filter(col => columnsInfo[col].needs_sum);
    let stripeAccRowsQty    = 0;


    for (let i = 0; i < rows.length; ++i) {
        let row         = rows[i];
        let nextIndex   = (i + 1);

        for (let column of colsForSummation) {
            let value = Number(row[column]) || 0;

            accSubtotalsRow[column] = utils.normalizeNumber(accSubtotalsRow[column] + value);
        }
        accSubtotalsRow[EVENT_NAME_COL] = row[STRIPE_ACC_NAME_COL];

        if (!row.is_acc_adjustments) {
            ++stripeAccRowsQty;
        }

        /**
         * Count a Stripe Acc. subtotals until we meet another Stripe Account or we reach the end
         * of the "rows" array
         */
        if ((nextIndex === rows.length || 
            rows[nextIndex].stripe_account_id !== row.stripe_account_id) && 
            (stripeAccRowsQty > 0) /* There is only one row for that acc. that contains adj-ts */
        ) {
            /**
             * Map element description: 
             *     key -> occurance of another Stripe Acc., i.e. index of a row with 
             *         "stripe_account_id" that differs from the current one. This is the place 
             *         to add a subtotals row
             *     value -> Stripe Acc. subtotals row
             */
            accSubtotalsRowsMap.set(nextIndex, accSubtotalsRow);
            accSubtotalsRow = this.__initTotalsObj__(columns, columnsInfo);
            stripeAccRowsQty = 0;
        }
    }

    return this.injectExtraRows(accSubtotalsRowsMap, rows);
}

/**
 * Inject subtotals rows to the "rows" array
 */
module.exports.injectExtraRows = function (rowsToInjectMap, rows) {
    let insertedRowsQty     = 0;
    let subtotalRowsIndexes = [];

    for (let initSubTRowIndex of rowsToInjectMap.keys()) {
        /**
         * Defines a place in the "rows" array where a subtotals row should be added. 
         * Is the sum of 2 values: 
         *     "insertedRowsQty" - as we modify the "rows" array on every iteration, this
         *         value represents an offset of the initial (see below) index in relation to the 
         *         current state of the "rows" array
         *     "initSubTRowIndex" - index in the initial "rows" array where a subtotals row should
         *         be added
         * @type {Number}
         */
        let indexToAddTo    = (insertedRowsQty + initSubTRowIndex);
        let rowToAdd        = rowsToInjectMap.get(initSubTRowIndex);

        if (indexToAddTo >= rows.length) {
            rows.push(rowToAdd);
        } else {
            rows.splice(indexToAddTo, 0, rowToAdd);
        }

        subtotalRowsIndexes.push(indexToAddTo);

        ++insertedRowsQty;
    }

    return [rows, subtotalRowsIndexes];
}

module.exports.getRefundsAdjustments = function () {
    let refunds = REFUNDED_CHARGES.reduce((sum, ch) => (sum + ch.adjustment), 0);

    refunds = utils.normalizeNumber(refunds);

    return {
        totals: refunds,
        list: REFUNDED_CHARGES
    }
}

module.exports.findLostDisputesPenalty = function (after, before) {
    return Db.query(
        `SELECT 
            COALESCE(JSON_OBJECT_AGG("payment_for", "penalty_amount"), '{}'::JSON) "penalty"
         FROM (
             SELECT 
                p."payment_for", (COALESCE(COUNT(*), 0) * $3 * -1) "penalty_amount"
             FROM "purchase" p 
             WHERE p."dispute_created" BETWEEN $1 AND $2
                 AND p."dispute_status" = 'lost'
             GROUP BY p."payment_for"
         ) "d"`,
        [after, before, StripeService.DISPUTE_FEE]
    ).then(res => {
        let { rows: [ { penalty } ] } = res;

        let totalPenalty = Object.keys(penalty).reduce((sum, key) => (sum + penalty[key]), 0);

        totalPenalty = utils.normalizeNumber(totalPenalty);

        return totalPenalty;
    })
}

module.exports.__initTotalsObj__ = function (columns, columnsInfo) {
    let totals = {};

    for (let c of columns) {
        if (columnsInfo[c].needs_sum) {
            totals[c] = 0;
        } else {
            totals[c] = null;
        }
    }

    return totals;
}

module.exports.prepareTotals = function (totals, columns, columnsInfo, numFormat) {
    return utils.removePropNames(columns, [totals], function formatTotals (column, value) {
        if (columnsInfo[column].needs_sum) {
            return numeral(value).format(numFormat);
        } else {
            return value;
        }
    })[0];
}

module.exports.cutPropsAndCountTotals = function (columns, rows, columnsInfo, numFormat, rowsToSkip) {
    let totals = this.__initTotalsObj__(columns, columnsInfo);

    rows = utils.removePropNames(columns, rows, function countTotals (column, value, rowIndex) {
        if (columnsInfo[column].needs_sum) {

            value = Number(value) || 0;

            if (!rowsToSkip.includes(rowIndex)) {
                totals[column] = utils.normalizeNumber(totals[column] + value);
            }

            return numeral(value).format(numFormat);
        } else {
            return value;
        }
    });

    return [rows, totals];
}
