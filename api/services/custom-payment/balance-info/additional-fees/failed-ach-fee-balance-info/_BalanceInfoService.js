
const { FAILED_ACH_PAYMENT_FEE, CUSTOM_PAYMENT} = require('../../../../../constants/payments');

class _BalanceInfoService {
    constructor(swUtils) {
        this.approx = swUtils.normalizeNumber.bind(swUtils);
    }

    PAYMENT_FOR = null;

    async getBalance(eventID) {
        const lostACHFeesToPay = await this.#getLostACHFeesToPay(eventID);

        const chargeIDs = lostACHFeesToPay.reduce((arr, paymentData) => {
            arr.push(paymentData.stripeChargeID);
            return arr;
        }, []);

        return {
            totals: this.#countTotals(lostACHFeesToPay),
            chargeIDs,
        }
    }

    async #getLostACHFeesToPay(eventID) {
        const [
            allFailedACHFeesToPay,
            alreadyPaidFailedACHFees
        ] = await Promise.all([
            this.#getAllFailedACHFeesToPay(eventID),
            this.#getPaidFailedACHFees(eventID)
        ]);

        return allFailedACHFeesToPay.filter(
            (payment) => !alreadyPaidFailedACHFees.includes(payment.stripeChargeID)
        );
    }

    async getCustomPaymentBalance(customPaymentID) {
        const query = knex('custom_payment as cp')
            .select({ stripeChargeIDs: knex.raw('JSON_AGG(p.stripe_charge_id)') })
            .join(
                'custom_payment_dispute_fee_failed_ach_fee as paid_dp',
                'paid_dp.custom_payment_id',
                'cp.custom_payment_id'
            )
            .join('purchase as p', 'p.stripe_charge_id', 'paid_dp.stripe_charge_id')
            .where('paid_dp.type', 'failed_ach_fee')
            .where('cp.custom_payment_id', customPaymentID);

        const { rows: [balance] } = await Db.query(query);

        if(!_.isEmpty(balance) && !_.isEmpty(balance.stripeChargeIDs)) {
            return {
                totals: this.#countTotals(balance.stripeChargeIDs),
                chargeIDs: balance.stripeChargeIDs
            }
        }
    }

    #countTotals(paymentsList) {
        const failedPaymentsCount = paymentsList.length;

        return {
            totalAmount: this.approx(failedPaymentsCount * FAILED_ACH_PAYMENT_FEE),
            count: failedPaymentsCount,
        }
    }

    async #getAllFailedACHFeesToPay(eventID) {
        const query = knex('purchase AS p')
            .select({
                stripeChargeID: 'p.stripe_charge_id'
            })
            .whereRaw(`p.status = 'canceled'`)
            .whereRaw(`p.type = 'ach'`)
            .where('p.event_id', eventID)
            .whereNotNull('p.canceled_date')
            .whereNull('p.date_refunded')
            .whereRaw('p.canceled_date > ?', [CUSTOM_PAYMENT.UNCOLLECTED_FEE_AND_ADDITIONAL_FEES_SEPARATION_DATE])
            .where((builder) => {
                builder.where('p.amount_refunded', 0)
                    .orWhereNull('p.amount_refunded')
            })
            .whereNull('p.dispute_created')
            .where('p.payment_for', this.PAYMENT_FOR);

        const { rows: failedACHFeesToPay } = await Db.query(query);

        return failedACHFeesToPay;
    }

    async #getPaidFailedACHFees(eventID) {
        const query = knex('custom_payment as cp')
            .select({
                paidChargeIDs: knex.raw(`COALESCE(JSONB_AGG(DISTINCT paid_dp.stripe_charge_id), '[]'::JSONB)`)
            })
            .join('custom_payment_dispute_fee_failed_ach_fee AS paid_dp', (join) => {
                join.on('paid_dp.custom_payment_id', 'cp.custom_payment_id')
                    .andOn(knex.raw(`paid_dp.type = ?`, 'failed_ach_fee'))
            })
            .where('cp.event_id', eventID)
            .whereIn('cp.status', ['pending', 'paid', 'requires_action'])
            .where('cp.payment_for', CUSTOM_PAYMENT.PAYMENT_FOR.LOST_DISPUTE_FEE_FAILED_ACH_FEE)
            .where('cp.payment_for_type', this.PAYMENT_FOR);

        const { rows: [result] } = await Db.query(query);

        return result?.paidChargeIDs;
    }

}

module.exports = _BalanceInfoService;
