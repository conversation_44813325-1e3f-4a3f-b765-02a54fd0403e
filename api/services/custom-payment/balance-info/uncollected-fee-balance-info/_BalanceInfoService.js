
const swUtils = require('../../../../lib/swUtils');
const { CUSTOM_PAYMENT } = require("../../../../constants/payments");

class _BalanceInfoService {
    constructor() {
        this.approx = swUtils.normalizeNumber.bind(swUtils);
    }

    async getCurrentBalance (eventID, collectedSWFee, expectedSWFee) {
        let { total: paidEscrow } = await this.__getPaidSWFee(eventID);

        let escrowStillOwned = this.__getEscrowCollected(collectedSWFee, expectedSWFee, paidEscrow);

        return escrowStillOwned;
    }

    __getPaidSWFee (eventID) {
        return FundsTransferService.getPaidSWFee(eventID, this.PAYMENT_FOR_TYPE, CUSTOM_PAYMENT.PAYMENT_FOR.UNCOLLECTED_FEE);
    }

    __getEscrowCollected (collectedSWFee, expectedSWFee, paidEscrow) {
        return this.approx(collectedSWFee - expectedSWFee + paidEscrow);
    }
}

module.exports = _BalanceInfoService;
