
const AdditionalFeesService = require('../balance-info/_AdditionalFeesBalanceInfoService');
const UncollectedSWFeesService = require('../balance-info/_UncollectedFeeBalanceInfoService');

const {
    CUSTOM_PAYMENT: { BALANCE_TYPE, PAYMENT_FOR}
} = require('../../../constants/payments');

class BalanceInfoService {
    constructor(
        AdditionalFeesService,
        UncollectedSWFeesService
    ) {
        this[BALANCE_TYPE.ADDITIONAL_FEES] = AdditionalFeesService;
        this[BALANCE_TYPE.UNCOLLECTED_SW_FEES] = UncollectedSWFeesService;
    }

    getBalanceTypeForCustomPaymentType(paymentType) {
        switch (paymentType) {
            case PAYMENT_FOR.UNCOLLECTED_FEE:
                return BALANCE_TYPE.UNCOLLECTED_SW_FEES;
            case PAYMENT_FOR.LOST_DISPUTE_FEE_FAILED_ACH_FEE:
                return BALANCE_TYPE.ADDITIONAL_FEES;
            default: throw new Error('Unknown payment type');
        }
    }
}

module.exports = new BalanceInfoService(AdditionalFeesService, UncollectedSWFeesService);
