'use strict';

const co = require('co');

module.exports = {
    EVENT_USERS_SEARCH_LIMIT: 5,
    EVENT_USER_PERMISSION_CONSTRAINT: 'event_user_permission_pkey',

    getAllUsers : function (eventID) {
        let sql =
            `SELECT eup."event_id",
                   u."first",
                   u."last",
                   u."email",
                   JSON_OBJECT_AGG(eup."event_operation_id", true) "permissions"
            FROM event_user_permission "eup"
                   JOIN event "e" ON eup."event_id" = e."event_id"
                   JOIN "user" "u" ON eup."user_id" = u."user_id"
            WHERE eup."event_id" = $1
                   AND eup."deleted" IS NULL
            GROUP BY eup."event_id", u."first", u."last", u."email"
            HAVING NULLIF(JSON_OBJECT_AGG(eup."event_operation_id", true)::TEXT, '{}'::TEXT) IS NOT NULL`;

        return Db.query(sql, [eventID]).then(result => result.rows);
    },

    updateUserPermissions: async function (granterUserID, eventID, email, permissions) {
        let tr = await Db.begin();

        try {
            let { success, userID } = await this.addUserByEmail(granterUserID, eventID, email, permissions, tr);

            if(!success) {
                throw new Error('Update failed');
            }

            let deleteNotInListPermissionsQuery = squel.update().table('event_user_permission', 'eup')
                .set('deleted', 'NOW()', { dontQuote: true })
                .where(`eup.event_operation_id NOT IN ('${Object.keys(permissions).join('\', \'')}')`)
                .where('eup.event_id = ?', eventID)
                .where('eup.user_id = ?', userID);

            await tr.query(deleteNotInListPermissionsQuery);

            await tr.commit();

            return userID;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            throw err;
        }
    },

    getEODataByEventID: function (eventID) {
        let sql = squel.select().from('event', 'e')
            .field('u.first')
            .field('u.last')
            .field('u.email')
            .join('event_owner', 'eo', 'e.event_owner_id = eo.event_owner_id')
            .join('user', 'u', 'u.user_id = eo.user_id')
            .where('e.event_id = ?', eventID);

        return Db.query(sql).then(result => result.rows[0] || null);
    },

    markAsDeleted: function (eventID, email) {
        let sql =
            `WITH user_data AS (SELECT u."user_id" FROM "user" u WHERE u."email" = $1)
            UPDATE event_user_permission eup
            SET deleted = NOW()
            WHERE eup.user_id = (SELECT user_data.user_id FROM user_data)
              AND eup.event_id = $2
            RETURNING (SELECT user_data.user_id FROM user_data) "user_id"`;

        return Db.query(sql, [email, eventID]).then(result => result.rows[0] && result.rows[0].user_id);

    },

    findEligibleUsers : function(eventID, search) {
        let sql =`
            SELECT DISTINCT u."first",
                u."last",
                u."email",
                COUNT(*) OVER () :: INT "total_rows"
            FROM "user" u
            WHERE u."user_id" NOT IN (SELECT eup."user_id"
                                    FROM event_user_permission "eup"
                                    WHERE eup."event_id" = $1
                                      AND eup."deleted" IS NULL
                                    UNION
                                    SELECT eo."user_id"
                                    FROM event_owner "eo"
                                      JOIN event "e" ON e."event_id" = $1
                                    WHERE eo."event_owner_id" = e."event_owner_id")
              AND (u."first" ILIKE '%'  || $2 || '%' OR
                   u."last" ILIKE '%'   || $2 || '%' OR
                   u."email" ILIKE '%'  || $2 || '%')
            ORDER BY u."first", u."last"
            LIMIT $3 :: INT`;

        return Db.query(sql, [eventID, search, this.EVENT_USERS_SEARCH_LIMIT]).then(result => result.rows);
    },

    addUserByEmail: async function(granterUserID, eventID, email, permissions, tr = null) {
        let userID = await Db.query(`SELECT DISTINCT user_id FROM "user" WHERE email = $1`, [email])
            .then(result => result.rows[0] && result.rows[0].user_id || null);

        let { query, values } = this.__getUserAdditionQuery(eventID, userID, granterUserID, permissions);

        return (tr || Db).query(query, values).then(result => {
            let success = result.rowCount === Object.keys(permissions).length;

            return { success, userID };
        });
    },

    __getUserAdditionQuery: function (eventID, userID, granterUserID, permissions) {
        let fieldsRows = Object.keys(permissions).map(permission => {
            return {
                event_operation_id   : permission,
                event_id             : eventID,
                user_id              : userID,
                granter_user_id      : granterUserID
            }
        });

        let sql = squel.insert().into('event_user_permission').setFieldsRows(fieldsRows);

        let {text:query, values} = sql.toParam();

        query += `ON CONFLICT ON CONSTRAINT ${this.EVENT_USER_PERMISSION_CONSTRAINT} DO UPDATE SET deleted = NULL`;

        return { query, values };
    },

    setUserAsCoOwner: async function(tr, eventOwnerID, eventID, userID) {
        let options = await this.getUserPermissions();

        let permissions = options.reduce((all, option) => {
            all[option.event_operation] = true;

            return all;
        }, {});

        let { query, values } = this.__getUserAdditionQuery(eventID, userID, userID, permissions);

        return tr.query(query, values);
    },

    getUserPermissions: function () {
        return Db.query(
            `SELECT eo."event_operation", eo."title"
                FROM event_operation "eo"`
        ).then(result => result.rows);
    },

    getUserPermissionsTree: function () {
        return Db.query(
            `WITH RECURSIVE event_operation_structure AS (
                -- Anchor member: Select the root nodes (elements with no parent)
                SELECT event_operation, title, parent_event_operation, 1 AS depth
                FROM event_operation
                WHERE parent_event_operation IS NULL

                UNION ALL

                -- Recursive member: Select children of the current node, increment depth
                SELECT mt.event_operation, mt.title, mt.parent_event_operation, eos.depth + 1
                FROM event_operation mt
                         JOIN event_operation_structure eos ON mt.parent_event_operation = eos.event_operation
            )
             SELECT *
             FROM event_operation_structure
             ORDER BY depth`
        ).then(result => {
            const permissionsMap = result.rows.reduce((all, item) => {
                if(!item.parent_event_operation) {
                    all[item.event_operation] = {
                        title: item.title,
                        event_operation: item.event_operation,
                        children: [],
                    };
                } else {
                    all[item.parent_event_operation].children.push({
                        title: item.title,
                        event_operation: item.event_operation,
                    });
                }

                return all;
            }, {});

            return Object.values(permissionsMap);
        });
    },
    getEventUserPermissions: function (eventID, userID) {
        return Db.query(
            `SELECT JSON_OBJECT_AGG(eup.event_operation_id, true) "permissions"
                FROM event_user_permission eup
                WHERE eup.event_id = $1
                  AND eup.user_id = $2
                  AND eup.deleted IS NULL`,
            [eventID, userID]
        ).then(result => result.rows[0] && result.rows[0].permissions || []);
    },
};



