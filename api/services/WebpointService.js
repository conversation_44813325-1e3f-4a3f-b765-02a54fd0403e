'use strict';

const MemberValidationService = require('./webpoint/_WebpointMemberValidation');

class WebpointService {
    constructor () {}

    get VALID_SAFESPORT_STATUS () {
        return 2;
    }

    get VALID_BACKGROUND_SCREENING_STATUS () {
        return 2;
    }

    get WEBPOINT_RESPONSE_KEY () {
        return {
            FIRST_NAME          : 'FIRST_NAME',
            LAST_NAME           : 'LAST_NAME',
            GENDER              : 'GENDER',
            BIRTH_DATE          : 'BIRTH_DATE',
            CLUB_CODE           : 'CLUB_CODE',
            USAVNUMBER          : 'USAVNUMBER',
            BGSTATUSID          : 'BGSTATUSID',
            MEMBER_STATUS       : 'MEMBER_STATUS',
            BGEXPDATE           : 'BGEXPDATE',
            MBR_EXP_DATE        : 'MBR_EXP_DATE',
            SAFESPORT_END_DATE  : 'SAFESPORT_END_DATE',
            SAFESPORT_STATUSID  : 'SAFESPORT_STATUSID'
        };
    }

    get member_validation () {
        return MemberValidationService;
    }
}

module.exports = new WebpointService();
