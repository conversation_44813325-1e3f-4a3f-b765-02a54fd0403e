'use strit';

class ClubWebpointService {

    constructor (SafeSportService) {
        this._SafeSportService = SafeSportService;
    }

    async syncMember (masterClubID, swMemberID, memberType) {
        let member = await (TeamMembersService.findMember(masterClubID, swMemberID, memberType));

        if (member == null) {
            throw { validation: 'Member not found in club!' };
        }

        let updatedMemberData = null;
        try {
            const SafeSportService  = new this._SafeSportService(Db);

            let result              = await (SafeSportService.processMember(member));
            updatedMemberData       = result.row;
        } catch (rejection) {

            loggers.debug_log.info(rejection);

            throw { validation: (rejection.message || rejection.errorMessage || rejection.validation) };
        }

        let safeSportID     = Number(updatedMemberData.safesport_statusid);
        let backgroundID    = Number(updatedMemberData.bg_screening);
        let lastSync        = updatedMemberData.last_webpoint_sync;

        return {
            safesport_status    : (safeSportID === WebpointService.VALID_SAFESPORT_STATUS),
            bg_status           : (backgroundID === WebpointService.VALID_BACKGROUND_SCREENING_STATUS)
                ? 'OK'
                : 'NO' ,
            cert                : updatedMemberData.cert,
            last_webpoint_sync  : lastSync
        }
    }
}

module.exports = ClubWebpointService;
