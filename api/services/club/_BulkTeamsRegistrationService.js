
const { registration: registrationValidationSchema } = require('../../validation-schemas/club-bulk-registration');

class BulkTeamsRegistrationService {

    async registration (masterClubID, clubOwnerID, selection) {
        const { error } = registrationValidationSchema.validate(selection);

        if (error) {
            throw { validation: error.details[0].message };
        }

        let addedList = {};

        try {
            for(const event of selection) {
                const eventID = event.event_id;
                const teams = event.teams;

                addedList[eventID] = {
                    expected: teams.length,
                    added: 0,
                    notAdded: 0,
                    teamsAdded: {},
                    notAddedTeams: {}
                }

                const sanctionValidationResult = await CheckInRosterService.validateSanction(masterClubID, eventID);

                if (!sanctionValidationResult) {
                    throw {
                        validation: 'Your club does not have the correct sanctioning to enter this event: ' + eventID
                    };
                }

                for(const team of teams) {
                    const { master_team_id, division_id } = team;

                    try {
                        await this.__processTeamEntering(
                            masterClubID, clubOwnerID, eventID, master_team_id, division_id
                        );

                        addedList[eventID].added++;
                        addedList[eventID].teamsAdded[master_team_id] = team;
                    } catch (err) {
                        console.error(err);
                        addedList[eventID].notAddedTeams[master_team_id] = { ...team, error: err };
                        addedList[eventID].notAdded++;
                    }
                }
            }

            return addedList;
        } catch (err) {
            throw err;
        }
    }

    async list (masterClubID, filters) {
        if(!masterClubID) {
            throw { validation: 'Master Club ID required' };
        }

        let list = await this.__getRegistrationListData(masterClubID, filters);

        if(!!filters.event_visibility) {
            list = list.filter(i => !_.isEmpty(i.teams));
        }

        return list;
    }

    __getRegistrationListData (masterClubID, filters) {
        const query = this.__generateListSQL(masterClubID, filters);

        return Db.query(query).then(result => result?.rows);
    }

    __generateListSQL (masterClubID, filters = {}) {
        let teamAgeComparison = '>=';

        if (filters.team_age_comparison === 'equal') {
            teamAgeComparison = '=';
        }

        let teamsQuery = knex('master_club AS mc')
            .select({
                team_name: 'mt.team_name',
                master_team_id: 'mt.master_team_id',
                organization_code: 'mt.organization_code',
                pre_selected_division_id: knex.raw(`
                    (ARRAY_AGG(d_t.division_id ORDER BY d_t.max_age) 
                        FILTER (WHERE d_t.max_age ${teamAgeComparison} mt.age))[1]
                `),
                divisions: knex.raw(`ARRAY_AGG(DISTINCT jsonb_build_object(
                    'id', d_t.division_id, 
                    'name', d_t.name,
                    'fee', COALESCE(d_t.reg_fee, e.reg_fee, 0)
                ))`)
            })
            .leftJoin('roster_club AS rc', (table) => {
                table.on('mc.master_club_id', 'rc.master_club_id')
                    .andOn('e.event_id', 'rc.event_id')
            })
            .join('master_club_sanctioning AS mcs', (table) => {
                table.on('mc.master_club_id', 'mcs.master_club_id')
                    .andOn(knex.raw(`e.sport_sanctioning_id = mcs.sport_sanctioning_id`))
            })
            .join('master_team AS mt', (table) => {
                table.on('mt.master_club_id', 'mc.master_club_id')
                    .andOnNull('mt.deleted')
                    .andOn('mt.season', 'e.season')
                    .andOnNotExists((builder) => {
                        builder.select('*').from('roster_team AS rt')
                            .where(knex.raw('rt.event_id = e.event_id'))
                            .whereNull('rt.deleted')
                            .where(knex.raw('rt.roster_club_id = rc.roster_club_id'))
                            .where(knex.raw('mt.master_team_id = rt.master_team_id'))
                    })
            })
            .join('division AS d_t', (table) => {
                table.on('d_t.event_id', 'e.event_id')
                    .andOnNull('d_t.closed')
                    .andOn(knex.raw('d_t.locked IS NOT TRUE'))
                    .andOn(knex.raw('d_t.published IS TRUE'))
                    .andOn(knex.raw(`COALESCE(d_t.date_reg_close, e.date_reg_close) > now() AT TIME ZONE e.timezone`))
                    .andOn('d_t.max_age', teamAgeComparison, 'mt.age')
                    .andOn('d_t.gender', 'mt.gender')
            })
            .whereRaw('e.sport_id = mc.sport_id')
            .where('mc.master_club_id', masterClubID)
            .whereRaw('(e.entry_region_restriction IS NULL OR mc.region = ANY (e.entry_region_restriction))')
            .groupBy('mt.master_team_id')
            .orderBy('mt.age', 'asc');

        let mainQuery = knex('event AS e')
            .select({
                division_selection_disabled: knex.raw(
                    `COALESCE((e.teams_settings ->> 'division_selection_disabled')::BOOLEAN, FALSE) IS TRUE`
                ),
                event_date_end: knex.raw(`to_char(e.date_end, 'MM/DD/YY')`),
                event_name: 'e.long_name',
                event_short_name: 'e.name',
                event_id: 'e.event_id',
                ages: knex.raw(
                    `ARRAY_TO_JSON(ARRAY_AGG(DISTINCT d.max_age ORDER BY d.max_age) 
                        FILTER ( WHERE d.division_id IS NOT NULL ))`
                ),
                event_date_start: knex.raw(`to_char(e.date_start, 'MM/DD/YY')`),
            })
            .join('division AS d', (table) => {
                table.on('e.event_id', 'd.event_id')
                    .andOnNull('d.closed')
                    .andOn('d.max_age', '>', 0)
                    .andOn(knex.raw('d.locked IS NOT TRUE'))
                    .andOn(knex.raw('d.published IS TRUE'))
            })
            .where('e.published', true)
            .where('e.season', sails.config.sw_season.current)
            .where('e.teams_use_clubs_module', true)
            .whereNull('e.deleted')
            .whereRaw('e.date_start > now() AT TIME ZONE e.timezone')
            .whereRaw('e.date_reg_open <= now() AT TIME ZONE e.timezone')
            .whereRaw('e.club_private_reg_active IS NOT TRUE')
            .where('e.live_to_public', true)
            .groupBy('e.event_id')
            .orderBy('e.date_start', 'desc');

        this.__addFilters(mainQuery, teamsQuery, filters);

        mainQuery.select({
            teams: knex.raw(`(SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(p))) FROM (
                    ${teamsQuery.toString()}
                ) AS p)`)
        });

        return mainQuery;
    }

    __addFilters (mainQuery, teamsQuery, filters = {}) {
        this.__addEventFilters(mainQuery, filters);
        this.__addTeamsFilters(teamsQuery, filters);
    }

    __addEventFilters (query, filters) {
        const {
            event_type,
            event_state,
            event_region,
            event_date_start,
            event_date_end,
            event_division_ages,
            event_gender,
            event_name_search
        } = filters;

        if(event_type) {
            query.where('e.event_type', event_type);
        }

        if(event_state) {
            query.where('e.state', event_state);
        }

        if(event_region) {
            query.where('e.region', event_region);
        }

        if(event_date_start) {
            query.where(knex.raw(`e.date_start >= ? AT TIME ZONE e.timezone`, event_date_start));
        }

        if(event_date_end) {
            query.where(knex.raw(`e.date_end <= ? AT TIME ZONE e.timezone`, event_date_end));
        }

        if(Array.isArray(event_division_ages) && event_division_ages.length) {
            query.whereIn('d.max_age', event_division_ages);
        }

        if(event_gender) {
            let genderField;

            switch (event_gender) {
                case 'male':
                    genderField = 'e.has_male_teams';
                    break;
                case 'female':
                    genderField = 'e.has_female_teams';
                    break;
                case 'coed':
                    genderField = 'e.has_coed_teams';
                    break;
            }

            query.where(knex.raw(`${genderField} IS TRUE`));
        }

        if(event_name_search) {
            query.whereRaw('e.long_name ILIKE ?', `%${event_name_search}%`);
        }
    }

    __addTeamsFilters (query, filters) {
        const {
            team_age,
            team_rank,
            event_division_ages,
            team_name_search
        } = filters;

        if(team_age) {
            query.where('mt.age', Number(team_age));
        }

        if(team_rank) {
            query.where('mt.rank', team_rank);
        }

        if(Array.isArray(event_division_ages) && event_division_ages.length) {
            query.whereIn('d_t.max_age', event_division_ages);
        }

        if(team_name_search) {
            query.whereRaw('mt.team_name ILIKE ?', `%${team_name_search}%`);
        }
    }

    async __processTeamEntering (masterClubID, clubOwnerID, eventID, masterTeamID, divisionID) {
        if (divisionID > 0) {

            const isValidAge = await CheckInRosterService.validateAge({
                masterTeamId: masterTeamID,
                rosterTeamId: null,
                divisionId: divisionID
            });
            if(!isValidAge) {
                throw { validation: 'Division age must be not less than team age' };
            }
        }

        const idValidationResult =
            await CheckInRosterService.validateMasterTeamRosterWithMasterTeamId(eventID, masterTeamID);

        if (idValidationResult.length) {
            throw { validation: _.first(idValidationResult) };
        }

        let team, club;

        try {
            ({rosterTeam: team, rosterClub: club} = await manageTeamsEntranceService.manage2({
                event_id        : eventID,
                master_team_id  : masterTeamID,
                division_id     : divisionID,
                club_owner_id   : clubOwnerID,
                master_club_id  : masterClubID,
                reg_method      : CheckInRosterService.ROSTER_TEAM_REG_METHOD.REGULAR
            }))
        } catch (err) {
            switch(err.type) {
                case 'Argument':
                case 'Entry':
                case 'Entrance':
                    throw { validation: err.validation };
                default: {
                    if (_.isObject(err)) {
                        throw err;
                    }

                    throw new Error(err);
                }
            }
        }

        const { roster_club_id, distance_to_event } = club;

        if (!distance_to_event) {
            await ClubService.geo.calculateDistanceToEvent(Db, roster_club_id, masterClubID, eventID)
                .catch(err => console.log(err));
        }

        return team;
    }
}

module.exports = BulkTeamsRegistrationService;
