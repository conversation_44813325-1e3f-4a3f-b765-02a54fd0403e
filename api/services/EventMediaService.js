const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const { Upload } = require('@aws-sdk/lib-storage');
const sharp     = require('sharp');
const optimist  = require('optimist');
const { PassThrough } = require('stream');
const utils = require('../lib/swUtils');

const S3_FOLDER = 'images/';

const MAX_FILE_SIZE = 5120;

const VALIDATION_RULES = {
    'main-logo': {
        size: MAX_FILE_SIZE,
    },
    'cover-image': {
        size: MAX_FILE_SIZE,
    },
    sharedRules: {
        fileTypes: ['image/jpeg', 'image/png'],
    },
};

class EventMediaService {
    constructor() {
        const s3Config = optimist.argv.prod
            ? sails.config.s3.ticketsBucket
            : sails.config.s3.ticketsBucketDev;

        this.s3Client = new S3Client({
            region: s3Config.region,
            credentials: {
                accessKeyId: s3Config.key,
                secretAccessKey: s3Config.secret
            }
        });

        this.s3Bucket = s3Config.bucket;

        this.imagesTypes = {
            SMALL_LOGO       : 'small-logo',
            MAIN_LOGO        : 'main-logo',
            COVER_IMAGE      : 'cover-image',
        };

        this.dimensions = {
            [this.imagesTypes.SMALL_LOGO]: {
                width   : 90,
                height  : 90,
            },
            [this.imagesTypes.MAIN_LOGO]: {
                width   : 355,
                height  : 145,
            },
            [this.imagesTypes.COVER_IMAGE]: {
                width   : 1400,
                height  : 219,
            },
        };
    }

    async saveEventImage(image, imageType, eventId, tr = null) {
        if (
            ![
                this.imagesTypes.MAIN_LOGO,
                this.imagesTypes.COVER_IMAGE,
            ].includes(imageType)
        )
            throw { validation: "Not valid image type" };

        const filename = this.formatFileName(image.filename);
        const validationRules = VALIDATION_RULES[imageType];
        const fileType = image.headers["content-type"];
        const { fileNameWithoutExt, ext } = this.getFileData(filename);
        const s3Path = `${S3_FOLDER}${eventId}/${filename}`;

        if (
            utils.getNextBinaryPrefixValue(image.byteCount) >
            validationRules.size
        ) {
            throw {
                validation: `Maximum size for this image type is ${utils.getNextBinaryPrefixValue(
                    validationRules.size
                )}MB`,
            };
        }

        if (!VALIDATION_RULES.sharedRules.fileTypes.includes(fileType)) {
            throw { validation: "Allowable PNG or JPEG types" };
        }

        const imagesData = this.generateImageData(eventId, filename);

        const result = await this.uploadImages(
            image,
            imageType,
            imagesData,
            s3Path,
            fileNameWithoutExt
        );

        const imagesDataForDB = _.filter(result, (type) => type);
        const _saveImage = this.saveImage.bind(this, ext, eventId);
        const images = this.setDBPathToImageData(eventId, imagesDataForDB);

        const res = await Promise.all(
            images.map((image) => _saveImage(image, tr))
        );

        const {
            event_media_id: id,
            file_path,
            file_ext,
        } = _.find(res, (img) => img.file_type === imageType);

        return {
            id,
            path: file_path + "." + file_ext,
        };
    }

    async saveImage(fileExt, eventId, { pathToDB, imageType, fileName }, tr=null) {
        const result = await (tr || Db).query(this.SAVE_IMAGE_SQL, [pathToDB, eventId, imageType, fileExt, fileName]).then(({rows})=>rows[0]||null)
        Cache.invalidateTags([
            Cache.tag.dbTable('event_media'),
            Cache.tag.dbTable('event_media', {event_id: eventId}),
        ]).catch(err => Cache.tagInvalidator.errorHandler('event_media', err));
        return result;
    }

    async saveFlowChart(data) {
        const {
            fileType,
            eventId,
            filePath,
            fileName,
            fileExt,
            divisionId,
        } = data;

        const result = await Db.query(this.SAVE_FLOWCHART_SQL, [filePath, eventId, fileType, fileExt, fileName, divisionId]);
        Cache.invalidateTags([
            Cache.tag.dbTable('event_media'),
            Cache.tag.dbTable('event_media', {event_id: eventId}),
        ]).catch(err => Cache.tagInvalidator.errorHandler('event_media', err));
        return result
    }

    getImages(eventId) {
        return Db.query(this.GET_IMAGES_SQL, [eventId])
            .then(response => this.formatImages(response.rows));
    }

    async removeImage(imageId) {
        const result = await Db.query(this.REMOVE_IMAGE_SQL, [imageId]);
        if(result.rows.length === 1) {
            Cache.invalidateTags([
                Cache.tag.dbTable('event_media', {event_id: result.rows[0].event_id}),
            ]).catch(err => Cache.tagInvalidator.errorHandler('event_media', err));
        }
        return result;
    }

    async removeEventImages(eventId, imageIds, tr = null) {
        const result = await (tr || Db).query(this.REMOVE_EVENT_IMAGES_SQL, [eventId, imageIds]);
        if(result.rows.length === 1) {
            Cache.invalidateTags([
                Cache.tag.dbTable('event_media', {event_id: result.rows[0].event_id}),
            ]).catch(err => Cache.tagInvalidator.errorHandler('event_media', err));
        }
        return result;
    }

    async removeSmallLogo(eventId) {
        const result = await Db.query(this.REMOVE_SMALL_LOGO_SQL, [eventId]);
        Cache.invalidateTags([
            Cache.tag.dbTable('event_media', {event_id: eventId}),
        ]).catch(err => Cache.tagInvalidator.errorHandler('event_media', err));
        return result;
    }

    formatImages(images) {
        const grouped = _.groupBy(images, image => image.file_type);
        const _images = {};

        _.forIn(grouped, (value, key) => {
            _images[key] = _.omit(value[0], 'file_type');
        });

        return _images;
    }

    /**
     * Function returns file name without extension and its extension.
     *
     * @param fileName
     * @returns {{fileNameWithoutExt: string, ext: string}}
     */
    getFileData(fileName) {
        const re        = /(?:\.([^.]+))?$/;
        const result    = re.exec(fileName);

        return {
            fileNameWithoutExt: fileName.slice(0, result.index),
            ext: result[1]
        }
    }

    /**
     * Function replacing spaces with underscores
     * and add metadata to the file name.
     *
     * @param fileName
     * @param timestamp
     * @returns {string}
     */
    formatFileName(fileName, timestamp) {
        const now = timestamp || Date.now();
        const {
            ext,
            fileNameWithoutExt,
        } = this.getFileData(fileName);

        const fName = fileNameWithoutExt.replace(/([^A-Za-z0-9])+/g,"_");

        return `${fName}-${now}.${ext}`;
    }

    uploadOriginalImageToS3(stream, s3Path) {
        return new Promise((resolve, reject) => {
            const pass = new PassThrough();
            const upload = new Upload({
                client: this.s3Client,
                params: {
                    Bucket: this.s3Bucket,
                    Key: s3Path,
                    Body: pass,
                    ContentType: stream.headers['content-type'] || 'application/octet-stream',
                }
            });

            upload.done()
                .then(() => {
                    resolve()
                })
                .catch((err) => {
                    reject(err);
                });

            stream.on('error', reject);
            stream.pipe(pass);
        });
    }

    resizeImage(stream, { width, height }, imageType) {
        return new Promise((resolve, reject) => {
            const image     = sharp();

            image.metadata().then(data => {
                const { width: originalWidth, height: originalHeight } = data;

                if (imageType === this.imagesTypes.COVER_IMAGE) {
                    if (originalWidth <= width) {
                        return resolve(false);
                    }
                } else {
                    if (originalWidth <= width && originalHeight <= height) {
                        return resolve(false);
                    }
                }

                const fit = imageType === this.imagesTypes.COVER_IMAGE ? 'outside' : 'inside';
                return image.resize(width, height, {fit})
                    .jpeg( { quality: 100, force: false })
                    .png({ compressionLevel: 9, force: false })
                    .toBuffer()

            }).then(resolve)
                .catch(err => {
                    loggers.errors_log.error(err);
                    reject(err);
                });

            stream.pipe(image);
        })
    }

    uploadResizedFile(originalImageStream, s3Path, imageType,  buffer, fileName) {
        return new Promise((resolve, reject) => {
            const params = {
                Bucket: this.s3Bucket,
                Key: s3Path,
                Body: buffer,
                ContentType: originalImageStream.headers['content-type'],
            };

            const command = new PutObjectCommand(params);

            this.s3Client.send(command)
                .then(() => {
                    resolve({
                        imageType,
                        fileName,
                    });
                })
                .catch(reject);
        });
    }

    reqStreamErrorHandler(stream) {
        return new Promise((resolve, reject) => {
            stream.on('error', (err) => {
                reject(err);
            });

            stream.on('end', () => {
                resolve();
            })
        })
    }

    async resizeAndUpload(originalImageStream, dimensions, imageData, originalFileName) {
        const { pathToS3, imageType, fileName } = imageData;

        const buffer = await this.resizeImage(originalImageStream, dimensions, imageType);

        if(!buffer) {
            originalImageStream.end();
            return { imageType: imageType, fileName: originalFileName };
        }

        return this.uploadResizedFile(originalImageStream, pathToS3, imageType, buffer, fileName);
    }

    uploadImages(originalImageStream, imageType, imagesData, s3Path, originalFileName) {
        const imageData = imagesData[imageType];

        const promises = [
            this.reqStreamErrorHandler(originalImageStream),
            this.uploadOriginalImageToS3(originalImageStream, s3Path),
            this.resizeAndUpload(originalImageStream, this.dimensions[imageType], imageData, originalFileName),

        ];

        if (imageType === this.imagesTypes.MAIN_LOGO) {
            const imageData = imagesData[this.imagesTypes.SMALL_LOGO];

            promises.push(
                this.resizeAndUpload(
                    originalImageStream,
                    this.dimensions[this.imagesTypes.SMALL_LOGO],
                    imageData,
                    originalFileName
                )
            );
        }

        return Promise.all(promises);
    }

    getPath(eventId, fileName, pathToDb) {
        const prefix = pathToDb ? '/' : '';

        return `${prefix}${S3_FOLDER}${eventId}/${fileName}`
    }

    generateImageData(eventId, fileName) {
        const { fileNameWithoutExt }    = this.getFileData(fileName);
        const imagesData                = {};

        _.forEach(this.imagesTypes, type => {
            const _fileName             = `${type}.${fileName}`;
            const _fileNameWithoutExt   = `${type}.${fileNameWithoutExt}`;

            imagesData[type] = {
                pathToS3: this.getPath(eventId, _fileName),
                fileName: _fileNameWithoutExt,
                imageType: type,
            };
        });

        return imagesData;
    }

    setDBPathToImageData(eventId, imagesDataForDB) {
        _.forIn(imagesDataForDB, data => {
            data.pathToDB = this.getPath(eventId, data.fileName, true)
        });

        return imagesDataForDB;
    }

    get SAVE_IMAGE_SQL() {
        return `
            WITH
             "update_row" AS (
                UPDATE 
                    event_media 
                SET 
                    file_path = $1,
                    file_name = $5,
                    file_ext = $4
                WHERE event_id = $2 AND file_type = $3
                RETURNING *
                ),
            "insert_row" AS (
                INSERT INTO event_media (file_type, event_id, file_path, file_name, file_ext)
                SELECT 
                $3::event_media_type "file_type", 
                $2::INTEGER "event_id", 
                $1::TEXT "file_path", 
                $5::TEXT "file_name",
                $4::TEXT "file_ext"
                WHERE NOT EXISTS (SELECT * FROM "update_row") 
                RETURNING *
            )
            SELECT * from "insert_row"
            UNION 
            SELECT * FROM "update_row"
        `
    }

    get SAVE_FLOWCHART_SQL() {
        return `
            WITH
             "update_row" AS (
                UPDATE 
                    event_media 
                SET 
                    file_path = $1,
                    file_name = $5,
                    file_ext = $4
                WHERE event_id = $2 AND file_type = $3 AND division_id = $6
                RETURNING *
                ),
            "insert_row" AS (
                INSERT INTO event_media (file_type, event_id, file_path, file_name, file_ext, division_id)
                SELECT 
                $3::event_media_type "file_type", 
                $2::INTEGER "event_id", 
                $1::TEXT "file_path", 
                $5::TEXT "file_name",
                $4::TEXT "file_ext",
                $6::INTEGER "division_id"
                WHERE NOT EXISTS (SELECT * FROM "update_row") 
                RETURNING *
            )
            SELECT * from "insert_row"
        `
    }

    get GET_IMAGES_SQL() {
        return `
            SELECT event_media_id as id, concat(file_path, '.', file_ext) as path, file_type
            FROM event_media 
            WHERE event_id = $1 AND file_type <> 'small-logo'
        `
    }

    get REMOVE_IMAGE_SQL() {
        return `
            DELETE FROM event_media 
            WHERE event_media_id = $1
            RETURNING file_type, event_id
        `
    }

    get REMOVE_EVENT_IMAGES_SQL() {
        return `
            DELETE FROM event_media 
            WHERE event_id = $1 AND event_media_id = ANY($2)
            RETURNING file_type, event_id
        `
    }

    get REMOVE_SMALL_LOGO_SQL() {
        return `
            DELETE FROM event_media
            WHERE event_id = $1 AND file_type = 'small-logo'
        `
    }

}

module.exports = new EventMediaService();


