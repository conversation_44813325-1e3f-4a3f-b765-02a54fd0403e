const { updateTeam } = require('../../../validation-schemas/roster_team');
const { normalizeNumber } = require('../../../lib/swUtils');
const { ENTRY_STATUSES, PAYMENT_STATUSES, USAV_SANC_BODY } = require('../../../constants/teams');

const DIVISION_IS_FULL_ERROR = 'DIVISION_IS_FULL';

class TeamUpdateService {

    get DIVISION_IS_FULL_ERROR () {
        return DIVISION_IS_FULL_ERROR;
    }

    async process (eventID, rosterTeamID, data, userId) {
        let tr;

        try {
            await this.validateTeamData(eventID, rosterTeamID, data);

            const initialTeamData = await this.getInitialTeamData(eventID, rosterTeamID);

            if(data.division_price_change_agreed) {
                const feesDifference = await this.getDivisionsFeesDifference(
                    eventID, initialTeamData.division_id, data.division_id
                )

                if(feesDifference >= 0) {
                    throw { validation: 'New Division fee expected to be higher' };
                } else {
                    if(initialTeamData.status_entry !== ENTRY_STATUSES.PENDING) {
                        data.status_entry = ENTRY_STATUSES.PENDING;
                    }

                    if(initialTeamData.status_paid !== PAYMENT_STATUSES.NOT_PAID) {
                        data.status_paid = PAYMENT_STATUSES.NOT_PAID;
                    }
                }
            }

            tr = await Db.begin();

            const newTeamData = await this.updateTeam(data, eventID, rosterTeamID, tr);

            const historyData = this.collectTeamHistoryChanges(
                initialTeamData,
                newTeamData,
                userId,
                data.notes
            );

            await this.addChangesToHistory(historyData, tr);

            await tr.commit();

            return _.omit(newTeamData, 'created', 'modified', 'club_owner_id');
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            if(err.message === this.DIVISION_IS_FULL_ERROR) {
                await this.processDivisionFullError(rosterTeamID, eventID, data.division_id);
            } else {
                throw err;
            }
        }
    }

    async validateTeamData (eventID, rosterTeamID, data) {
        this.validatePayload(data);

        await this.checkEventClosed(eventID);
        await this.checkTeamNameUniqueness(rosterTeamID, data);
        await this.checkTeamCodeUniqueness(data.organization_code, rosterTeamID, eventID);
        await this.checkDivisionAge(rosterTeamID, data.division_id);
        await this.checkDivisionSeasonality(eventID, rosterTeamID, data.division_id);
    }

    validatePayload (teamData) {
        const { error } = updateTeam.validate(teamData);

        if (error) {
            throw { validation: error.details[0].message };
        }
    }

    async checkEventClosed (eventID) {
        let sql =
            `SELECT e.event_id, e.long_name "name"
             FROM "event" e 
             WHERE e.event_id = $1 
                AND e.date_end >= (NOW() AT TIME ZONE e.timezone)`;

        const event = await Db.query(sql, [eventID]).then(result => result.rows[0])

        if (_.isEmpty(event)) {
            throw { validation: 'Event is Closed' };
        }
    }

    async checkTeamNameUniqueness (rosterTeamID, data) {
        let tnRes = await Db.query(
            `WITH team AS (
                SELECT roster_club_id
                FROM roster_team
                WHERE roster_team_id = $2
            )
            SELECT count(*) 
            FROM roster_team
            WHERE team_name = $1
                AND gender = $3
                AND event_id IN (
                    SELECT event_id FROM roster_team WHERE roster_team_id = $2
                )
                AND roster_team_id <> $2
                AND deleted IS NULL
                AND roster_club_id = (
                    SELECT roster_club_id FROM team
                )`,

            [data.team_name, rosterTeamID, data.gender]
        );

        if(Number(tnRes.rows[0].count) !== 0) {
            throw {
                validationErrors: [{ message: 'Team with such name already exists within the event.' }]
            }
        }
    }

    async checkTeamCodeUniqueness (code, rosterTeamID, eventID) {
        let tcRes = await Db.query(
            `SELECT count(*) FROM roster_team
                 WHERE organization_code = $1
                   AND event_id = $3
                   AND roster_team_id <> $2
                   AND deleted IS NULL`,
            [code, rosterTeamID, eventID]
        );

        if(Number(tcRes.rows[0].count) !== 0) {
            throw {
                validationErrors: [{ message: 'Team with such code already exists within the event.' }]
            }
        }
    }

    async checkDivisionSeasonality (eventID, rosterTeamID, newDivisionID) {
        const query = knex('division AS d')
            .select({
                seasonality_is_invalid: knex.raw(
                    `rt.seasonality <> d.seasonality AND e.sport_sanctioning_id = ?`, [USAV_SANC_BODY.USAV]
                )
            })
            .join('event AS e', 'e.event_id', 'd.event_id')
            .join('roster_team AS rt', (table) => {
                table.on('rt.roster_team_id', rosterTeamID)
                    .andOn('rt.event_id', 'e.event_id')
            })
            .where('d.division_id', newDivisionID)
            .where('e.event_id', eventID);

        const seasonalityIsInvalid = await Db.query(query).then(result => result?.rows?.[0]?.seasonality_is_invalid);

        if(seasonalityIsInvalid) {
            throw {
                validationErrors: [{ message: `Team's USAV Seasonality and division's should be equal` }]
            }
        }
    }

    async getDivisionsFeesDifference (eventID, initialDivisionID, newDivisionID) {
        if(Number(initialDivisionID) === Number(newDivisionID)) {
            throw { validation: 'Division not changed' };
        }

        const query = knex('division AS init_d')
            .where('init_d.division_id', initialDivisionID)
            .whereRaw('init_d.event_id = e.event_id')
            .select({
                init_fee: knex.raw(`COALESCE(init_d.reg_fee, e.reg_fee, 0)`),
                new_fee: knex.raw(`COALESCE(new_d.reg_fee, e.reg_fee, 0)`),
            })
            .join('event AS e', knex.raw('e.event_id = ?', [eventID]))
            .leftJoin('division AS new_d', (table) => {
                table.on('new_d.division_id', newDivisionID)
                    .andOn(knex.raw('new_d.event_id = e.event_id'))
            });

        const {
            init_fee: initialDivisionFee,
            new_fee: newDivisionFee
        } = await Db.query(query).then(result => result?.rows?.[0] || {});

        let feesDifference = normalizeNumber(initialDivisionFee - newDivisionFee);

        return feesDifference;
    }

    async checkDivisionAge (rosterTeamID, divisionID) {
        let dRes = await Db.query(
            `SELECT ( 
                 CASE 
                    WHEN e.registration_method = 'doubles' 
                        THEN TRUE 
                    WHEN (e.teams_settings ->> 'manual_club_names')::BOOLEAN IS TRUE 
                        THEN TRUE
                    WHEN rt.age <= d.max_age 
                        THEN TRUE 
                    ELSE FALSE  
                 END 
             ) "allow_access",
             e.teams_use_clubs_module
            FROM roster_team rt 
            LEFT JOIN division d 
                ON d.division_id = $2 
            LEFT JOIN "event" e 
                ON e.event_id = rt.event_id 
            WHERE rt.roster_team_id = $1 
             AND d.division_id = $2`,
            [rosterTeamID, divisionID]
        );

        let { allow_access, teams_use_clubs_module } = dRes.rows[0].allow_access;

        if(teams_use_clubs_module && !allow_access) {
            throw { validationErrors: [{ message: 'Division max age must be not less than team\'s age.' }] }
        }
    }

    getInitialTeamData (eventID, rosterTeamID) {
        return Db.query(
            squel.select()
                .from('roster_team', 'rt')
                .field('rt.event_id')
                .field('rt.master_team_id')
                .field('rt.roster_team_id')
                .field('e.event_owner_id')
                .field('rt.team_name')
                .field('rt.organization_code')
                .field('rt.gender')
                .field('rt.age')
                .field('rt.rank')
                .field('e.event_type')
                .field('e.name', 'event_name')
                .field('e.long_name', 'event_long_name')
                .field('d.division_id')
                .field('d.name', 'division_name')
                .field('rt.status_paid')
                .field('COALESCE(d.reg_fee, e.reg_fee)', 'reg_fee')
                .left_join('division', 'd', 'rt.division_id = d.division_id')
                .left_join('event', 'e', 'rt.event_id = e.event_id')
                .where('rt.roster_team_id = ?', rosterTeamID)
                .where('rt.event_id = ?', eventID)
        ).then(result => result.rows[0]);
    }

    async updateTeam (teamData, eventID, rosterTeamID, tr) {
        let updateData = {
            team_name: teamData.team_name,
            organization_code: teamData.organization_code,
            gender: teamData.gender,
            age: teamData.age,
            rank: teamData.rank,
            division_id: teamData.division_id,
            manual_club_name: String(teamData.manual_club_name).trim(),
        }

        if(teamData.status_entry) {
            updateData.status_entry = teamData.status_entry;
        }

        if(teamData.status_paid) {
            updateData.status_paid = teamData.status_paid;
        }

        const [divisionData, rosterTeamData] = await Promise.all([
            Db.query(
                knex('division AS d')
                    .select({
                        division_name: 'd.name',
                        reg_fee: knex.raw(`COALESCE(d.reg_fee, e.reg_fee)`),
                        event_id: 'd.event_id'
                    })
                    .where('d.division_id', teamData.division_id)
                    .join('event AS e', 'e.event_id', 'd.event_id')
                    .limit(1)
            ).then(result => result.rows[0] || {}),
            Db.query(
                knex('roster_team AS rt')
                    .where('rt.roster_team_id', rosterTeamID)
                    .where('rt.event_id', eventID)
                    .limit(1)
            ).then(result => result.rows[0] || {}),
        ]);

        await tr.query(
            knex('roster_team AS rt')
                .update(updateData)
                .where('rt.roster_team_id', rosterTeamID)
                .where('rt.event_id', eventID)
        );

        return {
            ...rosterTeamData,
            ...updateData,
            ...divisionData,
        };
    }

    collectTeamHistoryChanges(initialTeamData, newTeamData, userId, notes) {
        const collectedHistoryDataToStore = [];
        const basicFields = {
            event_id: initialTeamData.event_id,
            roster_team_id: newTeamData.roster_team_id,
            user_id: userId,
        };

        if (newTeamData) {
            collectedHistoryDataToStore.push({
                ...basicFields,
                roster_club_id: (newTeamData && newTeamData.roster_club_id),
                event_owner_id: (newTeamData && newTeamData.event_owner_id),
                action: 'team.roster.changed',
                comments: (notes || null)
            });
        }

        if (initialTeamData.division_id !== newTeamData.division_id) {
            collectedHistoryDataToStore.push({
                ...basicFields,
                action: 'team.division.changed',
                division_id: newTeamData.division_id,
            });
        }

        if (initialTeamData.gender !== newTeamData.gender) {
            collectedHistoryDataToStore.push({
                ...basicFields,
                old_data: { gender: initialTeamData.gender },
                action: 'team.gender.changed',
                new_data: { gender: newTeamData.gender },
            });
        }

        if (initialTeamData.team_name !== newTeamData.team_name) {
            collectedHistoryDataToStore.push({
                ...basicFields,
                old_data: { team_name: initialTeamData.team_name },
                action: 'team.name.changed',
                new_data: { team_name: newTeamData.team_name },
            });
        }

        if (parseInt(initialTeamData.age) !== parseInt(newTeamData.age)) {
            collectedHistoryDataToStore.push({
                ...basicFields,
                old_data: { age: initialTeamData.age },
                action: 'team.age.changed',
                new_data: { age: newTeamData.age },
            });
        }

        if (initialTeamData.rank !== newTeamData.rank) {
            collectedHistoryDataToStore.push({
                ...basicFields,
                old_data: { rank: initialTeamData.rank },
                action: 'team.rank.changed',
                new_data: { rank: newTeamData.rank },
            });
        }

        return collectedHistoryDataToStore;
    }

    addChangesToHistory(historyData, tr) {
        return tr.query(
            knex('event_change')
                .insert(historyData)
        );
    }

    async processDivisionFullError (rosterTeamID, eventID, divisionID) {
        let teamsParams = { teams: [rosterTeamID], event: eventID };

        let message = await DivisionService.checkDivisionEntrance(teamsParams, divisionID)

        throw { validation: message };
    }

}

module.exports = new TeamUpdateService();
