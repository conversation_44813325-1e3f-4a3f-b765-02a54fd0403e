'use strict';

const config = sails.config.paymentHub;
const fetch = require('node-fetch');
const swUtils = require('../lib/swUtils');

class PaymentHubService {
    FEE_PERCENTAGE = 0.029;
    FEE_FIXED = 30; // cents
    SPLIT_ACCOUNTS = [
        {
            accountId: '01J2TDAQJ2ZQ5Z7SBTSDFK6FHY',
            amountPercentage: 1,
        },
    ];

    __getDefaultHeaderOptions() {
        const headers = new fetch.Headers();
        headers.set('Accept', 'application/json');
        headers.set('Content-Type', 'application/json');
        headers.set('x-api-key', config.apiKey);

        return headers;
    }

    async createPayment(data) {
        const createPaymentData = await this.__preparePaymentData(data);

        const response = await this.__sendRequest('/payment-intents', {
            method: 'POST',
            data: createPaymentData,
        });

        return response;
    }

    async updatePayment(data) {
        const updatePaymentData = await this.__preparePaymentData(data);

        const response = await this.__sendRequest(
            `/payment-intents/${data.paymentIntentId}`,
            { method: 'PUT', data: updatePaymentData }
        );

        return response;
    }

    async createCustomer(userId) {
        const paymentHubCustomer = await this.__sendRequest('/customers', {
            method: 'POST',
            data: { userId },
        });

        const query = knex('payment_hub.customer')
            .insert({
                payment_hub_customer_id: paymentHubCustomer.id,
                user_id: userId,
                payment_hub_customer_object: paymentHubCustomer,
            })
            .returning('*');

        return Db.query(query).then(({ rows }) => rows[0]);
    }

    async getCustomer(userId) {
        let customer = await this.findCustomerByUserId(userId);

        if (!customer) {
            customer = await this.createCustomer(userId);
        }

        return customer;
    }

    async findCustomerByUserId(userId) {
        const query = knex('payment_hub.customer')
            .select('*')
            .where('user_id', userId);

        return Db.query(query).then(({ rows }) => rows[0] || null);
    }

    __calculateRefundFee({ refundAmount, paymentAmount }) {
        const feeBeforeRefund = this.calculateDefaultFee(paymentAmount);
        const feeAfterRefund = this.__calculateFeeAfterRefund({
            refundAmount,
            paymentAmount,
        });

        return swUtils.normalizeNumber(feeBeforeRefund - feeAfterRefund);
    }

    __calculateFeeAfterRefund({ refundAmount, paymentAmount }) {
        return this.calculateDefaultFee(
            swUtils.normalizeNumber(paymentAmount - refundAmount)
        );
    }

    async createRefund(data) {
        const amountInCents = swUtils.normalizeNumber(data.amount * 100);
        const paymentAmountInCents = swUtils.normalizeNumber(
            data.paymentAmount * 100
        );
        const paymentHubFeeInCents = this.__calculateRefundFee({
            paymentAmount: paymentAmountInCents,
            refundAmount: amountInCents,
        });

        const marketplaceOrderFeeInCents = swUtils.normalizeNumber(
            data.marketplaceOrderFee * 100
        );

        const netAmountInCents =
            amountInCents - paymentHubFeeInCents - marketplaceOrderFeeInCents;

        const refundSplits = this.SPLIT_ACCOUNTS.map(
            ({ accountId, amountPercentage }) => ({
                accountId,
                amount: Math.round(amountPercentage * netAmountInCents),
            })
        );

        return this.__sendRequest('/refunds', {
            method: 'POST',
            data: {
                paymentId: data.paymentHubPaymentId,
                amount: amountInCents,
                refundSplits,
                currency: 'USD',
                metadata: data.metadata,
                marketplaceOrderFee: marketplaceOrderFeeInCents,
                platformOrderFee: 0,
            },
        });
    }

    async __preparePaymentData(data) {
        const amountInCents = swUtils.normalizeNumber(data.amount * 100);
        const marketplaceOrderFeeInCents = swUtils.normalizeNumber(
            data.marketplaceOrderFee * 100
        );
        const paymentHubFeeInCents = this.calculateDefaultFee(amountInCents);
        const netAmountInCents =
            amountInCents - marketplaceOrderFeeInCents - paymentHubFeeInCents;

        const paymentSplits = this.SPLIT_ACCOUNTS.map(
            ({ accountId, amountPercentage }) => ({
                accountId,
                amount: Math.round(amountPercentage * netAmountInCents),
            })
        );

        this.__validateTotalAmount(netAmountInCents, paymentSplits);

        const paymentData = {
            amount: amountInCents,
            currency: 'USD',
            gatewayType: 'stripe',
            paymentSplits,
            marketplaceOrderFee: marketplaceOrderFeeInCents,
            platformOrderFee: 0,
            customerId: data.customerId,
            // currently only card supported
            paymentMethodType: 'card',
            //todo: add statement descriptor
        };

        return paymentData;
    }

    calculateDefaultFee(
        amount,
        feePercentage = this.FEE_PERCENTAGE,
        feeFixed = this.FEE_FIXED
    ) {
        if (amount === 0) {
            return 0;
        }

        return Math.round(amount * feePercentage + feeFixed);
    }

    calculateTotalIncludingFees(
        amount,
        feePercentage = this.FEE_PERCENTAGE,
        feeFixed = this.FEE_FIXED
    ) {
        if (amount === 0) {
            return 0;
        }

        return swUtils.normalizeNumber(
            (amount + feeFixed) / (1 - feePercentage)
        );
    }

    calculateCustomerFee(
        amount,
        feePercentage = this.FEE_PERCENTAGE,
        feeFixed = this.FEE_FIXED
    ) {
        return (
            this.calculateTotalIncludingFees(amount, feePercentage, feeFixed) -
            amount
        );
    }

    __validateTotalAmount(amount, paymentSplits) {
        const totalPaymentSplits = paymentSplits.reduce(
            (acc, split) => acc + split.amount,
            0
        );

        if (totalPaymentSplits !== amount) {
            throw new Error('Total amount mismatch');
        }
    }

    async __sendRequest(url, options) {
        const preparedOptions = this.__prepareRequestOptions(options);

        const response = await fetch(`${config.apiUrl}${url}`, preparedOptions);

        if (!response.ok) {
            const responseData = await response.text();
            loggers.errors_log.error(`Error sending request to payment hub`, {
                response: {
                    ..._.pick(response, 'url', 'status', 'statusText'),
                },
                body: preparedOptions.body,
                data: responseData,
            });
            throw new Error("Cannot connect to Payment Service");
        }

        return response.json();
    }

    __prepareRequestOptions({ data, method }) {
        const options = {
            headers: this.__getDefaultHeaderOptions(),
            method,
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        return options;
    }
}

module.exports = new PaymentHubService();
