'use strict';

module.exports = {
    add: function (master_club_id, option) {  
        return Db.query(
            'INSERT INTO "webpoint_queue" ("master_club_id", "option") VALUES($1, $2)',
            [master_club_id, option]
        ).then(() => {});            
    },
    smart_pop: function () {
        let query = 
            ` SELECT   
                wq.webpoint_queue_id "id", mc.webpoint_username "username", wq.option, 
                mc.webpoint_password "password",   
                mc.webpoint_import_agree "agree",   
                mc.master_club_id, mc.code "club_code",
                u.email "user_email",
                mc.webpoint_club_id
             FROM "webpoint_queue" wq  
             LEFT JOIN master_club mc   
                 ON wq.master_club_id = mc.master_club_id   
             LEFT JOIN "club_owner" co 
                ON co."club_owner_id" = mc."club_owner_id"
             LEFT JOIN "user" u 
                ON u.user_id = co.user_id
             WHERE wq.requested IS NULL   
                AND wq.responded IS NULL
             ORDER BY mc.webpoint_import_agree ASC, wq.created ASC   
             LIMIT 1`

        return Db.query(query)
        .then(result => result.rows[0])
        .then(item => {
            if (!item) {
                return null;
            }

            if (item.agree) {
                return item;
            }

            return isNoAgreedAllowed()
            .then(allow => {
                if (allow) {
                    return item;
                }
                return null;
            })
        })

        function isNoAgreedAllowed () {
            let query = 
            `SELECT (
                 CASE  
                     WHEN NOW() - MAX(wq.responded) >= INTERVAL '5 minutes'   
                         THEN TRUE  
                     ELSE FALSE  
                 END
                ) "ok"
             FROM "webpoint_queue" wq`;

             return Db.query(query)
             .then(result => !!(result.rows[0] && result.rows[0].ok));
        }
    },

    set_requested: function(id) {  
        if (!id) {
            return Promise.reject(new Error('No Id passed'));
        }

        return Db.query(
            squel.update().table('webpoint_queue')
            .set('requested', 'NOW()')
            .where('webpoint_queue_id = ?', id)
        ).then(() => {});
    },

    set_responded: function (id, data) {
        if (!id) {
            return Promise.reject(new Error('No Id passed'));
        }

        let query = 
            squel.update().table('webpoint_queue')
                .set('responded', 'NOW()')
            .where('webpoint_queue_id = ?', id);

        if(data) {
            query.set(
                'response_body', 
                _.isObject(data)
                    ?JSON.stringify(data)
                    :`${data}`
            );
        }

        return Db.query(query);
    },

    findClubWPSyncDate: function (masterClubID) {
        if(!masterClubID) {
            return Promise.reject('Club ID required');
        }

        let query =
            `SELECT TO_CHAR(wq.requested, 'MM/DD/YYYY HH12:MI AM') "requested"
            FROM webpoint_queue wq
            WHERE wq.master_club_id = $1 AND wq.response_body LIKE '[{%'
            ORDER BY wq.responded DESC NULLS LAST
            LIMIT 1`;

        return Db.query(query, [masterClubID])
            .then(result => result.rows[0] && result.rows[0].requested || null);
    }
}
