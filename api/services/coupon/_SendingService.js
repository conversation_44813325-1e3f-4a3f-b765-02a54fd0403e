
const TeamsSendingService = require('./sending/_TeamsSendingService');
const CustomReceiversSendingService = require('./sending/_CustomReceiverSendingService');
const MessagesService = require('./sending/_MessagesService');

const COUPON_RECEIVER_TYPE = {
    TEAMS: Symbol('teams'),
    CUSTOM: Symbol('custom')
};

class SendingService {
    get teams () {
        return TeamsSendingService;
    }

    get customReceivers () {
        return CustomReceiversSendingService;
    }

    get messages () {
        return MessagesService;
    }

    get COUPON_RECEIVER_TYPE () {
        return COUPON_RECEIVER_TYPE;
    }

    get COUPON_RECEIVER_TYPES_LIST () {
        return Object.values(this.COUPON_RECEIVER_TYPE);
    }

    get COUPON_RECEIVERS_DATA_SOURCES () {
        return {
            [this.COUPON_RECEIVER_TYPE.TEAMS]: this.teams.getMessagesData.bind(this.teams),
            [this.COUPON_RECEIVER_TYPE.CUSTOM]: this.customReceivers.getMessagesData.bind(this.customReceivers)
        }
    }

    async sendCouponsMessages({ eventID, filters, teamsRecipientTypes, isResend }) {
        let messagesData = await Promise.all(
            this.COUPON_RECEIVER_TYPES_LIST.map(receiver =>
                this.COUPON_RECEIVERS_DATA_SOURCES[receiver]({eventID, filters, teamsRecipientTypes, isResend})
            )
        );

        messagesData = _.flatten(messagesData);

        return this.messages.send(messagesData);
    }
}

module.exports = new SendingService();
