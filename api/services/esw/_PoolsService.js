class PoolsService {
    constructor(swUtilsService, eswUtilsService) {
        this.swUtilsService = swUtilsService;
        this.eswUtilsService = eswUtilsService;
    }

    async getPoolInfo(eventId, poolId) {
        if(!this.swUtilsService.isESWId(eventId)) {
            throw { validation: 'Invalid Event Identifier passed' };
        }

        if(!this.swUtilsService.isUUID(poolId)) {
            throw { validation: 'No pool identifier passed' };
        }

        const poolData = await this.#getPoolData(eventId, poolId);

        if (poolData.upcoming_matches) {
            poolData.upcoming_matches = this.#decorateUpcomingMatches(poolData);
        }

        const poolSiblings = await this.#getPoolSiblings(poolData);

        return this.#addSiblingsToPoolData(poolData, poolSiblings);
    }

    async getPoolDataWithDivision(poolId) {
        const query =
            `SELECT pb.display_name, pb.team_count, pb.division_id, d.name
            FROM poolbrackets pb
                LEFT JOIN division d ON d.division_id = pb.division_id
            WHERE pb.uuid = $1`;

        const {rows: [pool]} = await Db.query(query, [poolId]);

        return pool;
    }

    async getPoolBracket(bracketId) {
        const poolBracket = await this.#getPoolBracketRow(bracketId);

        const poolBracketWithFlowChart = this.#addFlowChart(poolBracket);

        const poolSiblings = await this.#getPoolSiblings(poolBracketWithFlowChart);

        return this.#addSiblingsToPoolData(poolBracketWithFlowChart, poolSiblings);
    }

    async #getPoolBracketRow(bracketId) {
        const query =
            `SELECT 
                pb.event_id, pb.pb_seeds, pb.pb_finishes, pb.uuid AS bracket_id,
                concat_ws(' ', r.name, rr.name, pb.name) AS display_name,
                pb.name full_name, pb.display_name display_name_short, d.name division_name,
                bt.m1t1, bt.m1t2, bt.m1ref,
                bt.m2t1, bt.m2t2, bt.m2ref,
                bt.m3t1, bt.m3t2, bt.m3ref,
                bt.m4t1, bt.m4t2, bt.m4ref,
                bt.m5t1, bt.m5t2, bt.m5ref,
                bt.m6t1, bt.m6t2, bt.m6ref,
                bt.m7t1, bt.m7t2, bt.m7ref,
                bt.m8t1, bt.m8t2, bt.m8ref,
                bt.m9t1, bt.m9t2, bt.m9ref,
                bt.m10t1, bt.m10t2, bt.m10ref,
                bt.m11t1, bt.m11t2, bt.m11ref,
                bt.m12t1, bt.m12t2, bt.m12ref,
                bt.m13t1, bt.m13t2, bt.m13ref,
                bt.m14t1, bt.m14t2, bt.m14ref,
                bt.m15t1, bt.m15t2, bt.m15ref,
                bt.m16t1, bt.m16t2, bt.m16ref,
                bt.m17t1, bt.m17t2, bt.m17ref,
                bt.m18t1, bt.m18t2, bt.m18ref,
                bt.m19t1, bt.m19t2, bt.m19ref,
                bt.m20t1, bt.m20t2, bt.m20ref,
                bt.m21t1, bt.m21t2, bt.m21ref,
                bt.m22t1, bt.m22t2, bt.m22ref,
                bt.uuid, bt.win, bt."3rd",
                pb.flow_chart, pb.team_count, pb.sort_priority, pb.round_id, pb.consolation
            FROM poolbrackets pb
            LEFT JOIN division d ON d.division_id = pb.division_id
            LEFT JOIN bracketteams bt ON bt.uuid = pb.uuid
            LEFT JOIN rounds r ON pb.round_id = r.uuid
            LEFT JOIN rounds rr ON pb.group_id = rr.uuid
            WHERE pb.uuid = $1::UUID`;

        const {rows: [poolBracket]} = await Db.query(query, [bracketId]);

        if (_.isEmpty(poolBracket)) {
            throw {error: 'Pool is not found: ', bracket_id: bracketId};
        }

        return poolBracket;
    }

    async #getPoolData(eventId, poolId) {
        const query =
            `SELECT  
                pb.event_id, pb.uuid, pb.division_short_name,  
                concat_ws(' ', r.name, rr.name, pb.name) AS display_name, 
                pb.display_name display_name_short, pb.team_count, pb.is_pool, pb.consolation,  
                pb.pb_finishes, pb.pb_seeds, pb.pb_stats, pb.sort_priority, pb.round_id,
                (pb.event_id IN (72, 67, 19018) and pb.settings::JSON->>'SetCount' = '2' and pb.settings::JSON->>'PlayAllSets' = 'true') hide_winner,
                (
                    SELECT array_to_json(array_agg(row_to_json(upmatches))) 
                    FROM (
                        SELECT m.match_id, m.source,  
                            m.team1_roster_id, rt1.team_name team1_name,    rt1.organization_code team1_code,   
                            m.team2_roster_id, rt2.team_name team2_name,    rt2.organization_code team2_code,  
                            m.ref_roster_id,   rt3.team_name ref_team_name, rt3.organization_code ref_team_code,  
                            m.display_name, m.division_short_name,  
                            extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
                            c.name court_name, 
                            m.results, m.footnote_play, m.footnote_team1, m.footnote_team2
                        FROM matches m  
                        LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team1_roster_id LIMIT 1) rt1 ON true
                        LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team2_roster_id LIMIT 1) rt2 ON true
                        LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.ref_roster_id LIMIT 1) rt3 ON true
                        LEFT JOIN courts c ON c.uuid = m.court_id  
                        WHERE m.pool_bracket_id = pb.uuid  
                            AND m.secs_finished IS NULL 
                        ORDER BY m.secs_start, c.sort_priority, m.match_number
                    ) upmatches  
                ) "upcoming_matches",  
                (
                    SELECT array_to_json(array_agg(row_to_json(resmatches))) 
                    FROM (
                            SELECT m.match_id,  
                                m.team1_roster_id, rt1.team_name team1_name,    rt1.organization_code team1_code,   
                                m.team2_roster_id, rt2.team_name team2_name,    rt2.organization_code team2_code,  
                                m.ref_roster_id,   rt3.team_name ref_team_name, rt3.organization_code ref_team_code,  
                                m.display_name, m.division_short_name,  
                                extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
                                c.name court_name, 
                                m.results  
                        FROM matches m  
                        LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team1_roster_id LIMIT 1) rt1 ON true
                        LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.team2_roster_id LIMIT 1) rt2 ON true
                        LEFT JOIN LATERAL (SELECT rt.team_name, rt.organization_code FROM roster_team rt WHERE rt.roster_team_id = m.ref_roster_id LIMIT 1) rt3 ON true
                        LEFT JOIN courts c ON c.uuid = m.court_id  
                        WHERE m.pool_bracket_id = pb.uuid  
                            AND m.secs_finished IS NOT NULL 
                        ORDER BY m.secs_start, c.sort_priority, m.match_number
                    ) resmatches  
                ) "results", 
                (
                    SELECT array_to_json(array_agg(row_to_json(matches_standings))) 
                    FROM ( 
                        SELECT pb.display_name,  
                            d.short_name "division_short_name", d.name "division_name",  
                            pb.pb_stats  
                        FROM poolbrackets pb  
                        LEFT JOIN division d ON d.division_id = pb.division_id   
                        WHERE pb.event_id = e.event_id  
                            AND pb.uuid = $1
                    ) matches_standings
                ) "standings"
            FROM poolbrackets pb  
            INNER JOIN "event" e ON e.event_id = pb.event_id 
            LEFT JOIN rounds r ON pb.round_id = r.uuid  
            LEFT JOIN rounds rr ON pb.group_id = rr.uuid  
            WHERE pb.uuid = $1 AND e.esw_id = $2`;

        const {rows: [pool]} = await Db.query(query, [poolId, eventId])

        if(_.isEmpty(pool)) {
            throw { validation: `Pool does not exist ${poolId}` };
        }

        return pool;
    }

    #decorateUpcomingMatches(pool) {
        const {upcoming_matches, pb_seeds} = pool;

        return upcoming_matches.map((match, key) => {
            if (match.source && pb_seeds) {
                let source = [], seeds = [];
                let newPool = 0;

                try {
                    source = JSON.parse(match.source);

                    seeds = this.eswUtilsService.parseSeeds(pb_seeds);
                } catch (e) {
                    //
                }

                const firstTeam = source?.['team1'];
                const firstTeamType = firstTeam?.['type'];
                const firstTeamSeed = firstTeam?.['seed'];
                if (firstTeam && firstTeamType && firstTeamType === 5 && firstTeamSeed) {
                    newPool = seeds[firstTeamSeed];

                    if (newPool) {
                        upcoming_matches[key].team1_pool_id = newPool.id;
                        upcoming_matches[key].team1_pool_name = newPool.name;
                    }
                }

                const secondTeam = source?.['team2'];
                const secondTeamType = secondTeam?.['type'];
                const secondTeamSeed = secondTeam?.['seed'];
                if (secondTeam && secondTeamType && secondTeamType === 5 && secondTeamSeed) {
                    newPool = seeds[secondTeamSeed];

                    if (newPool) {
                        upcoming_matches[key].team2_pool_id = newPool.id;
                        upcoming_matches[key].team2_pool_name = newPool.name;
                    }
                }


                const referee = source?.['ref'];
                const refereeType = referee?.['type'];
                const refereeSeed = referee?.['seed'];
                if (referee && refereeType && refereeType === 5 && refereeSeed) {
                    newPool = seeds[refereeSeed];

                    if (newPool) {
                        upcoming_matches[key].ref_pool_id = newPool.id;
                        upcoming_matches[key].ref_pool_name = newPool.name;
                    }
                }
            }

            return match;
        });
    }

    async #getPoolSiblings(pool) {
        const query =
            `SELECT pb.uuid, concat(rr.short_name, ' ', pb.name) "name", pb.is_pool
             FROM poolbrackets pb
             INNER JOIN rounds r ON r.uuid = pb.round_id
             LEFT JOIN rounds rr ON pb.group_id = rr.uuid
             WHERE pb.division_short_name = $1 AND pb.event_id = $2
             ORDER BY r.sort_priority, rr.sort_priority, pb.sort_priority`;

        const {rows: poolSiblings} = await Db.query(query, [pool.division_short_name, pool.event_id])

        return poolSiblings;
    }

    #addSiblingsToPoolData(pool, siblings) {
        siblings.forEach(function (sibling, index, arr) {
            if (sibling.uuid === pool.uuid) {
                if (index > 0) {
                    const prev = arr[index - 1];

                    pool.prev = {
                        uuid: prev.uuid,
                        name: prev.name,
                        is_pool: prev.is_pool
                    };
                }

                if (index < arr.length - 1) {
                    const next = arr[index + 1];

                    pool.next = {
                        uuid: next.uuid,
                        name: next.name,
                        is_pool: next.is_pool
                    }
                }
            }
        });

        return pool;
    }

    #addFlowChart(poolBracket) {
        if (!poolBracket.flow_chart && poolBracket.team_count) {
            if (poolBracket.consolation === 0) {
                poolBracket.flow_chart = poolBracket.team_count + 'tnc.html';
            } else if (poolBracket.consolation === 1) {
                poolBracket.flow_chart = poolBracket.team_count + 'tw3.html';
            } else if (poolBracket.consolation === 2) {
                poolBracket.flow_chart = poolBracket.team_count + 'twc.html';
            }
        }

        return poolBracket;
    }
}

module.exports = PoolsService;
