
const {
    FORM_TYPE,
    TEXT_FIELD_VARIATION,
    NOT_EDITABLE_FIELDS
} = require('../../constants/event-custom-form');

const {
    EMAIL_REGEX,
    PHONE_REGEX,
    URL_REGEX
} = require('../../constants/regex-patterns');

const FormsListService = require('./custom-form/_FormsListService');
const FormEditingService = require('./custom-form/_FormEditingService');

class EventCustomFormService {

    #FORM_SERVICE = {
        [FORM_TYPE.TEAM_ASSIGN_FOR_EVENT]: require('./custom-form/team-assign-for-event/_FormService'),
        [FORM_TYPE.CAMPS_PURCHASE_PAGE]: require('./custom-form/camps-purchase-page/_FormService')
    }

    list = FormsListService;
    editing = FormEditingService;

    async getForm (eventID, eventFormID, submitterID) {
        let form = await this.#getCustomForm(eventID, eventFormID);

        const defaultValuesData = await this.#FORM_SERVICE[form.type].getDefaultValues(submitterID);

        form.fields = this.#addFieldsDefaultValues(form.fields, defaultValuesData);

        return form;
    }

    async submitForm (eventID, eventFormID, submitterID, submittedData) {
        let form = await this.#getCustomForm(eventID, eventFormID);

        this.#validateForm(submittedData, form.fields);

        if(!submitterID) {
            throw { validation: 'Form submitter not passed' };
        }

        let editableFields = form.fields.filter(f => !NOT_EDITABLE_FIELDS.includes(f.type));

        await this.#FORM_SERVICE[form.type].submitForm(
            eventID,
            eventFormID,
            submittedData,
            editableFields,
            submitterID
        );
    }

    async getFormResults (eventID, eventFormID) {
        let form = await this.#getCustomForm(eventID, eventFormID);

        const dataQuery = this.#FORM_SERVICE[form.type].getFormResultsSQL(eventID, eventFormID);

        return XLSXService.export(dataQuery, 'custom_form');
    }

    async checkIfFormSubmitted (formType, eventFormID, submitterID) {
        if(!eventFormID) {
            throw { validation: 'Event Form ID not passed' };
        }

        if(!submitterID) {
            throw { validation: 'Form submitter not passed' };
        }

        if(!formType) {
            throw { validation: 'Form type not passed' };
        }

        let result = await this.#FORM_SERVICE[formType].checkIfFormSubmitted(eventFormID, submitterID);

        return result;
    }

    async #getCustomForm (eventID, eventFormID) {
        let form = await this.#getCustomFormData(eventID, eventFormID);

        if(_.isEmpty(form)) {
            throw { validation: 'Form not found' };
        }

        return form;
    }

    async #getCustomFormData (eventID, eventFormID) {
        const query = `
        SELECT cfe.header_text,
               cfe.custom_form_event_id AS                                                               form_id,
               cfe.type,
               COALESCE(ARRAY_TO_JSON(ARRAY_AGG(json_build_object(
                       'type', cfft.type,
                       'options', cff.options,
                       'default_value', cff.default_value,
                       'is_required', cff.is_required,
                       'label', cff.label,
                       'id', cff.custom_form_field_id,
                       'variation', cff.variation,
                       'help_text', cff.help_text,
                       'section', cff.section,
                       'settings', cff.settings
                                                ) ORDER BY cff.sort_order)
                                      FILTER ( WHERE cff.custom_form_field_id IS NOT NULL)), '[]'::JSON) fields
        FROM custom_form_event cfe
                 LEFT JOIN custom_form_field cff ON cff.custom_form_event_id = cfe.custom_form_event_id
                 LEFT JOIN custom_form_field_type cfft ON cfft.custom_form_field_type_id = cff.custom_form_field_type_id
        WHERE cfe.event_id = $1
          AND cfe.custom_form_event_id = $2
        GROUP BY cfe.header_text, cfe.custom_form_event_id, cfe.type`;

        const { rows: [form] } = await Db.query(query, [eventID, eventFormID]);

        if(!_.isEmpty(form)) {
            await this.#getDynamicSelectOptions(form.fields);
        }

        return form;
    }

    async #getDynamicSelectOptions (fields) {
        for(let field of fields) {
            if(['select', 'multiselect'].includes(field.type) && field?.settings?.optionsQuery) {
                const optionsFromDB = await this.#getOptionsFromDB(field?.settings?.optionsQuery);

                delete field.settings.optionsQuery;

                if(!_.isEmpty(optionsFromDB)) {
                    field.options = optionsFromDB;
                }
            }
        }
    }

    async #getOptionsFromDB (query) {
        const { rows: values } = await Db.query(query);

        if(_.isEmpty(values)) {
            return [];
        }

        return values.map(item => {
            return {
                label: item.label,
                id: this.#generateFieldName(item.label)
            }
        });
    }

    #generateFieldName (text) {
        let replRegExp = /[^a-z0-9]/ig;
        return text.toLowerCase().replace(replRegExp, '_');
    };

    #addFieldsDefaultValues (formFields, defaultValues) {
        if(_.isEmpty(defaultValues)) {
            return formFields;
        }

        return formFields.map(ff => {
            if(ff.default_value) {
                ff.default_value = defaultValues[ff.default_value];
            }

            return ff;
        })
    }

    #validateForm (submittedData, formFields) {
        for (const formField of formFields) {
            const submittedFieldData = submittedData[formField.id];

            if(formField.is_required &&
                (
                    (_.isEmpty(submittedFieldData) && _.isObject(submittedFieldData)) ||
                    (_.isUndefined(submittedFieldData) || _.isNull(submittedFieldData) || _.isNaN(submittedFieldData))
                )
            ) {
                throw { validation: 'Some required fields are not filled' };
            }

            if(formField.variation && submittedFieldData) {
                this.#validateFormVariation(formField, submittedFieldData);
            }

            if(formField.type === 'multiselect' && !Array.isArray(submittedFieldData)) {
                throw { validation: 'Invalid submitted data type for select' };
            }
        }
    }

    #validateFormVariation (formField, submittedFieldData) {
        let variationIsValid = true;

        switch (formField.type === 'text' && formField.variation) {
            case TEXT_FIELD_VARIATION.EMAIL :
                variationIsValid = EMAIL_REGEX.test(submittedFieldData);
                break;
            case TEXT_FIELD_VARIATION.URL :
                variationIsValid = URL_REGEX.test(submittedFieldData);
                break;
            case TEXT_FIELD_VARIATION.PHONE :
                variationIsValid = PHONE_REGEX.test(submittedFieldData);
                break;
        }

        if(!variationIsValid) {
            throw { validation: `${formField.label} field value is invalid` };
        }
    }
}

module.exports = new EventCustomFormService();
