'use strict';

class EventOwnerService {
	constructor () {}

	findEvents (user) {
        var events = [],
            k = ((user.shared_events && Object.keys(user.shared_events)) || []);

        for(var i = 0, l = k.length, item; i < l; ++i) {
            item = user.shared_events[k[i]];
            if(item.role_co_owner) events.push(k[i])
        }

        return events;
    }

    findAllEvents(user) {
	    const ownEvents = user.events || [];

	    return this.findEvents(user).concat(ownEvents);
    }

    findAvailableEventsOwnersID (user) {
        let godEOList = user.eo_list;
        let list      = user.event_owner_id ? [user.event_owner_id] : [];

	    if(!_.isEmpty(godEOList)) {
	        return list.concat(Object.keys(godEOList));
        }

	    return (Object.values(user.shared_events || {})).reduce((all, event) => {
	        all.push(Number(event.event_owner_id));
            return all;
        }, list);
    }

    // covered 😄👍
	findId (eventId, user) {
		eventId = +eventId;

		if (!eventId) {
			throw new Error('Invalid Event Identifier');
		}

		let eventOwnerId    = user.event_owner_id,
			ownEvents 		= user.events || [],
        	eventUser       = user.shared_events && user.shared_events[eventId],
		    headOfficial    = user.head_official_events && user.head_official_events[eventId],
            godEOList       = user.eo_list;
loggers.debug_log.debug('findId', eventId, eventOwnerId, ownEvents, eventUser, headOfficial, godEOList);
        if (ownEvents.indexOf(eventId) !== -1) {
            console.log(1);
        	return parseInt(eventOwnerId, 10);
        } else if(eventUser && eventUser.role_co_owner) {
            console.log(2);
	        return parseInt(eventUser.event_owner_id, 10)
	    } else if(headOfficial && headOfficial.event_owner_id) {
            console.log(3);
            return parseInt(headOfficial.event_owner_id, 10);
        } else if(godEOList) {
            console.log(4);
            return this.__findEOInGodEOList(godEOList, eventId);
        }

	    return null;
	}

    async getClothesTypes() {
        const {rows: common_items} = await Db.query(
            squel.select()
                .from('common_item', 'ci')
                .where(`ci.item_type = 'event_clothes'`)
                .order(`ci.details->'order'`)
                .field('*')
        );
        return common_items;
    }

    async getClothesRequirements(eventID) {
        const [
            { rows: event_clothes },
            commonItems,
        ] = await Promise.all([
            Db.query(squel.select()
                .from('event_clothes', 'ec')
                .where('ec.event_id = ?', eventID)
                .field('*')
            ),
            this.getClothesTypes(),
        ]);
        /*
        requirements = {
            [memberType]: {
                [gender]: [
                    {
                        common_item_id,
                        required,
                    },
                    ...
                },
                ...
            },
            ...
        }
         */
        const requirements = ['official', 'staff'].reduce((result, memberType) => {
            result[memberType] = ['male', 'female'].reduce((result, gender) => {
                result[gender] = commonItems
                    .filter(v => ['any', gender].includes(v.details.gender))
                    .map(
                        ({common_item_id}) => ({
                            common_item_id,
                            required: (event_clothes.find(
                                ec => ec.common_item_id === common_item_id
                                    && ec.gender === gender
                                    && ec.member_type === memberType
                            ) || {deleted: true}).deleted === null
                        })
                    )
                return result;
            }, {});
            return result;
        }, {});

        return requirements;
    };

	getEventsPermissions (events) {
        return Object.keys(events).reduce((acl, eventID) => {
            acl[eventID] = events[eventID].permissions;

            return acl;
        }, {})
    }

    __findEOInGodEOList (list, eventID) {
	    for(let eoID of Object.keys(list)) {
	        if(_.some(list[eoID], event => event === eventID)) {
                return Number(eoID);
            }
        }

        return null;
    }
}

module.exports = new EventOwnerService();
