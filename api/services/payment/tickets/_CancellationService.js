const knex          = require('knex')({client: 'pg'});
const CashService   = require('./cancellation/__CashService');

class CancellationService {
    constructor () {
        this.cash = new CashService(this);
    }

    get ALLOWED_TO_CANCEL_PURCHASE_TYPES () {
        return [SWTPaymentsService.CHECK_METHOD, SWTPaymentsService.CASH_METHOD, SWTPaymentsService.FREE_METHOD];
    }

    get PURCHASE_STATUS_CANCELED () {
        return PaymentService.tickets.participation.PAYMENT_STATUS.CANCELED;
    }

    get PURCHASE_TYPE_CASH () {
        return SWTPaymentsService.CASH_METHOD;
    }

    get PURCHASE_STATUS_PENDING () {
        return PaymentService.tickets.participation.PAYMENT_STATUS.PENDING;
    }

    get PURCHASE_STATUS_PAID () {
        return PaymentService.tickets.participation.PAYMENT_STATUS.PAID;
    }

    get PURCHASE_TYPE_FREE () {
        return SWTPaymentsService.FREE_METHOD;
    }

    async voidPurchase (eventID, ticketBarcode, restoreDiscounts, userID) {
        if (!ticketBarcode) {
            throw { validation: 'Invalid Payment Code' };
        }

        const purchaseInfo = await this.__getPurchaseInfo(ticketBarcode, eventID);

        if(_.isEmpty(purchaseInfo)) {
            throw { validation: 'Purchase not found' };
        }

        const {
            ticket          : [ticket],
            payment         : [payment],
            other_tickets   : otherTickets,
            is_kiosk_payment,
            require_tickets_names
        } = purchaseInfo;

        if (this.__purchaseIsNotAllowedToBeCancelled(ticket, is_kiosk_payment, payment.type)) {
            throw { validation: 'No Suitable Payment found for cancellation' };
        }

        if(!is_kiosk_payment && require_tickets_names) {
            throw { validation: `Can't void non kiosk assigned ticket` };
        }

        if(ticket.status === this.PURCHASE_STATUS_PAID && payment.type === this.PURCHASE_TYPE_CASH) {
            return this.cash.refundPaidTickets(payment, ticket, otherTickets, eventID, userID);
        } else {
            return this.__cancelPurchase(ticket, payment, otherTickets, eventID, restoreDiscounts, userID);
        }
    }

    savePurchaseCancellationActionToHistory (purchase, userID, tr) {
        const query = `INSERT INTO "purchase_history" 
                    ("purchase_id", "action", "description", "user_id", "amount")
                 VALUES ($1, 'purchase.canceled', $2, $3, $4)`;

        return tr.query(query, [purchase.purchase_id, `Purchase canceled`, userID, purchase.amount])
    }

    cancelPurchase (purchaseIds, tr) {
        let query = knex('purchase')
            .update({
                status          : this.PURCHASE_STATUS_CANCELED,
                canceled_date   : knex.fn.now()
            })
            .whereIn('purchase_id', purchaseIds)
            .where('status', '<>', this.PURCHASE_STATUS_CANCELED)
            .whereNull('canceled_date');

        return tr.query(query);
    }

    cancelPurchaseTickets (allPurchaseIds, tr) {
        let query = knex('purchase_ticket')
            .update({ canceled: knex.fn.now() })
            .whereIn('purchase_id', allPurchaseIds)
            .whereNull('canceled');

        return tr.query(query);
    }

    async __cancelPurchase (ticket, payment, otherTickets, eventID, restoreDiscounts, userID) {
        let tr;

        try {
            tr = await Db.begin();

            const allPurchaseIds = [
                payment.purchase_id,
                ticket.purchase_id,
                ...otherTickets.map(t => t.purchase_id)
            ];

            await this.cancelPurchase(allPurchaseIds, tr);
            await this.cancelPurchaseTickets(allPurchaseIds, tr);

            if (payment.type === this.PURCHASE_TYPE_FREE && restoreDiscounts) {
                await this.__updateTicketDiscount(payment, tr);
            }

            await this.savePurchaseCancellationActionToHistory(payment, userID, tr);

            return tr.commit();
        } catch (err) {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }

            throw err
        }
    }

    __getPurchaseInfo (ticketBarcode, eventID) {
        let query = `
            SELECT (SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t")))
                    FROM (SELECT ticket.purchase_id,
                                 ticket.status,
                                 ticket.amount,
                                 COALESCE(
                                         NULLIF(pt.ticket_fee, 0),
                                         NULLIF(et.application_fee, 0),
                                         e.tickets_sw_fee,
                                         0
                                     ) "app_fee") "t")     "ticket",
            
                   (SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("p")))
                    FROM (SELECT payment.purchase_id,
                                 payment.status,
                                 payment.net_profit,
                                 payment.collected_sw_fee,
                                 payment.amount,
                                 payment.amount_refunded,
                                 payment.type) "p")        "payment",
            
                   (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("o"))), '[]'::JSON)
                    FROM (
                             SELECT other.purchase_id
                             FROM purchase other           
                             WHERE other.purchase_id IS NOT NULL
                               AND other.status <> $3
                               AND other.linked_purchase_id = ticket.linked_purchase_id
                               AND other.purchase_id <> ticket.purchase_id
                         ) "o"
                   )                                       "other_tickets",
            
                   (e."tickets_settings" ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
                                                           "require_tickets_names",
            
                   (payment.kiosk IS NOT NULL AND payment.kiosk <> '{}'::JSONB AND
                    payment.status IN ($4, $5)) "is_kiosk_payment"
            
            FROM purchase ticket
                     JOIN purchase payment
                          ON payment.is_payment IS TRUE 
                            AND (payment.purchase_id = ticket.linked_purchase_id 
                            OR payment.purchase_id = ticket.purchase_id)                
                     JOIN event e ON e.event_id = ticket.event_id
                     JOIN purchase_ticket pt ON pt.purchase_id = ticket.purchase_id
                     JOIN event_ticket et ON et.event_ticket_id = pt.event_ticket_id
            WHERE ticket.ticket_barcode = $1
              AND ticket.event_id = $2
              AND ticket.is_ticket IS TRUE`;

        return Db.query(query, [
            ticketBarcode,
            eventID,
            this.PURCHASE_STATUS_CANCELED,
            this.PURCHASE_STATUS_PAID,
            this.PURCHASE_STATUS_PENDING
        ]).then(result => result.rows[0] || {});
    }

    __purchaseIsNotAllowedToBeCancelled (ticket, is_kiosk_payment, type) {
        return !ticket || (!is_kiosk_payment && !this.ALLOWED_TO_CANCEL_PURCHASE_TYPES.includes(type));
    }

    async __updateTicketDiscount (payment, tr) {
        let result = await tr.query(
            `UPDATE "ticket_discount" td 
                         SET "used_count" = ("used_count" - pt.qty)
                     FROM (
                         SELECT 
                             pt.ticket_discount_id, 
                             pt.discounted_quantity "qty", 
                             p.event_id
                         FROM "purchase_ticket" pt 
                         INNER JOIN "purchase" p 
                             ON p.purchase_id = pt.purchase_id
                         WHERE "pt".purchase_id = $1
                             AND "pt".ticket_discount_id IS NOT NULL 
                             AND "pt".discounted_quantity IS NOT NULL
                             AND "pt".discounted_quantity > 0
                     ) "pt"
                     WHERE td.ticket_discount_id = pt.ticket_discount_id
                         AND td.event_id = pt.event_id
                     RETURNING pt."qty", td.ticket_discount_id, td.code`, [payment.purchase_id]
        );

        if (result.rows.length > 0) {
            return (Promise.all(
                result.rows.map(
                    restoredDiscount => this.__saveDiscountRestoreActionToHistory(payment, restoredDiscount, tr)
                )
            ));
        }
    }

    __saveDiscountRestoreActionToHistory (payment, restoredDiscount, tr) {
        const query = `INSERT INTO "purchase_history" (
                                    "purchase_id", "action", "description"
                                 ) VALUES (
                                    $1, 'discount.restored', 
                                    concat_ws(' ', 'Restored', $2::TEXT, 'Item(s) for Discount', $3::TEXT)
                                 )`;
        return tr.query(query, [payment.purchase_id, restoredDiscount.qty, restoredDiscount.code]);
    }
}

module.exports = new CancellationService();
