class TagNameFactory {
    dbTable(name, fields = {}) {
        let tag = ['table', name];
        const keys = Object.keys(fields).filter(key => fields[key]).sort();
        for(const key of keys) {
            tag.push(key);
            tag.push(stringifyFieldValue(fields[key]));
        }
        return tag.join(':');
    }
}

module.exports = new TagNameFactory();

function stringifyFieldValue(value) {
    if(!_.isString(value) && !_.isNumber(value)) {
        throw new Error('Unsupported parameter type');
    }

    return value.toString();
}
