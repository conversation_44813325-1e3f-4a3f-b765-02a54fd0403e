class CacheError extends Error{
    constructor(message, {req, prevous} = {}) {
        super(message);
        this.prevous = prevous;
        if(req) {
            this.req = req;
        }
    }

    get name() {
        return this.constructor.name;
    }

    set req(req) {
        if(!_.isObject(req)) {
            return;
        }
        this.requestUrl = req.url;
        if(req.user) {
            this.userId = req.user.user_id;
        }
    }
}

module.exports = CacheError;
