

class TicketsPurchaseRefund {
    constructor(webhook, purchase) {
        loggers.debug_log.verbose('Processing tickets refund options');

        if(_.isEmpty(webhook)) {
            throw { text: 'Webhook object required' };
        }

        if(_.isEmpty(purchase)) {
            throw { text: 'Purchase object required' };
        }

        this.webhook = webhook;
        this.purchase = purchase;
    }

    get RefundService () {
        return PaymentService.tickets.refunds;
    }

    proceed (refundAmount, chargeID) {
        if(!refundAmount) {
            throw { text: 'Refund Amount required' };
        }

        if(!chargeID) {
            throw { text: 'Charge ID required' };
        }

        this.refundAmount = refundAmount;
        this.chargeID = chargeID;

        if(this.__isBasicTicketsRefund()) {
            return this.__proceedBasicTicketsRefund();
        }

        if(this.__isAssignedTicketsRefund()) {
            if(this.__isFullRefund()) {
                return this.__proceedAssignedTicketsFullRefund();
            } else {
                return this.__proceedAssignedTicketsPartialRefund();
            }
        }
    }

    __isBasicTicketsRefund () {
        return this.purchase.is_payment && this.purchase.is_ticket;
    }

    __isAssignedTicketsRefund () {
        return this.purchase.is_payment === true && this.purchase.is_ticket === false;
    }

    __isFullRefund () {
        return this.refundAmount === (this.purchase.amount + this.purchase.amount_refunded);
    }

    __proceedBasicTicketsRefund () {
        let stripeEventID = this.webhook.id;

        if(!stripeEventID) {
            throw { text: 'Webhook object without ID' };
        }

        return this.RefundService.webhookFullRefundBasicTickets(
            this.refundAmount, this.chargeID, stripeEventID
        ).catch(err => { throw { text: err.message, sendEmail: false }})
    }

    __proceedAssignedTicketsFullRefund () {
        return this.RefundService.webhookFullRefundAssignedTickets(this.purchase)
            .catch(err => { throw { text: err.message, sendEmail: false }})
    }

    __proceedAssignedTicketsPartialRefund () {
        loggers.debug_log.verbose('Ignoring partial refund');

        let barcode = null;
        let eventID = this.purchase.event_id;

        if(!eventID) {
            throw { text: 'Event ID not fund in purchase object' };
        }

        try {
            barcode = JSON.parse(this.webhook.data.object.metadata.ticket_holders)[0].barcode;
        } catch (err) {
            throw { text: 'Barcode not found in metadata' };
        }

        let refundNotificationData = {
            ticketCode: barcode,
            refundedAmount: this.refundAmount
        };

        return this.RefundService.sendStripeDashboardRefundNotification(
            this.purchase.event_id, refundNotificationData
        ).catch(err => { throw { text: err.message, sendEmail: false }})
    }
}

module.exports = TicketsPurchaseRefund;
