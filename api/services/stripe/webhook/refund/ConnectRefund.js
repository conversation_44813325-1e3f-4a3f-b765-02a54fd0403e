
const TicketsPurchaseRefund = require('./connect/TicketsPurchaseRefund');
const TeamsPurchaseRefund = require('./connect/TeamsPurchaseRefund');
const BoothsPurchaseRefund = require('./connect/BoothsPurchaseRefund');

class ConnectRefund {
    constructor (webhookData) {
        this.webhook = webhookData;
    }

    async getPurchaseData (chargeID) {
        this.purchase = await Db.query(
            squel.select()
                .from('purchase')
                .where('stripe_charge_id = (?)::TEXT', chargeID)
                .fields([
                    'purchase_id', 'event_id', 'status', 'amount', 'amount_refunded', 'is_payment', 'is_ticket',
                    'payment_for'
                ])
        ).then(result => result && result.rows[0]);

        return this.purchase;
    }

    __getRefundTypeInstance () {
        if(_.isEmpty(this.purchase)) {
            throw { text: 'Purchase is empty' };
        }

        // Tickets payment refund processing
        if(this.purchase.payment_for === 'tickets') {
            return new TicketsPurchaseRefund(this.webhook, this.purchase);
        }

        // Teams payment refund processing
        if(this.purchase.payment_for === 'teams') {
            return new TeamsPurchaseRefund(this.purchase);
        }

        // Booths payment refund processing
        if(this.purchase.payment_for === 'booths') {
            return new BoothsPurchaseRefund();
        }

        throw { text: 'Invalid refund type' };
    }

    async process (refundAmount, chargeID) {
        let RefundType = this.__getRefundTypeInstance();

        await RefundType.proceed(refundAmount, chargeID);

        await this.__updateStripeChargeRow(chargeID)
    }

    async __updateStripeChargeRow(chargeID) {
        let errors = [];

        await StripeService.webhook.charge.updateRow({chargeID}).catch(errors.push.bind(errors));

        if (errors.length > 0) {
            throw errors;
        }
    }
}

module.exports = ConnectRefund;
