'use strict';

const ConnectDisputeService = require('./dispute/__ConnectDisputeService');
const CustomPaymentDisputeService = require('./dispute/__CustomPaymentDisputeService');
const DisputeNotificationService = require('./notification/__DisputeNotificationService');

class DisputeService {
    constructor () {
        this.DisputeNotificationService = new DisputeNotificationService(this);
    }

    get STRIPE_DISPUTE_FEE () {
        // https://stripe.com/docs/disputes/faq
        return 15;
    }

    get connectService () {
        return require('../../../lib/StripeConnect');
    }

    get connectCharge () {
        return new ConnectDisputeService(this.connectService, this);
    }

    get customPayment () {
        return new CustomPaymentDisputeService(this);
    }

    get DISPUTE () {
        return  {
            CLOSED  : 'charge.dispute.closed',
            CREATED : 'charge.dispute.created'
        }
    }

    get notifications () {
        return this.DisputeNotificationService;
    }

    async process(disputeEvent) {
        let disputeType    = disputeEvent.type,
            disputeStatus  = disputeEvent.data.object.status,
            stripeEventId  = disputeEvent.id,
            chargeID       = disputeEvent.data.object.charge;

        if(![this.DISPUTE.CLOSED, this.DISPUTE.CREATED].includes(disputeType)) {
            return;
        }

        let { purchase, type } = await this.__getPurchaseData(disputeEvent.data.object.charge);

        ErrorSender.webhookDispute({
            stripeEventObj  : disputeEvent,
            purchase        : purchase
        });

        if(_.isEmpty(purchase)) {
            loggers.errors_log.error('Purchase not found for dispute event\n', disputeEvent);
            return;
        }

        if(type === 'custom') {
            await this.customPayment.process(disputeEvent.data.object, purchase, disputeType);
        } else {
            await this.connectCharge.process(disputeType, disputeStatus, chargeID, stripeEventId, purchase);

            const isWonDispute = this.isWonDispute(disputeType, disputeStatus);

            if(isWonDispute || this.DISPUTE.CREATED === disputeType) {
                await this.notifications.sendWebhookOccurredEmail(disputeEvent);
            }
        }

        if (purchase.payment_for === 'tickets') {
            await this._banEmail(purchase.email, purchase.user_email, purchase.event_id, purchase.purchase_id);
        }

        return this._banFingerprint(purchase.fingerprint, purchase.event_id, purchase.purchase_id);
    }

    isLostDispute (disputeType, disputeStatus) {
        return disputeType === this.DISPUTE.CLOSED && disputeStatus === StripeService.DISPUTE_STATUS.LOST;
    }

    isWonDispute (disputeType, disputeStatus) {
        return disputeType === this.DISPUTE.CLOSED && disputeStatus === StripeService.DISPUTE_STATUS.WON;
    }

    async __getPurchaseData (chargeID) {
        let { type, purchase_id: purchaseID } = await this.__getPurchase(chargeID);

        let purchaseData;

        if(type === 'custom') {
            purchaseData = await this.customPayment.getPurchaseData(purchaseID);
        } else {
            purchaseData = await this.connectCharge.getPurchaseData(purchaseID);
        }

        return { purchase: purchaseData, type };
    }

    async __getPurchase (chargeID) {
        let query = `
            SELECT purchase_id "purchase_id", 'connect' "type"
            FROM purchase
            WHERE stripe_charge_id = $1
            UNION
            SELECT custom_payment_id "purchase_id", 'custom' "type"
            FROM custom_payment cp
                     JOIN stripe_payment_intent AS spi ON spi.stripe_payment_intent_id = cp.stripe_payment_intent_id
            WHERE spi.stripe_charge_id = $1
        `;

        let purchase = await Db.query(query, [chargeID]).then(result => result && result.rows[0] || null);

        if(_.isEmpty(purchase)) {
            throw {
                text: 'Purchase not found', sendEmail: false
            }
        }

        return purchase;
    }

    async _banEmail(paymentEmail, userEmail, eventID, purchaseID) {
        const eoEmails = await this.getEOEmails();
        paymentEmail = (paymentEmail || '').toLowerCase();
        userEmail = (userEmail || '').toLowerCase();

        return Promise.all([
            (!!paymentEmail && !this.isEOEmail(eoEmails, paymentEmail))
                ? banService.banEmail(paymentEmail, 'dispute', eventID, purchaseID)
                : null,
            ((!!userEmail && !this.isEOEmail(eoEmails, userEmail)) && (paymentEmail !== userEmail))
                ? banService.banEmail(userEmail, 'dispute', eventID, purchaseID)
                : null
        ])
    }

    async _banFingerprint(fingerprint, eventID, purchaseID) {
        return (!!fingerprint)
            ? banService.banFingerprint(fingerprint, 'dispute', eventID, purchaseID)
            : null;
    }

    getEOEmails() {
        const query = knex('event_owner AS eo')
            .select({
                emails: knex.raw('COALESCE(ARRAY_AGG(u.email), ARRAY[]::TEXT[])')
            })
            .join('user AS u', 'u.user_id', 'eo.user_id')
            .where('eo.approved', true);

        return Db.query(query).then(({ rows: [row] }) => row.emails);
    }

    /**
     * @param {Array<String>} eoEmails
     * @param {String} email
     *
     * @returns {Boolean}
     */
    isEOEmail(eoEmails, email) {
        return eoEmails.includes(email);
    }
}

module.exports = new DisputeService();
