class FileService {
    constructor(StripeConnect) {
        this.StripeConnect = StripeConnect;
        this.stripe = null;
    }

    get PURPOSE() {
        return {
            ACCOUNT_REQUIREMENT: 'account_requirement',
            ADDITIONAL_VERIFICATION: 'additional_verification',
            BUSINESS_ICON: 'business_icon',
            BUSINESS_LOGO: 'business_logo',
            CUSTOMER_SIGNATURE: 'customer_signature',
            DISPUTE_EVIDENCE: 'dispute_evidence',
            IDENTITY_DOCUMENT: 'identity_document',
            PCI_DOCUMENT: 'pci_document',
            TAX_DOCUMENT_USER_UPLOAD: 'tax_document_user_upload',
            TERMINAL_READER_SPLASHSCREEN: 'terminal_reader_splashscreen',
        }
    }

    get ALLOWED_PURPOSES() {
        return Object.values(this.PURPOSE)
    }

    async uploadFile({file, filename, purpose}) {
        await this._initStripe();

        if (!this.ALLOWED_PURPOSES.includes(purpose)) {
            throw new Error('Invalid file purpose was passed');
        }

        const stripeFile = await this.stripe.files.create({
            purpose,
            // hack that makes stripe to send nested object, library is old and does not support it
            'file_link_data[create]': 'true',
            file: {
                data: file,
                name: filename,
            },
        }).catch(err => {
            loggers.errors_log.error('Error uploading file to stripe ' + err);
            throw err
        })

        return stripeFile;
    }

    async _initStripe() {
        if (this.stripe) {
            return;
        }

        this.stripe = await this.StripeConnect.getInstanceV2();
    }
}

module.exports = FileService;
