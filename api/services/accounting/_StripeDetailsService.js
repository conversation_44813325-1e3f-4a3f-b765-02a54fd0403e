'use strict';

const co = require('co');

const CARD_PAYMENT = 'card';
const ACH_PAYMENT  = 'ach';
const CHECK_PAYMENT= 'check';
const CASH_PAYMENT = 'cash';

class StripeDetailsService {
    constructor() {}

    // GET  /api/event/:event/accounting/details?for={teams, tickets, booths}
    getDetails(eventId, paymentFor) {
        const details = {
            dueToEvent: 0,
            manualPayouts: 0,
            lostDisputes: 0,
            refundAdjustments: 0,
            escrowAccBeingHeld: 0,
            totalAvailableForTickets : 0,
            stillAvailableToPauout: 0,
            stripeAcctTotalForThisEvent: 0
        };

        return co(function* () {
            let creditStat = yield AccountingService.Common.merchantStat(eventId, CARD_PAYMENT, paymentFor);
            let achStat = yield AccountingService.Common.merchantStat(eventId, ACH_PAYMENT, paymentFor);

            let checkStat;
            let cashStat; 

            if(paymentFor === AccountingService.Common.TICKETS_PAYMENT_FOR_TYPE) {
                cashStat = yield AccountingService.Common.merchantStat(eventId, CASH_PAYMENT, paymentFor);
                details.dueToEvent = creditStat.balance + achStat.balance + cashStat.sw_service_fees;
                details.ticketsEscrow = yield AccountingService.Common.ticketsEscrow(eventId);
            } else {
                checkStat = yield AccountingService.Common.merchantStat(eventId, CHECK_PAYMENT, paymentFor);
                details.dueToEvent = creditStat.balance + achStat.balance + checkStat.sw_service_fees;
            }

            let manualPayoutsStat = yield AccountingService.Common.manualPayoutsStat(eventId, paymentFor);
            details.manualPayouts = manualPayoutsStat.notCanceledSum;

            let lostDisputesStat = yield AccountingService.Common.lostDisputesStat(eventId, paymentFor);
            details.lostDisputes = lostDisputesStat.entry_fee_amount;

            details.refundAdjustments = yield AccountingService.Common.refundAdjustments(eventId, paymentFor);

            details.totalAvailableForTickets = (paymentFor === AccountingService.Common.TICKETS_PAYMENT_FOR_TYPE
                                            ? details.dueToEvent + details.ticketsEscrow : 0);

            details.stillAvailableToPauout = details.dueToEvent - details.manualPayouts
                                    + (paymentFor === AccountingService.Common.TICKETS_PAYMENT_FOR_TYPE
                                            ? details.escrowAccBeingHeld : 0);

            // TODO (later): compare this value with ⅀ purchase.net_profit {card,ach} - ⅀ SW Fees for non-Stripe payments
            details.stripeAcctTotalForThisEvent = details.dueToEvent + details.refundAdjustments 
                                    + details.lostDisputes;

            return details;
        })

    }
}

module.exports = new StripeDetailsService();
