'use strict';

/**
 * Descriptions of formulas - 
 *     https://docs.google.com/spreadsheets/d/1YEF7TFk7SwDzbqIzJnTWKyBJJvvNhahsUy3ctxIiv84/edit
 */

const utils = require('../../lib/swUtils');


// we are taking into account these types of payment status
const PAYMENT_STATUS = {
    paid   : 22,
    pending: 24
};

// we are taking into account these types of stripe transfers
const STRIPE_TRANSFER_TYPES = [
    'pay_out',
    'internal'
];

const PAYOUT_TRANSFER_TYPE      = 'pay_out';

const TEAMS_PAYMENT_FOR_TYPE    = 'teams';
const TICKETS_PAYMENT_FOR_TYPE  = 'tickets';
const BOOTHS_PAYMENT_FOR_TYPE   = 'booths';

const PAYMENT_FOR = [
    TEAMS_PAYMENT_FOR_TYPE,
    TICKETS_PAYMENT_FOR_TYPE,
    BOOTHS_PAYMENT_FOR_TYPE
];

const DATE_FORMAT = 'YYYY-MM-DD';


class AccountingCommon {
    constructor() {}

    get ACH () {
        return 'ach';
    }

    get CARD () {
        return 'card';
    }

    get CHECK () {
        return 'check';
    }

    get CASH () {
        return 'cash';
    }

    /* NOTE: what should we do with "free" type? */

    get TEAMS_PAYMENT_FOR_TYPE () {
        return TEAMS_PAYMENT_FOR_TYPE;
    }

    get TICKETS_PAYMENT_FOR_TYPE () {
        return TICKETS_PAYMENT_FOR_TYPE;
    }

    get BOOTHS_PAYMENT_FOR_TYPE () {
        return BOOTHS_PAYMENT_FOR_TYPE;
    }

    get AVAILABLE_FOR_REFUND_PAYMENT_TYPES () {
        return this.STRIPE_PAYMENT_TYPES;
    }

    get PAYOUT_TRANSFER_TYPE () {
        return PAYOUT_TRANSFER_TYPE;
    }

    get PAYMENT_TYPES() { 
        return [this.ACH, this.CARD, this.CHECK, this.CASH]; 
    }

    get PAYMENT_FOR() {
        return PAYMENT_FOR;
    }

    get PAYMENT_STATUS_NAMES() { 
        return Object.keys(PAYMENT_STATUS);
    }

    get PAYMENT_STATUS_CODES() { 
        return Object.keys(PAYMENT_STATUS).map(key => PAYMENT_STATUS[key]) 
    }

    get STRIPE_TRANSFER_TYPES() { 
        return STRIPE_TRANSFER_TYPES;
    }

    get STRIPE_PAYMENT_TYPES() {
        return [this.ACH, this.CARD]; 
    }

    isAllowedPaymentType(paymentType) {
        return this.PAYMENT_TYPES.indexOf(paymentType) !== -1;
    }

    isAllowedPaymentFor(paymentFor) {
        return this.PAYMENT_FOR.indexOf(paymentFor) !== -1;
    }

    /**
     * Can be used as a data source for:
     * 1. SPORTWRENCH Team/Ticketing/Exhibitor Statistics
     * 2. STRIPE Team/Ticketing/Exhibitor Details : Entry Fee Totals Due To Event
     * @param {number} eventId 
     * @param {string: 'card', 'ach', 'check'} paymentType 
     * @param {string} paymentFor 
     * @returns recordset for each type of payment with columns:
     * 1. 
     *      FOR TEAMS  : Number of merchant transactions (number_of_merchant_transactions)
     *      FOR BOOTHS : Number of booths (number_of_booths)
     *      FOR TICKETS: Number of tickets (number_of_tickets) 
     * 2. Number of transaction (number_of_transactions)
     * 3. Entry Fee Amount (entry_fee_amount)
     * 4. Stripe Transaction Fee (stripe_transaction_fees)
     * 5. SW Service Fees (sw_service_fees)
     * 6. Bought Items Quantity (items_qty)
     * 7. Balance (balance)
     * 
     * Note:
     * stripe_transaction_fees and sw_service_fees returned as negative numbers
     * to be used in formulas with + (addition)
     */
    merchantStat (eventId, paymentType, paymentFor, dateAfter, dateBefore) {
        if (!this.isAllowedPaymentType(paymentType)) {
            return Promise.reject('Not alowed payment type.')
        }

        if (!this.isAllowedPaymentFor(paymentFor)) {
            return Promise.reject('Not alowed payment for.')
        }

        if (dateAfter && !utils.isDateValid(dateAfter, DATE_FORMAT)) {
            return Promise.reject({ validation: `Invalid date ${dateAfter}` });
        }

        if (dateBefore && !utils.isDateValid(dateBefore, DATE_FORMAT)) {
            return Promise.reject({ validation: `Invalid date ${dateBefore}` });
        }

        let sql;
        let params;

        if (paymentFor === this.TEAMS_PAYMENT_FOR_TYPE) {
            sql = `
                WITH "stripe_fees" AS (
                    SELECT -COALESCE(SUM(p."stripe_fee"), 0) "transaction_fee"
                    FROM "purchase" p 
                    WHERE p.event_id = $1
                        AND p.payment_for = $3
                        AND (p."status" = 'paid' OR p.dispute_status = 'lost') 
                        AND p.type = $2
                ), 
                "entry_fee" AS (
                    SELECT teams_entry_sw_fee "fee" FROM event e WHERE e.event_id = $1
                )
                SELECT 
                    COUNT(rt.roster_team_id) 
                        FILTER (
                            WHERE rt.deleted IS NULL AND rt.status_paid = ANY ($4)
                        ) "number_of_merchant_transactions",

                    COUNT(DISTINCT p.purchase_id) 
                        FILTER (
                            WHERE 
                                (p."status" = 'paid' OR p.dispute_status = 'lost')
                        ) "number_of_transactions",

                    COALESCE (
                        SUM(pt.amount + COALESCE(pt.surcharge, 0)) 
                        FILTER (
                            WHERE 
                                ((p."status" = 'paid' AND pt.canceled IS NULL) OR p.dispute_status = 'lost')
                        ), 
                        0
                    ) "entry_fee_amount",

                    (
                        SELECT "transaction_fee" FROM "stripe_fees" 
                    ) "stripe_transaction_fees",

                    COALESCE(
                        -(COUNT(pt.*)
                            FILTER (
                                WHERE ((p."status" = 'paid' AND pt.canceled IS NULL) OR p.dispute_status = 'lost')
                                )) * (SELECT fee FROM entry_fee),
                            0
                    ) "sw_service_fees",

                    (
                        COALESCE(
                            COUNT(pt.*)
                            FILTER (
                                WHERE ((p."status" = 'paid' AND pt.canceled IS NULL) OR p.dispute_status = 'lost')
                            ), 
                            0
                        ) 
                    ) "items_qty"

                FROM purchase p 
                JOIN purchase_team pt 
                    ON p.purchase_id = pt.purchase_id 
                    AND p.event_id = pt.event_id
                JOIN roster_team rt 
                    ON pt.roster_team_id = rt.roster_team_id 
                    AND pt.event_id = rt.event_id
                WHERE p.event_id = $1 
                    AND p.type = $2 
                    AND p.payment_for = $3
            `;
            params = [
                /* $1 */ eventId, 
                /* $2 */ paymentType, 
                /* $3 */ paymentFor, 
                /* $4 */ this.PAYMENT_STATUS_CODES
            ];
        }

        if(paymentFor === this.TICKETS_PAYMENT_FOR_TYPE) {
            sql = `
                SELECT
                    COALESCE (
                        SUM(ptt.quantity) FILTER(WHERE ptt.canceled IS NULL), 
                        0
                    ) "number_of_tickets",
                    COUNT(
                        DISTINCT p.purchase_id) 
                        FILTER (
                            WHERE p.canceled_date IS NULL 
                            AND p.status = ANY ($4)
                        ) "number_of_transactions",
                    COALESCE (
                        SUM(ptt.amount) FILTER (WHERE ptt.canceled IS NULL), 
                        0
                    ) "entry_fee_amount",
                    COALESCE (
                        -SUM(p.stripe_fee) FILTER (WHERE p.canceled_date IS NULL), 
                        0
                    ) "stripe_transaction_fees",
                    COALESCE (
                        -SUM(ptt.ticket_fee * ptt.quantity) 
                            FILTER (WHERE ptt.canceled IS NULL), 
                        0
                    ) "sw_service_fees"
                FROM purchase p 
                JOIN purchase_ticket ptt 
                    ON p.purchase_id = ptt.purchase_id
                WHERE p.event_id = $1 
                    AND p.type = $2 
                    AND p.payment_for = $3
            `;
            params = [
                /* $1 */ eventId, 
                /* $2 */ paymentType, 
                /* $3 */ paymentFor, 
                /* $4 */ this.PAYMENT_STATUS_NAMES
            ];
        }

        if (paymentFor === this.BOOTHS_PAYMENT_FOR_TYPE) {
            sql = `
                SELECT 
                    COALESCE (SUM(pb.quantity), 0) as number_of_booths,
                    COUNT(DISTINCT p.purchase_id) as number_of_transactions,
                    COALESCE (SUM(p.amount), 0) as entry_fee_amount,
                    COALESCE (-SUM(p.stripe_fee), 0) as stripe_transaction_fees,
                    0 as sw_service_fees
                FROM purchase p 
                JOIN purchase_booth pb 
                    ON p.purchase_id = pb.purchase_id
                WHERE p.event_id = $1 
                    AND p.type = $2 
                    AND p.payment_for = $3 
                    AND p.canceled_date IS NULL
                    AND p.status = ANY ($4)
            `;
            params = [
                /* $1 */ eventId, 
                /* $2 */ paymentType, 
                /* $3 */ paymentFor, 
                /* $4 */ this.PAYMENT_STATUS_NAMES
            ]
        }

        if (dateAfter) {
            params.push(dateAfter);

            sql += 
                `
                AND p."created"::DATE >= $${params.length}`;
        }

        if (dateBefore) {
            params.push(dateBefore);

            sql += 
                `
                AND p."created"::DATE <= $${params.length}`;
        }

        return Db.query(sql, params)
        .then(result => {
            let details = utils.propsToNumber(result.rows[0]);

            details.balance = (
                details.entry_fee_amount + 
                                        details.stripe_transaction_fees + details.sw_service_fees);

            return details;
        });
    }

    /**
     * Can be used as a data source for:
     * 1. SPORTWRENCH Team/Ticketing/Exhibitor Payouts Details
     * 2. STRIPE Team/Ticketing/Exhibitor Details : Manual Payouts
     * @param {number} eventId 
     * @param {string} paymentFor
     * @param {array} transferTypes
     * @returns recordset with columns:
     * 1. Date (date_sent)
     * 2. Description (description)
     * 3. Amount (amount)
     * 4. Canceled: FALSE OR TRUE
     * 5. Date canceled (date_canceled)
     */
    manualPayoutsStat(eventId, paymentFor, transferTypes = this.STRIPE_TRANSFER_TYPES) {

        if (!this.isAllowedPaymentFor(paymentFor)) {
            return Promise.reject('Not alowed payment for.')
        }

        return Db.query(`
            SELECT
              TO_CHAR(st.date_sent, 'MM/DD/YYYY') date_sent,
              st.description,
              st.date_canceled,
              st.amount :: NUMERIC,
              (st.date_canceled IS NOT NULL) "canceled"
            FROM stripe_transfer st
            WHERE st.event_id = $1
                  AND st.payment_for = $2
                  AND st.transfer_type = ANY ($3)
            `, [eventId, paymentFor, transferTypes])
            .then(result => {
                let details = { rows: result.rows } || {};
                if (_.isEmpty(details)) {
                    details.notCanceledSum = 0;
                    details.canceledSum = 0;
                    return details;
                } else {
                    details.notCanceledSum = details.rows.filter(r => !r.canceled).reduce((acc, cur) => acc + +cur.amount, 0);
                    details.canceledSum = details.rows.filter(r => r.canceled).reduce((acc, cur) => acc + +cur.amount, 0);
                    return details;
                }
            });
    }


    /**
     * Can be used as a data source for:
     * 1. SPORTWRENCH Team/Ticketing/Exhibitor Statistics: Adjustments by SW: Lost Disputes with Stripe
     * 2. STRIPE Team/Ticketing/Exhibitor Details : Lost Disputes
     * @param {number} eventId 
     * @param {string} paymentFor 
     * @returns recordset with columns:
     * 1. Number of lost disputes (number_of_lost_disputes)
     * 2. Entry Fee Amount (entry_fee_amount)
     * 3. Stripe Penalty Fee (stripe_penalty_fee)
     * 4. Balance (balance)
     * 
     * Note:
     * entry_fee_amount, stripe_penalty_fee, balance returned as negative numbers
     * to be used in formulas with + (addition)
     */
    lostDisputesStat(eventId, paymentFor) {

        if (!this.isAllowedPaymentFor(paymentFor)) {
            return Promise.reject('Not alowed payment for.')
        }

        let returnedSWServiceFeesSubquery;

        /**
         * NOTE: these are the same formulas as for "sw_service_fees" in #merchantStat()
         */
        
        /**
         * NOTE: there is a small chance to get payment with lost dispute and refund. 
         * I mean, refund was earlier that occurance of the dispute. 
         * In this case we can not tell, whether item (team/ticket/booth) was refunded or canceled 
         * due to lost dispute. 
         * For now (27.11.2017), let it leave as is. 
         * As a workaround, we can set "canceled_date" to "purchase" with lost dispute and 
         * "canceled" to it's items ("purchase_teams/tickets/booths"). Then, in sub-queries below 
         * we can check the dates are equal.
         */
        if (paymentFor === this.TEAMS_PAYMENT_FOR_TYPE) {
            returnedSWServiceFeesSubquery = 
            `SELECT 
                SUM(pt."sw_fee")
             FROM "purchase_team" pt 
             WHERE p."purchase_id" = pt."purchase_id" 
                AND p."event_id" = pt."event_id"`;
        } else if (paymentFor === this.TICKETS_PAYMENT_FOR_TYPE) {
            returnedSWServiceFeesSubquery = 
            `SELECT
                SUM(ptt."ticket_fee" * ptt."quantity")
             FROM "purchase_ticket" ptt 
             WHERE p."purchase_id" = ptt."purchase_id"`;
        } else if (paymentFor === this.BOOTHS_PAYMENT_FOR_TYPE) {
            returnedSWServiceFeesSubquery = '0';
        }

        return Db.query(
            `SELECT  
                COUNT(p.*) "number_of_lost_disputes",
                COALESCE(-SUM(p.amount), 0) "entry_fee_amount",
                -(COUNT(p.*) * $3) "stripe_penalty_fee",
                -(COUNT(p.*) * $3) "balance", (
                    SUM(
                        (${returnedSWServiceFeesSubquery})
                    )
                ) "returned_sw_service_fees",
                COALESCE(SUM(p."stripe_fee"), 0) "returned_stripe_fee"
            FROM purchase p
            WHERE p.event_id = $1 
                AND p.payment_for = $2 
                AND p.dispute_status = 'lost'`, 
            [eventId, paymentFor, StripeService.DISPUTE_FEE]
        ).then(result => {
            let details = utils.propsToNumber(result.rows[0]);

            return details;
        });
    }

    /**
     * Can be used as a data source for:
     * 1. STRIPE Team/Ticketing/Exhibitor Details : Refund Adjustments
     * @param {number} eventId 
     * @param {string} paymentFor 
     * @returns recordset with column:
     * 1. Refund Adjustments (refund_adjustments)
     */
    refundAdjustments(eventId, paymentFor) {

        if (!this.isAllowedPaymentFor(paymentFor)) {
            return Promise.reject('Not alowed payment for.')
        }

        let stripe_percent;

        if (paymentFor == this.TEAMS_PAYMENT_FOR_TYPE || paymentFor == this.BOOTHS_PAYMENT_FOR_TYPE) {
            stripe_percent = 'e.stripe_teams_percent';
        } else {
            stripe_percent = 'e.stripe_tickets_percent';
        }

        return Db.query(
            `SELECT 
                COALESCE(SUM(p.amount_refunded * ${stripe_percent} / 100 + 0.3) , 0) "refund_adjustments"
             FROM purchase p 
             JOIN event e on p.event_id = e.event_id
             WHERE p.date_refunded IS NOT NULL 
                AND p.status = ANY($3)
                AND p.event_id= $1 
                AND p.payment_for = $2`,
             [eventId, paymentFor, this.PAYMENT_STATUS_NAMES]
            ).then(result => {
                return +result.rows[0].refund_adjustments;
            });
    }

    // !!!! this must be checked.
    // should be equal to SUM(purchase_ticket.ticket_fee * quantity) 
    //            - (purchase.additional_fee_amount + purchase.collected_sw_fee) <-card pmt only
    ticketsEscrow(eventId) {
        return Db.query(`
            SELECT COALESCE(tickets_sw_balance, 0) as escrow_being_held
            FROM event 
            WHERE event_id = $1
            `,[eventId])
            .then(result => {
                return +result.rows[0].escrow_being_held;
            });
    }

}

module.exports = new AccountingCommon();
