var pg 				= require('pg'),
	EventEmitter 	= require('events').EventEmitter

pg.defaults.poolSize = 20;

module.exports = Db;

function Db (config) {
	this.connection = __initConnection(config);
}

function __initConnection () {

}

function __normalizeSQL (a, b, c) {
	return c;
}

function __retrieveClient () {

}

function __execSQL (client, text, params, closeClient) {
	client.query(sql.queryText, sql.queryParams, function (err, result) {

	})
}

Db.prototype.query = function (a, b, c) {
	var connection 	= this.connection,
		sql 		= __normalizeSQL(a, b, c);

	__retrieveClient()
	.then(function () {

	})

	return new Promise(function (reject, resolve) {
		pg.connect(connection, function (err, client, done) {
			if(err) {
				reject(err)
			} else {
				return __execSQL(client, sql.queryText, sql.params, done)
			}
		})
	})
}

Db.prototype.begin = function () {

}
