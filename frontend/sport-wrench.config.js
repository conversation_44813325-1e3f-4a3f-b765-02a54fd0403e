angular.module('SportWrench')
.run(function($rootScope, $templateCache, $state, userService, $location, $uibModalStack,
    lazyLoadService, previousStateService, $log, eventStrorage, toastr, APP_ROUTES, Sentry, $http, NotificationService, $injector) {

    var EO_ROUTES_PREFIX = APP_ROUTES.EO.PARENT + '.';
    let FIRST_VISIT = true;
    let lastShownNotificationId = null;

    // Check notifications on initial load
    __checkUnreadNotifications(userService, NotificationService, toastr, lastShownNotificationId)
        .then(id => {
        lastShownNotificationId = id
        });

    $rootScope.$on('$stateChangeSuccess', function (ev, to, toParams, from, fromParams) {
        previousStateService.set(from.name, fromParams);
        // save EO last visited state
        if(to.name.indexOf(EO_ROUTES_PREFIX) >= 0) {
            eventStrorage.setLastVisited(to.name, toParams);
        }

        // Stop ACL monitoring when leaving event context
        if (from.name.indexOf(EO_ROUTES_PREFIX) >= 0 && to.name.indexOf(EO_ROUTES_PREFIX) < 0) {
            const EventACLService = $injector.get('EventACLService');
            EventACLService.stopAclRefreshMonitoring();
        }

        __checkUnreadNotifications(userService, NotificationService, toastr, lastShownNotificationId)
            .then(id => {
            lastShownNotificationId = id
            });

        gtag('event', 'page_view', {
            page_title: to.name,
            page_location: $location.absUrl()
        });
    });

    $rootScope.$on('$stateChangeError', function (event, toState, toParams, fromState, fromParams, errors) {
        if(errors) {
            if(typeof errors === 'string') {
                toastr.warning(errors);
                throw new Error(errors);
            } else {
                throw errors;
            }
        }
    });

    $rootScope.$on('$stateChangeStart', function() {
        if (FIRST_VISIT) {
            FIRST_VISIT = false;

            userService.getUserInfo();
        }

        var top = $uibModalStack.getTop();

        if (top) {
            $uibModalStack.dismiss(top.key);
        }
    });

    $rootScope.$on('$locationChangeSuccess',function() {
    });

    __configSentry(userService, Sentry);

    __fetchCsrfToken($http);
})

.config(function(
    $httpProvider, $uiViewScrollProvider, cfpLoadingBarProvider, APP_ROUTES,
    $compileProvider, $provide, toastrConfig, INTERNAL_ERROR_MSG, $injector,
    uiSelectConfig
) {

    __decorateErrors($provide);

    // hack for lazy loaded modules config
    $provide.value('$providerInjector', $injector);

    $compileProvider.debugInfoEnabled(false);

    cfpLoadingBarProvider.latencyThreshold = 500;

    $uiViewScrollProvider.useAnchorScroll();

    $httpProvider.interceptors.push('UIReloader', 'ResponseErrorInterceptor');

    var HEADERS = {
        contentType: {
            name    : 'Content-Type',
            value   : 'application/json;charset=utf-8'
        }
    };

    $httpProvider.defaults.headers.post[HEADERS.contentType.name]   = HEADERS.contentType.value;
    $httpProvider.defaults.headers.put[HEADERS.contentType.name]    = HEADERS.contentType.value;
    $httpProvider.defaults.headers.get = {
        'Cache-Control'     : 'no-cache',
        'Pragma'            : 'no-cache'
    };

    uiSelectConfig.theme = 'bootstrap';

    __decorateAccordion($provide);

    __configToastr(toastrConfig);

    __decorateLocale($provide);
});

function __fetchCsrfToken($http) {
    const methods = ['post', 'put', 'patch', 'delete'];
    $http.get('/api/csrfToken')
        .success(function ({_csrf}) {
            for(const method of methods) {
                if(!$http.defaults.headers[method]) {
                    // DELETE method doesn't exist in $http.defaults.headers
                    // https://code.angularjs.org/1.5.6/docs/api/ng/provider/$httpProvider
                    $http.defaults.headers[method] = {
                        'X-CSRF-Token': null
                    };
                }

                $http.defaults.headers[method]['X-CSRF-Token'] = _csrf;
            }
        });
}

function __decorateErrors($provide) {
    $provide.decorator("$exceptionHandler", ["$delegate", "$window", function($delegate, $window) {
        return function (error, cause) {
            const exception = error.error || error.message || error.originalError || error;

            if ($window.Sentry) {
                $window.Sentry.captureException(exception);
            }

            // In *non-production* environments you may still want the error sent to the console.
            // You can delegate to the original handler (console.error) if you'd like.
            // Warning, this can cause double tracking of errors, so do not use in production.
            if(/localhost|dev\./i.test($window.location.host)) {
                $delegate(exception);
            }
        };
    }]);
}

function __decorateAccordion ($provide) {
    $provide.decorator('uibAccordionGroupDirective', ['$delegate', '$timeout', '$stateParams',
    function ($delegate, $timeout, $stateParams) {
        var directive = $delegate[0], origLink = directive.link;

        var newLink = function ($scope, $element, $attrs, ctrl) {
            var __slided = true, animationInProgress = false,
                __slideToTop = function () {
                    if(animationInProgress) return;
                    animationInProgress = true;
                    $timeout(function () {
                        angular.element('html,body').animate({
                            scrollTop: $($element).offset().top
                        }).promise().done(function complete () {
                            animationInProgress = false;
                        });
                        __slided = true
                    }, 50, false);
                };

            $scope.$watch('isOpen', function (value) {
                $element.toggleClass($scope.openClass, !!value);
                if (value) {
                  ctrl.closeOthers($scope);
                  if($attrs.sliding && (!__slided || $stateParams.accord)) __slideToTop();
                } else {
                    __slided = false
                }
            });
            origLink.apply(this, arguments);
            return;
        };

        directive.compile = function () {
          return newLink;
        };

        return $delegate;
    }]);
}

function __decorateLocale ($provide) {

    /**
     * These decorations are made to provide an ability to customize negative currency-numbers
     * formatting:
     *
     * Some components require to display currencies like -$NNN, another ones - like ($NNN).
     * So, based on the AngularJS implementation of the "currency" filter we have such changes:
     *
     * 1. Remove auto "minus" sign substitution (negPre property)
     * NOTE: the currency filter is strongly coupled with the $locale
     *
     * 2. Customize the "currency" filter: add "minus" sign or place formatted number
     * inside parentheses depending on the arguments
     *
     */

    $provide.decorator('$locale', ['$delegate', function ($delegate) {
      if($delegate.id === 'en-us') {
        /* https://github.com/angular/angular.js/blob/master/src/ngLocale/angular-locale_en-us.js#L132 */
        $delegate.NUMBER_FORMATS.PATTERNS[1].negPre = '\u00A4';
        $delegate.NUMBER_FORMATS.PATTERNS[1].negSuf = '';
      }
      return $delegate;
    }]);

    /* https://github.com/angular/angular.js/blob/master/src/ng/filter/filters.js#L62 */
    $provide.decorator('currencyFilter', ['$delegate', function ($delegate) {
        return function (amount, currencySymbol, fractionSize, useParentheses) {
            let currencyRes = $delegate(amount, currencySymbol, fractionSize);

            if (amount == null || (amount >= 0)) {
                return currencyRes;
            } else {
                return useParentheses ? `(${currencyRes})` :`-${currencyRes}`;
            }
        };
    }]);
}

function __configToastr(toastrConfig) {
    angular.extend(toastrConfig, {
        autoDismiss: false,
        containerId: 'toast-container',
        maxOpened: 0,
        newestOnTop: true,
        positionClass: 'toast-top-right',
        preventDuplicates: false,
        preventOpenDuplicates: false,
        target: 'body',
        timeOut: 7000
    });
}

function __configSentry (userService, Sentry) {
    var userEmail = userService.getEmail() || '';

    if (userService.isLoggedIn() && !userEmail) {
        userService.getUser(function (user) {

            Sentry.setUser({ email: user.email });

            userService.setEmail(user.email);
        });
    } else {
        Sentry.setUser({ email: userEmail });
    }
}

async function __checkUnreadNotifications(userService, NotificationService, toastr, lastShownNotificationId) {
    if (!userService.isLoggedIn()) {
        return lastShownNotificationId;
    }

    try {
        const notification = await NotificationService.getLatestUnreadNotification();

        if (notification && notification.user_notification_id !== lastShownNotificationId) {
            const notificationId = notification.user_notification_id;

            toastr[notification.message_type](notification.message_text, null, {
                closeButton: true,
                timeOut: 0,
                extendedTimeOut: 0,
                tapToDismiss: false,
                onHidden: () => {
                    NotificationService.markAsRead(notification.user_notification_id);
                    lastShownNotificationId = null;
                }
            });

            return notificationId;
        }
        return lastShownNotificationId;
    } catch (error) {
        return lastShownNotificationId;
    }
}


