<button class="btn btn-primary row-space" ng-click="to_create_menu();">Create Exhibitor</button>
<button class="btn btn-success" ng-click="xlsx_export();">Export</button>
<table class="table table-condensed" sticky-header>
    <thead>
        <tr>
            <th></th>
            <th>
                <a data-ng-click="orderData('first');" href="">First</a>
                <reverse-arrow reverse="reverseSort" show="{{orderColumn == 'first'}}"></reverse-arrow>               
            </th>
            <th>
                <a data-ng-click="orderData('last');" href="">Last</a>
                <reverse-arrow reverse="reverseSort" show="{{orderColumn == 'last'}}"></reverse-arrow>                
            </th>
            <th>
                <a data-ng-click="orderData('company_name');" href="">Company name</a>
                <reverse-arrow reverse="reverseSort" show="{{orderColumn == 'company_name'}}"></reverse-arrow>   
            </th>
            <th>
                <a data-ng-click="orderData('company_description');" href="">Description</a>
                <reverse-arrow reverse="reverseSort" show="{{orderColumn == 'company_description'}}"></reverse-arrow>
            </th>
            <th>Exhibitor</th>
            <th>Sponsor</th>
            <th>Non profit</th>
            <th>Other</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat="sponsor in sponsors | orderBy:orderColumn:reverseSort">
            <td>
                <a ui-state="updateState" ui-state-params="{ exhibitor: sponsor.sponsor_id }">
                    <i class="fa fa-pencil-square-o big-icon pull-left edit-icon" ng-click="to_update_menu()"></i>
                </a>
            </td>
            <td ng-click="to_info_menu(sponsor.sponsor_id);">        
                {{sponsor.first}}
            </td>
            <td ng-click="to_info_menu(sponsor.sponsor_id);">{{sponsor.last}}</td>
            <td ng-click="to_info_menu(sponsor.sponsor_id);">{{sponsor.company_name}}</td>
            <td>{{sponsor.company_description}}</td>
            <td>
                <input type="checkbox" ng-model="sponsor.is_exhibitor" ng-disabled="true">
            </td>
            <td>
                <input type="checkbox" ng-model="sponsor.is_sponsor" ng-disabled="true">
            </td>
            <td>
                <input type="checkbox" ng-model="sponsor.is_non_profit" ng-disabled="true">
            </td>
            <td>
                <input type="checkbox" ng-model="sponsor.is_other" ng-disabled="true">
            </td>
        </tr>
    </tbody>
</table>
