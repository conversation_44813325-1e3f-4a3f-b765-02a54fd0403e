<table class="table table-condensed sales-event-list">
    <thead>
    <tr>
        <th>Event Name</th>
        <th>Region</th>
        <th>State</th>
        <th>Date Start</th>
        <th></th>
    </tr>
    </thead>
    <tbody ng-if="!utils.loading">
    <tr ng-repeat-start="event in events track by $index" class="bg-info font-bold" ng-if="showYear(event, $index)">
        <td colspan="5">{{event.season}} Events:</td>
    </tr>
    <tr ng-repeat-end>
        <td>
            <a ui-state="states.event_exhibitors"
               ui-state-params="{ event: event.event_id }"
            >
                {{event.name}}
            </a>
        </td>
        <td>{{event.region}}</td>
        <td>{{event.state}}</td>
        <td>{{event.date_start_form}}</td>
        <td class="actions-column">
            <button
                class="btn btn-primary btn-xs"
                ng-click="goToInvoices(event)"
            >
                Invoices
            </button>
        </td>
    </tr>
    </tbody>
    <tbody ng-if="utils.loading">
    <tr>
        <td colspan="5" class="text-center"><i class="fa fa-spinner fa-pulse fa-2x"></i></td>
    </tr>
    </tbody>
</table>
