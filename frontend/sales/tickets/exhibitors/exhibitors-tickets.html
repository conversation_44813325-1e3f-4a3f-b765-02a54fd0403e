<div class="row row-space tickets-payments-bar">
    <div class="col-sm-12 form-inline">
        <div ng-if="showFreeTicketButton()">
            <button ng-click="openGenerateFreeTicketModal()" class="btn btn-primary pull-right">Generate exhibitors ticket</button>
        </div>
        <exhibitors-tickets-filters
            filters-changed="data.filtersChanged(filters, search, updateWithoutReload)"
        ></exhibitors-tickets-filters>
    </div>
</div>
<div>
    <div class="ov-x-auto spacer-sm-b" editable-grid one-active="true">
        <table class="table table-condensed tickets-payments-table">
            <thead>
                <tr>
                    <th ng-click="data.sort('code')" class="code-col" ng-if="data.showColumn('ticket_barcode')">
                        Ticket Code
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'code'}}"></reverse-arrow>
                    </th>                
                    <th class="ticket-count-col text-grey nowrap" ng-repeat="t in data.ticket_types" ng-bind="t.label"></th>
                    <th ng-click="data.sort('amount')" class="total-col" ng-if="data.showColumn('amount')">
                        Total Price
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'amount'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('purchased')" class="purchased-col">
                        Purchased
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'purchased'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('first')" class="firstname-col">
                        First
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'first'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('last')" class="lastname-col">
                        Last
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'last'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('birthdate')" ng-if="data.showColumn('ages')">
                        Ages
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'birthdate'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('email')" class="email-col">
                        Email
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'email'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('zip')" class="zip-col">
                        Zip
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'zip'}}"></reverse-arrow>
                    </th>
                    <th ng-if="data.showColumn('type')">Type</th>
                    <th ng-click="data.sort('status')" class="status-col" ng-if="data.showColumn('status')">
                        Status
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'status'}}"></reverse-arrow>
                    </th>
                    <th ng-click="data.sort('scanned')" class="scanned-col" ng-if="data.showColumn('scanned_at') || data.showColumn('scanner_id') || data.showColumn('scanner_location') ">
                        Scanned
                        <reverse-arrow reverse="data.filters.revert" show="{{data.filters.sort === 'scanned'}}"></reverse-arrow>
                    </th>
                </tr>            
            </thead>
            <tbody>
                <tr ng-repeat="p in data.payments" 
                    ng-click="data.ticketModal(p)"
                    ng-class="data.rowClass(p)"
                    >

                    <td ng-show="data.showColumn('ticket_barcode')">
                        <a href="" ng-bind="p.ticket_barcode"></a>
                    </td>

                    <td sw-binder="{{::p.tickets_count}}" ng-class="{ 'striked-out': p.status === 'canceled' }"></td>
        
                    <td ng-bind="p.amount" class="text-right" ng-if="data.showColumn('amount')"></td>
                    <td ng-bind="::p.purchased"></td>

                    <td><a href="" ng-bind="p.buyer_first ==='' ? '-' : p.buyer_first"></a></td>
                    <td><a href="" ng-bind="p.buyer_last === '' ? '-' : p.buyer_last"></a></td>

                    <!-- Start Ages -->
                    <td ng-if="data.showColumn('ages')" ng-click="$event.stopPropagation()">
                        <p ng-if="p.ages.length === 0"> - </p>
                        <span class="pointer"
                            ng-repeat="age in p.ages"
                            uib-popover-html="data.agesHTML(age)"
                            popover-trigger="mouseenter"
                            popover-append-to-body="false"
                            popover-enable="age.age !== 0"
                            popover-title="Participant Age: {{ age.age }}"
                        >
                        {{ age.age }}{{$last ? '' : ', '}}
                        </span>
                    </td>
                    <!-- End Ages -->

                    <td ng-click="$event.stopPropagation()">
                        <a href="mailto:{{p.email}}">{{p.email}}</a></br>
                        <a href="tel:{{p.phone}}">{{p.phone | tel}}</a>
                    </td>
                    <td ng-bind="::p.zip"></td>
                    <td ng-bind="::p.type" ng-if="data.showColumn('type')"></td>
                    <td class="text-center" ng-if="data.showColumn('status')">
                        <payment-dispute-status-label ng-if="p.dispute_status !== null" payment="p" transform-label="transform-label"></payment-dispute-status-label>
                        <payment-status-label ng-if="p.dispute_status === null" payment="p"></payment-status-label>
                    </td>
                    <td class="pointer" ng-if="data.showColumn('scanned_at') || data.showColumn('scanner_id') || data.showColumn('scanner_location') ">
                        <div class="text-grey" ng-if="!p.scanned_at && !scanner_id && !p.scanner_location">Not scanned</div>
                        <div ng-bind="p.scanned_at"></div>
                        <div ng-bind="p.scanner_id"></div>
                        <div ng-bind="p.scanner_location"></div>
                    </td>
                </tr>
                <tr ng-if="!data.payments.length">
                    <td colspan="{{10 + data.ticket_types.length}}" class="text-center"><i class="fa fa-exclamation-circle"></i> No tickets found</td>
                </tr>
            </tbody>
        </table>
    </div>
    <pagination
        page="data.filters.page"
        current="data.payments.length"
        limit="data.filters.limit"
        total="data.utils.total_rows"
        change-page="data.changePage(page)"
    ></pagination>
</div>
