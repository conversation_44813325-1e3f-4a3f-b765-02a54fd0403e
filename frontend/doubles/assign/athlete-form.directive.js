angular.module('SportWrench').directive('doublesPlayerForm', function () {
    return {
        restrict: 'E',
        scope: {
            player: '=',
            errors: '=',
            pref: '@',
            form: '=',
            teamType: '='
        },
        templateUrl: 'doubles/assign/athlete-form.html',
        controller: function ($scope, $log) {
            $scope.utils = {
                email_reg_exp: /\S+@\S+/,
                usav_reg_exp: /^[a-zA-Z]{2}[0-9]{7,7}[a-zA-Z]{2,}\d{2,}$|^[0-9]{7,7}$/
            };

            $scope.showRequiredMsg = function (field) {
                return ($scope.form[$scope.pref + field].$error.required && $scope.form[$scope.pref + field].$touched);
            }

            $scope.fieldInvalid = function (field) {
                return ($scope.form[$scope.pref + field].$invalid && $scope.form[$scope.pref + field].$dirty);
            }

            $scope.memberIdFieldName = function () {
                return (($scope.teamType === 'junior')?'USAV':'Membership') + ' Number'
            }

            $scope.memberIdPattern = function () {
                return ($scope.teamType === 'junior')?$scope.utils.usav_reg_exp:null
            }
        }
    }
})
