angular.module('SportWrench').directive('blockedEventsList', blockedEventsList);

function blockedEventsList () {
	var DEFAULT_MSG = 'Changes will not be applied to the following event(s) due to locked team roster:';
	return {
		restrict 	: 'E',
		scope 		: {
			txt 	: '@?',
			events 	: "="
		},
		templateUrl : 'common/blocked-events/events.html',
		link: function (scope) {
			scope.msg = DEFAULT_MSG || scope.txt;
		}
	};
}
