angular.module('SportWrench').service('EventACLService', EventACLService);

function EventACLService(EVENT_OPERATIONS) {
    this.acl                = null;
    this.EVENT_OPERATIONS   = EVENT_OPERATIONS;
}

EventACLService.prototype.isOperationAllowed = function (eventUserAcl, operation) {
    return eventUserAcl && eventUserAcl[operation];
};

EventACLService.prototype.setUserAcl = function (acl) {
    this.acl = acl;
};

EventACLService.prototype.getUserAcl = function () {
    return this.acl;
};

EventACLService.prototype.userHasOnlyEventEditPermissions = function (userPermissions) {
    if(!userPermissions) {
        return true;
    }

    const editEventPermissions = [
        this.EVENT_OPERATIONS.EDIT_EVENT,
        this.EVENT_OPERATIONS.EDIT_EVENT_GENERAL,
        this.EVENT_OPERATIONS.EDIT_EVENT_LOCATION,
        this.EVENT_OPERATIONS.EDIT_EVENT_USERS,
        this.EVENT_OPERATIONS.EDIT_EVENT_TRANSACTIONAL_EMAILS
    ];

    const userPermissionKeys = Object.keys(userPermissions);
    const hasOnlyEditEventPermissions = userPermissionKeys.every(permission =>
        editEventPermissions.includes(permission)
    );

    return hasOnlyEditEventPermissions && userPermissionKeys.length > 0;
};
