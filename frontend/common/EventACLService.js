angular.module('SportWrench').service('EventACLService', EventACLService);

function EventACLService(EVENT_OPERATIONS, $http, $interval, eventsService, $stateParams) {
    this.acl                = null;
    this.EVENT_OPERATIONS   = EVENT_OPERATIONS;
    this.$http              = $http;
    this.$interval          = $interval;
    this.eventsService      = eventsService;
    this.$stateParams       = $stateParams;
    this.refreshInterval    = null;
}

EventACLService.prototype.isOperationAllowed = function (eventUserAcl, operation) {
    return eventUserAcl && eventUserAcl[operation];
};

EventACLService.prototype.setUserAcl = function (acl) {
    this.acl = acl;
    this.startAclRefreshMonitoring();
};

EventACLService.prototype.getUserAcl = function () {
    return this.acl;
};

EventACLService.prototype.userHasOnlyEventEditPermissions = function (userPermissions) {
    if(!userPermissions) {
        return true;
    }

    const editEventPermissions = [
        this.EVENT_OPERATIONS.EDIT_EVENT,
        this.EVENT_OPERATIONS.EDIT_EVENT_GENERAL,
        this.EVENT_OPERATIONS.EDIT_EVENT_LOCATION,
        this.EVENT_OPERATIONS.EDIT_EVENT_USERS,
        this.EVENT_OPERATIONS.EDIT_EVENT_TRANSACTIONAL_EMAILS
    ];

    const userPermissionKeys = Object.keys(userPermissions);
    const hasOnlyEditEventPermissions = userPermissionKeys.every(permission =>
        editEventPermissions.includes(permission)
    );

    return hasOnlyEditEventPermissions && userPermissionKeys.length > 0;
};

EventACLService.prototype.startAclRefreshMonitoring = function () {
    const self = this;

    // Stop any existing interval
    if (this.refreshInterval) {
        this.$interval.cancel(this.refreshInterval);
    }

    // Only start monitoring if we have an event ID
    const eventId = this.$stateParams.event;
    if (!eventId) {
        return;
    }

    // Check every 5 seconds if ACL needs refresh
    this.refreshInterval = this.$interval(function() {
        self.checkAndRefreshAcl(eventId);
    }, 5000);
};

EventACLService.prototype.checkAndRefreshAcl = function (eventId) {
    const self = this;

    this.$http.get(`/api/event/${eventId}/acl-refresh-needed`)
        .then(function(response) {
            if (response.data.refreshNeeded) {
                // Refresh ACL data
                self.eventsService.getEventByIdWithLocation(eventId, function(error, data) {
                    if (!error && data && data.acl) {
                        self.acl = data.acl;
                        console.log('ACL refreshed due to permission changes');
                    }
                });
            }
        })
        .catch(function(error) {
            // Silently handle errors to avoid spamming console
            console.debug('ACL refresh check failed:', error);
        });
};

EventACLService.prototype.stopAclRefreshMonitoring = function () {
    if (this.refreshInterval) {
        this.$interval.cancel(this.refreshInterval);
        this.refreshInterval = null;
    }
};
