angular.module('SportWrench')

.factory('userService', function ($rootScope, $localStorage, $http, $state, APP_ROUTES, Sentry, $window, SET_USER_NAME, _, HOME_PAGE_URL) {
    var storage = $localStorage;
    const USER_KEY = 'user';

    var _loginAs = function (email, token) {
        return $http.post('/api/eo/login', {
            email: email,
            password: token
        })
    };

    function removeFromLocalStorageByKey(key) {
        if (storage) {
            delete storage[key];
            delete $localStorage[key];
        }
    }

    return {
        isLoggedIn: function() {
            if(storage && storage.user) {
                return true;
            }
            $rootScope.$emit('userLoggedOut');
            return false;
        },
        getName: function() {
            if(storage && storage.user) {
                return storage.user.first;
            }
        },
        isEventOwner: function() {
            if(storage && storage.user) {
                return storage.user.eventOwner;
            }
        },
        hasGodRole: function () {
            if(storage && storage.user) {
                return storage.user.hasGodRole;
            }
        },
        isClubDirector: function() {
            if(storage && storage.user) {
                return storage.user.clubDirector;
            }
        },
        isSponsor: function () {
            if(storage && storage.user) {
                return storage.user.sponsor
            }
        },
        isStaff: function() {
            if(storage && storage.user) {
                return storage.user.staff;
            }
        },
        isVendor: function() {
            if(storage && storage.user) {
                return storage.user.sponsor;
            }
        },
        isSales: function () {
            if(storage && storage.user) {
                return storage.user.sales;
            }
        },
        isSWOwner: function () {
            if(storage && storage.user) {
                return storage.user.owner;
            }
        },
        isHousingManager: function () {
            if(storage && storage.user)
                return storage.user.housing;
        },
        hasEORole: function () {
            return (storage && storage.user && storage.user.hasEORole) || false;
        },
        isSpectator: function() {
            return !!storage && storage.user && storage.user.spectator;
        },
        logOut: function() {
            const url = `${HOME_PAGE_URL}/logout`;
                
            removeFromLocalStorageByKey(USER_KEY);

            const w = $window.open(url, '_self');

            if (!w) {
                $window.location.href = url;
            }
        },
        logIn: function (loggedInUser) {
            if (loggedInUser) {
                storage.user = loggedInUser;
                Sentry.setUser({ email: loggedInUser.email });
            }
        },
        getCountry: function () {
            if(storage && storage.user) {
                return storage.user.country;
            }
        },
        hasTickets: function () {
            if(storage && storage.user) {
                return storage.user.has_tickets;
            }
        },
        updateUser: function (user) {
            return $http.put('/api/user', user)
            .then(function (resp) {
                storage.user = resp.data.user;
                return resp.data.user;
            });
        },
        getUser: function (cb) {
            return $http.get('/api/user')
            .then(function (resp) {
                var user = (resp.data && resp.data.user) || {};
                if(cb) {
                    cb(user);
                } else {
                    return user;
                }
            });
        },
        getEmail: function () {
            if (storage && storage.user) {
                return storage.user.email;
            }
        },
        setEmail: function (email) {
            if (storage && storage.user) {
                storage.user.email = email;
            }
        },
        loginAs: function(email, token, callback) {
            _loginAs(email, token).success(function(resp) {
                callback(resp);
            });
        },
        hasAccess: function (event) {
            // access for admins and EO only
            if(!event && !event.event_id && !event.eoemail) return false;

            return (event.eoemail == this.getEmail() || this.hasGodRole());
        },
        isUSAVAdmin: function () {
            return storage.user && storage.user.isUSAVAdmin;
        },
        isAllowedToLoginAsCD: function () {
            return storage.user && storage.user.allow_login_as_cd;
        },
        getUserInfo: function() {
            return $http.get('/api/auth')
                .then((response) => {
                    const user = response.data.user;

                    if (_.isEmpty(user)) {
                        this.redirectToHomePage(response);
                    } else {
                        this.logIn(user);

                        if ($state.current.name === APP_ROUTES.INDEX) {
                            this.navigateUser();
                        }

                        $rootScope.$emit(SET_USER_NAME, { name: user.first });
                    }
                })
                .catch((response) => {
                    this.redirectToHomePage(response);
                });
        },
        navigateUser() {
            if(this.isEventOwner()) {
                $state.go(APP_ROUTES.EO.EVENTS);
            } else if(this.isClubDirector()) {
                $state.go(APP_ROUTES.CD.INFO);
            } else if(this.isSales()) {
                $state.go(APP_ROUTES.SM.EVENTS);
            } else if(this.isSponsor()) {
                $state.go(APP_ROUTES.EX.PROFILE);
            } else if(this.isStaff()) {
                $state.go(APP_ROUTES.OF.INFO);
            } else if (this.isHousingManager()) {
                $state.go(APP_ROUTES.HOUSING_EVENTS);
            } else if (this.isSpectator) {
                window.location.replace('/spectator/tickets')
            } else {
                $state.go(APP_ROUTES.EO.EVENTS);
            }
        },
        redirectToHomePage(response, environment) {
            removeFromLocalStorageByKey(USER_KEY);

            const url = `${HOME_PAGE_URL}/login`;

            const w = $window.open(url, '_self');

            if (!w) {
                window.location = url;
            }
        }
    }
});
