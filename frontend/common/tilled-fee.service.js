angular.module('SportWrench').service('TilledFeeService', ['UtilsService', 'FEE_PAYER', TilledFeeService]);

function TilledFeeService (UtilsService, FEE_PAYER) {
	this.utils 					= UtilsService;
	this.DEFAULT_TILLED_FEE 	= 0.029;
	this.DEFAULT_TILLED_FIXED 	= 0.3;
	this.FEE_PAYER              = FEE_PAYER;
	this.MIN_CANCELLATION_AMOUNT= 0;
}

TilledFeeService.prototype.countCardFeeAmount = function (amount, eventPercent, eventTax, isDefault) {
	if(!amount) {
	    return 0;
    }

    let __percent   = parseFloat(eventPercent)  || this.DEFAULT_TILLED_FEE;
    let __fixed     = parseFloat(eventTax)      || this.DEFAULT_TILLED_FIXED;

	return isDefault
        ? this.__countDefaultCardFee(amount, __percent, __fixed)
        : this.__countCustomerCardFee(amount, __percent, __fixed);
};

TilledFeeService.prototype.__countCustomerCardFee = function (amount, percent, fixed) {
    return this.utils.approxNumber((amount + fixed) / (1 - percent) - amount);
};

TilledFeeService.prototype.__countDefaultCardFee = function (amount, percent, fixed) {
    return this.utils.approxNumber(amount * percent + fixed);
};

TilledFeeService.prototype.countACHFeeAmount = function (amount, percent, isDefault) {
	if(!amount) return 0;

	throw new Error('ACH not supported')
};
