angular.module('SportWrench').factory('PlaidService', ['Plaid', '$q', PlaidPaymentsService]);

function PlaidPaymentsService (Plaid, $q) {
	// https://plaid.com/docs/link/stripe/#step-3-integrate-with-plaid-link
	var plaidActions = {
		openPlaidLink: function (data, plaidLinkParams) {
			var params 		= plaidLinkParams || {},
				defer 		= $q.defer(),
				onLoad 		= function () {
					defer.notify('loaded');
				},
				onSuccess 	= function (public_token, metadata) {
					defer.resolve({
				    	public_token 	: public_token,
				    	account_id 		: metadata.account_id
				    });
				},
				onExit 		= function (err) {
					defer.reject(err);
				};

			Plaid.create({
				selectAccount 	: true,
				/* The Plaid API environment on which to create user accounts. For development and testing, use tartan. 
				*  For production use, use production.
				*/
				env 			: data.env, 
				// Displayed once a user has successfully linked their account.
				clientName 		: params.clientName || 'SportWrench', 
				// The public_key associated with your account
				key 			: data.public_key, 
				// The Plaid product you wish to use, either auth or connect.
				product 		: 'auth',
				/* A function that is called when the Link module has finished loading. 
				* Calls to plaidLinkHandler.open() prior to the onLoad callback will be delayed 
				* until the module is fully loaded.
				*/
				onLoad 			: onLoad,
				/* A function that is called when a user has successfully onboarded their account. 
				* The function should expect two arguments, the public_key and a metadata object.
				*/
				onSuccess 		: onSuccess,
				// A function that is called when a user has specifically exited the Link flow.
				onExit 			: onExit,

                apiVersion      : 'v2'
			}).open(params.bank);

			return defer.promise;
		}
	};

	Object.defineProperty(plaidActions, 'PROD', {
		value 			: 'production',
		writable 		: false,
	  	configurable 	: false
	});

	Object.defineProperty(plaidActions, 'DEV', {
		value 			: 'sandbox',
		writable 		: false,
	  	configurable 	: false
	});

	return plaidActions;
}
