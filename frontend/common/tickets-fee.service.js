angular.module('SportWrench').service('TicketsFeeService', ['UtilsService', 'TilledFeeService', 'StripeFeeService', 'FEE_PAYER', 'PAYMENT_PROVIDER', TicketsFeeService]);

function TicketsFeeService(UtilsService, TilledFeeService, StripeFeeService, FEE_PAYER, PAYMENT_PROVIDER) {
	this.utils 					= UtilsService;
	this.FEE_PAYER              = FEE_PAYER;
    this.PAYMENT_PROVIDER = PAYMENT_PROVIDER;
    this.tilledFee = TilledFeeService;
    this.stripeFee = StripeFeeService;
}

TicketsFeeService.prototype.calculateStripeFee = function (amount, payment, isDefault, isACH) {
    const { stripe_percent, stripe_fixed } = payment;

    if(isACH) {
        return this.stripeFee.countACHFeeAmount(amount, stripe_percent, isDefault)
    }

    return this.stripeFee.countCardFeeAmount(amount, stripe_percent, stripe_fixed, isDefault)
}

TicketsFeeService.prototype.calculateTilledFee = function (amount, payment, isDefault, isACH) {
    const { tilled_percentage, tilled_fixed } = payment;

    if(isACH) {
        return this.tilledFee.countACHFeeAmount(amount, tilled_percentage, isDefault)
    }

    return this.tilledFee.countCardFeeAmount(amount, tilled_percentage, tilled_fixed, isDefault)
}

TicketsFeeService.prototype.buyerPaysProviderFee = function (payment) {
    const { payment_provider, stripe_fee_payer, tilled_fee_payer } = payment;

    if(payment_provider === this.PAYMENT_PROVIDER.PAYMENT_HUB) {
        return false;
    }

    const provider_fee_payer = payment_provider === this.PAYMENT_PROVIDER.TILLED ? tilled_fee_payer : stripe_fee_payer;

    return provider_fee_payer  === this.FEE_PAYER.BUYER;
}

TicketsFeeService.prototype.buyerPaysSWFee = function (payment) {
    const { payment_provider } = payment;

    if(payment_provider === this.PAYMENT_PROVIDER.PAYMENT_HUB) {
        return false;
    }

    return payment.sw_fee_payer === this.FEE_PAYER.BUYER;
}

TicketsFeeService.prototype.calculatePaymentHubFee = function (amount, payment) {
    const { stripe_percent, stripe_fixed } = payment;
    
    return this.stripeFee.countCardFeeAmount(amount, stripe_percent, stripe_fixed, true)
}

TicketsFeeService.prototype.getFeeCalculator = function (payment) {
    const { payment_provider } = payment

    if(payment_provider === this.PAYMENT_PROVIDER.PAYMENT_HUB) {
        return this.calculatePaymentHubFee
    }

    return payment_provider === this.PAYMENT_PROVIDER.TILLED ? this.calculateTilledFee : this.calculateStripeFee;
}

TicketsFeeService.prototype.countTotals = function (payment, ticketsQtyList, isACH) {
    const isPaymentHub = payment.payment_provider === this.PAYMENT_PROVIDER.PAYMENT_HUB;

    let total = 0, subtotal = 0, swFee = 0;

    let refund = { total: payment.amount };

    const buyerIsSWFeePayer = payment.sw_fee_payer === this.FEE_PAYER.BUYER;

    payment.ticketsList.forEach((t) => {
        let qty             = +t.quantity,
            price           = +t.ticket_price,
            discount        = +t.discount < this.MIN_CANCELLATION_AMOUNT ? 0 : +t.discount,
            cancellation    = +t.cancellation < this.MIN_CANCELLATION_AMOUNT ? 0 : +t.cancellation;

        subtotal    = (qty === 0)
            ?cancellation
            :this.utils.approxNumber(qty * price - discount);

        swFee       += (qty === 0 && cancellation)
            ? !buyerIsSWFeePayer ? (ticketsQtyList[t.purchase_ticket_id] * t.app_fee) : t.app_fee
            :(qty * t.app_fee);

        total       += subtotal;

        t.amount = subtotal;
    });

    refund.sw_fee = this.utils.approxNumber(swFee);

    if(!isPaymentHub && buyerIsSWFeePayer) {
        total += refund.sw_fee;
    }

    const feeCalculator = this.getFeeCalculator(payment).bind(this);

    const isDefaultFeeCalculationMode = isPaymentHub ? true : !this.buyerPaysProviderFee(payment);

    refund.provider_fee = feeCalculator(total, payment, isDefaultFeeCalculationMode, isACH)

    if(!isPaymentHub && this.buyerPaysProviderFee(payment)) {
        total += refund.provider_fee;
    }

    refund.total 	    = this.utils.approxNumber(total);
    let amountToRefund  = this.utils.approxNumber(payment.amount - refund.total);

    return { refund, amountToRefund };
};