angular.module('SportWrench')
.factory('divisionsService', function ($http, toastr) {
    var urlPrefix   = '/api/event/',
        urlDivision = '/division/';

    return {
        getDivisions: function(eventId, params) {
            return $http.get(urlPrefix + eventId + '/divisions', {
                params: params
            })
            .then(function (resp) {
                return (resp.data && resp.data.divisions) || []
            })
        },
        createDivision: function(eventId, division) {
            return $http.post(urlPrefix + eventId + '/division', division) 
        },
        updateDivision: function(eventId, divisionId, data) {
            return $http.put(urlPrefix + eventId + urlDivision + divisionId, data, {
                withCredentials: true,
                headers: { 'Content-Type': undefined },
                transformRequest: angular.identity
            });
        },
        getDivision: function(eventId, divisionId) {
            return $http.get(urlPrefix + eventId + urlDivision + divisionId);
        },
        markFull: function (eventId, divisionId) {
            return $http.put(urlPrefix + eventId + urlDivision + divisionId + '/full')
        },
        remove: function (eventId, divisionId) {
            return $http.delete(urlPrefix + eventId + urlDivision + divisionId + '/remove')
        },
        getAges: function () {
            return [
                { age: 10, title: '10' },
                { age: 11, title: '11' },
                { age: 12, title: '12' },
                { age: 13, title: '13' },
                { age: 14, title: '14' },
                { age: 15, title: '15' },
                { age: 16, title: '16' },
                { age: 17, title: '17' },
                { age: 18, title: '18' },
                { age: 0,  title: 'Adult' }
            ];
        },
        generateShortName: function(age, level, gender, numberOfEventGenders, truncateLevel) {
            var genderAbbr = '';
            var levelAbbr  = '';

            // gender abbr added only if event has more then one gender
            if (numberOfEventGenders > 1) {
                genderAbbr = ( gender ? ' ' + gender.charAt(0).toUpperCase() : '' );
            }

            // dont truncate level if called from wizard 
            // when called from wizard form we use full abbr field
            // when called from create form we use first letter of division level
            levelAbbr = truncateLevel ?  level.charAt(0) :  level;

            return age + ' ' + levelAbbr + genderAbbr;
        }
    }
});
