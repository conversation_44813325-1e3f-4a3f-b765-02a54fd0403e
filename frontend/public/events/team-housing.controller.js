angular.module('SportWrench').controller('Housing.Team.Info.HousingController', HousingController);


function HousingController (team, $stateParams, $uibModalInstance, $rootScope, teamHousingService, $scope) {
    let self        = this;
    this.team       = team;
    this.ths_data   = {};
    this.contacts   = {};
    this.loading    = { data_loaded: false };
    this.eventId    = $stateParams.event;

    const initTeam = angular.copy(this.team);

    let closingInProcess = false;

    function _getBookings() {
        teamHousingService.getBookings($stateParams.event, self.team.roster_team_id).then(team => {
            self.ths_data = team;
            self.loading.data_loaded = true;
        })
    };

    this.isTHSManager = function () {
        return ($rootScope.isHousingManager() === 1);
    };

    this.close = function () {
        closingInProcess = true;

        $uibModalInstance.close(!_.isEqual(initTeam, this.team));
    };

    function _loadContacts() {
        teamHousingService.loadContacts($stateParams.event, self.team.roster_team_id).then(contacts => {
            self.contacts = contacts;
            self.loading.data_loaded = true;
        });
    }

    function loadData() {
        if(self.isTHSManager()) {
            _getBookings();
        };

        _loadContacts();
    }

    this.isBookingEmpty = function () {
        return _.isEmpty(this.ths_data);
    }

    loadData();

    $scope.$on('modal.closing', (e) => {
        if (closingInProcess) {
            return;
        }

        e.preventDefault();

        this.close();
    });
}
