angular.module('SportWrench')

.config(function ($stateProvider, $urlRouterProvider, APP_ROUTES, EVENT_OPERATIONS) {
    var openOfficialProfile = function ($uibModal, $state) {
        $uibModal.open({
            templateUrl     : 'official/official-edit.html',
            controller      : 'OfficialEditController'
        }).result.finally(function () {
            $state.go('^');
        });
    };

    var openExhibitorProfile = function ($uibModal) {
        return $uibModal.open({
            templateUrl     : 'exhibitor/edit-profile.html',
            controller      : 'Exhibitor.ChangeProfileCtrl'
        }).result;
    };

    var loadGoogleMaps = ['lazyLoadService', function (lazyLoadService) {
        return lazyLoadService.loadGoogleMaps();
    }];

    const checkIsTabAvailable = function(route) {
        return [
            'loadEvent', 'EventDashboardTabsService', '$state',
            function(loadEvent, EventDashboardTabsService, $state) {
                let tabIsAvailable = true;

                const { tab, parentTab } = EventDashboardTabsService.findTabByState(route, loadEvent);

                if (tab.isVisible) {
                    tabIsAvailable = tabIsAvailable && tab.isVisible();
                }

                if (parentTab) {
                    tabIsAvailable = tabIsAvailable && parentTab.isVisible();
                }

                if (!tabIsAvailable) {
                    let tabs = EventDashboardTabsService.getTabs(loadEvent);

                    let success = EventDashboardTabsService.goToAvailableTab(null, tabs);

                    if(!success) {
                        $state.go(APP_ROUTES.EO.EVENTS);
                    }
                } else {
                    return null;
                }
            }
        ]
    }

    let checkOfficialsRoutePermissions = function () {
        return [
            '$state', '$stateParams', 'userService', '$q', 'EVENTS_WHERE_OFFICIAL_PAYOUTS_HIDDEN_FROM_HO',
            function (
                $state, $stateParams, userService, $q, EVENTS_WHERE_OFFICIAL_PAYOUTS_HIDDEN_FROM_HO
            ) {
                let deferred = $q.defer();

                if(userService.hasGodRole()) {
                    deferred.resolve();
                    return deferred.promise;
                }

                if(EVENTS_WHERE_OFFICIAL_PAYOUTS_HIDDEN_FROM_HO.includes(Number($stateParams.event))) {
                    $state.go(APP_ROUTES.OF.MANAGE_EVENT, {event: $stateParams.event});
                    deferred.reject();
                } else {
                    deferred.resolve();
                }

                return deferred.promise;
            }
        ];
    }

    let checkRoutePermissions = function (permission) {
        return [
            'loadEvent', 'EventACLService', '$state', '$stateParams', 'toastr',
            'EventDashboardTabsService', 'userService',
            function (
                loadEvent, EventACLService,  $state, $stateParams, toastr, EventDashboardTabsService, userService
            ) {
                if(userService.hasGodRole()) {
                    return null;
                }

                let userPermissions = EventACLService.getUserAcl();

                if(!userPermissions) {
                    return null;
                }

                let routeIsAllowed = EventACLService.isOperationAllowed(userPermissions, permission);

                if(!routeIsAllowed) {
                    let tabs = EventDashboardTabsService.getTabs(loadEvent);

                    let success = EventDashboardTabsService.goToAvailableTab(null, tabs);

                    if(!success) {
                        $state.go(APP_ROUTES.EO.EVENTS);
                    }
                }
            }]
    };

    var loadCreditCardModules = ['lazyLoadService', function (lazyLoadService) {
        return lazyLoadService.loadCard();
    }];

    var loadCKEditor = ['lazyLoadService', function (lazyLoadService) {
        return lazyLoadService.loadCKEditor();
    }];

    var loadDragDrop = ['lazyLoadService', function (lazyLoadService) {
        return lazyLoadService.loadDragDrop();
    }];

    const loadMomentTimezone = ['lazyLoadService', function(lazyLoadService) {
        return lazyLoadService.loadMomentTimezone();
    }]

    var openClubProfile = function($stateParams, $state, $uibModal) {
        $uibModal.open({
            templateUrl: APP_ROUTES.CD.FOLDER + 'edit-club.html',
            controller: 'EditClubController'
        });
    };

    var _onClose = function ($state, isChanged) {
        if (isChanged === true) {
            $state.go('^', null, { reload: true });
        } else {
            $state.go('^');
        }
    };

    let checkCDProfileCompleted = function (loadClub, USER_ROLE, userProfileService) {
        if(loadClub && loadClub.not_completed) {
            userProfileService.openUserProfileCompletenessErrorModal(USER_ROLE.CD);
        }
    };

    const exhibitorsTickets = function ($q, ticketsService, $stateParams) {
        const defer = $q.defer(),
            resolveErrorObj = { payments: [], total: {}, total_rows: 0 },
            queryParams = {
                limit: 100,
                stat: true,
                sort: 'purchased',
                is_camp: false,
                revert: true
            };

        ticketsService.exhibitorsTickets($stateParams.event, queryParams)
            .then(function (resp) {
                defer.resolve(resp.data || resolveErrorObj);
            }, function () {
                defer.resolve(resolveErrorObj);
            });

        return defer.promise;
    };

    $urlRouterProvider.otherwise('/');

    $stateProvider

    .state(APP_ROUTES.INDEX, {
        url: '/',
        views: {
            'rootView': {
                controller: 'indexController'
            },
        }
    })

    .state(APP_ROUTES.OF.PARENT, {
        url: '/official',
        views: {
            'rootView': {
                templateUrl: APP_ROUTES.OF.FOLDER + 'official-menu.html',
                controller: 'OfficialMenuController'
            }
        },
        resolve: {
            officialData: function ($q, officialService) {
                return officialService.officialRes().find();
            }
        }
    }).state(APP_ROUTES.OF.INFO, {
        url: '/info',
        templateUrl: APP_ROUTES.OF.FOLDER + 'official-info.html',
        controller: 'OfficialInfoController'
    }).state(APP_ROUTES.EO.TRANSFERS, {
        url         : '/transfers',
        template    : '<event-transfers></event-transfers>',
        resolve     : {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TRANSFERS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.ACCOUNTING_TAB)
        }
    }).state(APP_ROUTES.OF.INFO_NEW, {
        url: '/new',
        onEnter: openOfficialProfile
    }).state(APP_ROUTES.OF.INFO_UPDATE, {
        url: '/update',
        onEnter: openOfficialProfile
    }).state(APP_ROUTES.OF.EVENTS, {
        url: '/events',
        templateUrl: APP_ROUTES.OF.FOLDER + 'events/official-events.html',
        controller: 'OfficialEventsController'
    }).state(APP_ROUTES.OF.EVENTS_INFO, {
        url: '/:event/info',
        onEnter: function($uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.OF.FOLDER + 'events/official-event-info.html',
                controller: 'OfficialEventInfoController'
            });
        }
    }).state(APP_ROUTES.OF.EVENTS_CHECKIN, {
        url: '/:event/check',
        onEnter: function($state, v2OfficialService, regData) {
            const close = _onClose.bind(null, $state);

            v2OfficialService.showEventRegModal(regData).then(close, close);
        },
        resolve: {
            regData: ['EventOfficialService', '$stateParams', async function (EventOfficialService, $stateParams) {
                const {data} = await EventOfficialService.regData($stateParams.event);

                return data;
            }],
        }
    }).state(APP_ROUTES.OF.PAYOUTS, {
        url: '/:event/payouts',
        params: {
            name : null
        },
        onEnter: function($stateParams, $state, $uibModal) {
            $uibModal.open({
                templateUrl : APP_ROUTES.OF.FOLDER + 'payouts/official-payout.html',
                controller: 'OfficialEventPayoutsController'
            }).result.then(function() {
                $state.transitionTo(APP_ROUTES.OF.EVENTS, null, {
                    reload: true,
                    inherit: true,
                    notify: true
                });
            }, function() {
                $state.go(APP_ROUTES.OF.EVENTS);
            });
        }
    })
    .state(APP_ROUTES.OF.SCHEDULE, {
        url: '/schedule',
        template: '<official-events-schedule></official-events-schedule>'
    }).state(APP_ROUTES.OF.MANAGE_EVENT, {
        url: '/event/:event/manage',
        templateUrl: APP_ROUTES.OF.FOLDER + 'event-managing/event-managing.html',
        controller: 'Official.Managing.EventManagingController',
        resolve: {
            loadEditor: loadCKEditor
        }
    })
    .state(APP_ROUTES.OF.OFFICIALS_PAYOUTS, {
        url: '/event/:event/officials-payouts',
        template: '<officials-payouts></officials-payouts>',
        onEnter: function($rootScope, $stateParams, $timeout) {
            // Used $timeout for asynchronous call $broadcast.
            $timeout(() => {
                // Head Official dashboard
                $rootScope.$broadcast('off.event.manage', $stateParams.event);
            }, 0);
        },
        resolve: {
            checkPermissions: checkOfficialsRoutePermissions()
        }
    }).state(APP_ROUTES.OF.STAFF_EVENTS, {
        url: '/staff-events',
        template: '<staff-events></staff-events>',
    }).state(APP_ROUTES.OF.STAFF_EVENTS_INFO, {
        url: '/:event/info',
        onEnter: function($uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.OF.FOLDER + 'events/official-event-info.html',
                controller: 'OfficialEventInfoController'
            });
        }
    }).state(APP_ROUTES.OF.STAFF_EVENTS_CHECKIN, {
        url: '/:event/check',
        onEnter: function($state, v2OfficialService, regData) {
            var close = _onClose.bind(null, $state);

            v2OfficialService.showEventRegModal(regData).then(close, close);
        },
        resolve: {
            regData: ['EventOfficialService', '$stateParams', async function (EventOfficialService, $stateParams) {
                const {data} = await EventOfficialService.regData($stateParams.event);

                return data;
            }],
        }
    }).state(APP_ROUTES.OF.MANAGE_EVENT_INFO, {
        url: '/:official/info',
        onEnter: ['$stateParams','HOEventManagementService', '$state', 'MembersInfoService', 'OFFICIAL_MEMBER_TYPE',
        function ($stateParams, HOEventManagementService, $state, MembersInfoService, OFFICIAL_MEMBER_TYPE) {
            var close = _onClose.bind(null, $state);

            MembersInfoService.openInfoModal($stateParams.event, $stateParams.official, {
                member: OFFICIAL_MEMBER_TYPE,
                operationService: HOEventManagementService,
            })
            .then(close, close);
        }]
    })
    .state(APP_ROUTES.EO.EVENTS, {
        url: '/events',
        views: {
            'rootView': {
                templateUrl: 'events/events.html',
                controller: 'eventsController'
            }
        },
        resolve: {
            loadEditor: loadCKEditor
        }
    })
    .state(APP_ROUTES.EO.STRIPE_ACCOUNT, {
        url: '/stripe-acc',
        onEnter: ['EventOwnerService', '$state', function (EventOwnerService, $state) {
            var close = _onClose.bind(null, $state);

            EventOwnerService.openStripeAccModal()
            .then(close, close);
        }]
    }).state(APP_ROUTES.EO.PAYMENT_CARD, {
        url: '/payment-card',
        resolve: {
            loadMyService: loadCreditCardModules,
        },
        onEnter: ['PaymentCardService', '$state', '$stateParams', '$location',
            function (PaymentCardService, $state, $stateParams, $location) {

            let close = _onClose.bind(null, $state);

            const activeTab = PaymentCardService.getActiveTab($stateParams, $location.search());

            PaymentCardService.openPaymentCardModal(activeTab)
                .then(close, close);
        }],
        params: {
            active_tab: null
        }
    }).state(APP_ROUTES.EO.ACCOUTING_MERCHANT_INFO, {
        url         : '/accounting/merchant-info',
        template    : '<accounting-merchant-info></accounting-merchant-info>'
    }).state(APP_ROUTES.EO.ACCOUTING_ENTRY_FEES, {
        url         : '/accounting/entry-fees',
        template    : '<accounting-entry-fees></accounting-entry-fees>'
    }).state(APP_ROUTES.EO.ACCOUTING_EXHIBITORS, {
        url         : '/accounting/exhibitors',
        template    : '<accounting-exhibitors></accounting-exhibitors>'
    })
    .state(APP_ROUTES.EO.CREATE_EVENT, {
        url: '/event/create',
        views: {
            'rootView': {
                template: '<event-settings mode="create"></event-settings>'
            }
        },
        resolve: {
            loadEditor: loadCKEditor
        }
    }).state(APP_ROUTES.EO.UPDATE_EVENT, {
        url: '/event/:event/update',
        views: {
            'rootView': {
                template: '<event-settings mode="update"></event-settings>'
            }
        },
        resolve: {
            loadEditor: loadCKEditor
        }
    }).state(APP_ROUTES.EO.ADD_EVENT_USER , {
        url: '/new',
        onEnter: function($stateParams, $state, $uibModal) {
            $uibModal.open({
                templateUrl: 'events/settings/general/event-users/event-users-add/event-users-add.html',
                controller: 'AddEventUserContoller',
            }).result.then(function() {
                $state.transitionTo(APP_ROUTES.EO.UPDATE_EVENT, null, {
                    reload: true,
                    inherit: true,
                    notify: true
                });
            }, function() {
                $state.go(APP_ROUTES.EO.UPDATE_EVENT);
            });
        }
    }).state(APP_ROUTES.EO.PARENT, {
        url: '/event/:event',
        views: {
            'rootView': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-dashboard.html',
                controller: 'EventDashboardController'
            }
        },
        resolve: {
            loadEvent: function (
                $q, eventsService, $stateParams, $timeout, eventDashboardService, eventUserService, EventACLService
            ) {
                var defer = $q.defer(),
                    timer = $timeout(function () {
                        defer.reject();
                    }, 10000);

                eventsService.getEventByIdWithLocation($stateParams.event, function (error, data) {
                    $timeout.cancel(timer);
                    if(error) {
                        defer.reject(error);
                    } else {
                        eventDashboardService.setEvent(data.data.event);
                        eventUserService.roles = data.data.roles;
                        EventACLService.setUserAcl(data.acl);
                        defer.resolve(data.data.event);
                    }
                });
                return defer.promise;
            }
        }
    }).state(APP_ROUTES.EO.INFO, {
        url: '/info',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-info.html',
                controller: 'Event.InfoController'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.INFO),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.EVENT_INFO_TAB)
        }
    }).state(APP_ROUTES.EO.EXHIBITORS_PAYMENTS, {
        url: '/exhibitors-payments',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'exhibitors/payments/exhibitors-payments.html',
                controller: 'events.ExhibitorsPaymentsController'
            }
        },
        onEnter: ['$stateParams', '$location', function ($stateParams, $location) {
            let queryParams = $location.search();

            if(queryParams) {
                if(queryParams.purchase_id) {
                    $stateParams.openPaymentModal = { purchase_id: Number(queryParams.purchase_id) };
                }
            }
        }],
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.EXHIBITORS_PAYMENTS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.EXHIBITORS_TAB),
            loadMyService: loadCreditCardModules,
        },
        params: {
            showStripeStatistic: true,
            filters: null,
        }
    }).state(APP_ROUTES.EO.BOOTHS,  {
        url: '/booths',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'exhibitors/booths/booths.html',
                controller: 'events.ExhibitorsController'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.EXHIBITORS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.EXHIBITORS_TAB)
        }
    }).state(APP_ROUTES.EO.EXHIBITORS,  {
        url: '/exhibitors',
        template: '<exhibitors></exhibitors>',
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.EXHIBITORS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.EXHIBITORS_TAB),
            loadMyService: loadCreditCardModules,
        },
        params: {
            userExhibitorsState: APP_ROUTES.EO.USER_EXHIBITORS,
            paymentsListState: APP_ROUTES.EO.EXHIBITORS_PAYMENTS,
        }
    }).state(APP_ROUTES.EO.USER_EXHIBITORS,  {
        url: '/user-exhibitors',
        template: '<user-exhibitors></user-exhibitors>',
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.USER_EXHIBITORS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.EXHIBITORS_TAB),
            loadMyService: loadCreditCardModules,
        },
        params: {
            exhibitorsState: APP_ROUTES.EO.EXHIBITORS,
        }
    }).state(APP_ROUTES.EO.EXHIBITORS_TICKETS, {
        url: '/exhibitors-tickets',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'exhibitors/tickets/exhibitors-tickets.html',
                controller: 'events.ExhibitorsTicketsController',
                controllerAs: 'data'
            }
        },
        resolve: {
            exhibitorsTickets
        },
        params: {
            event       : null,
        }
    }).state(APP_ROUTES.EO.CUSTOM_FORMS,  {
        url: '/forms',
        views: {
            '': {
                template: '<custom-forms-builder></custom-forms-builder>'
            }
        },
        resolve: {
            loadDragDrop
        }
    }).state(APP_ROUTES.EO.CLUB_INVOICES,  {
        url: '/club-invoices',
        views: {
            '': {
                template: '<club-invoice></club-invoice>'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.CLUB_INVOICES),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TEAMS_TAB)
        }
    }).state(APP_ROUTES.EO.CAMPS,  {
        url: '/camps',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'camps/camps.html',
                controller: 'CampsController'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.CAMPS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB)
        }
    })
    .state(APP_ROUTES.EO.TICKETS, {
        url    : '/tickets-settings',
        views  : {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'tickets/settings.html',
                controller : 'Event.Tabs.TicketsSettingsController'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TICKETS),
            checkPermissions   : checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB),
            loadEditor         : loadCKEditor,
            loadDragDrop
        }
    })
    .state(APP_ROUTES.EO.TICKETS_COUPONS, {
        url: '/tickets-coupons',
        views: {
            '': {
                template: '<ticket-coupon-list></ticket-coupon-list>'
            }
        }
    })
    .state(APP_ROUTES.EO.TICKETS_APP_VERIFICATION_APPROVE, {
        url  : '/application-approve',
        views: {
            '': {
                template: '<tickets-app-verification-users-list/>'
            }
        },
        resolve: {
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKET_APPLICATION_APPROVE_TAB),
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TICKETS_APP_VERIFICATION_APPROVE),
        }
    }).state(APP_ROUTES.EO.TICKETS_PAYMENTS, {
        url: '/tickets-payments?mode&value',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'tickets/payments-list.html',
                controller: 'Event.Tabs.TicketsPaymentsListController',
                controllerAs: 'data'
            }
        },
        resolve: {
            paymentsList: function (
                $q, ticketsService, $stateParams, eventStrorage, TICKETS_FILTER_NAME, loadEvent,
                CAMPS_TICKETS_FILTER_NAME, eventDashboardService, CAMPS_SALES_TYPE, $location
            ) {
                let campsFilters  = eventStrorage.getTicketsFilter(CAMPS_TICKETS_FILTER_NAME, $stateParams.event);
                let ticketsFilter = eventStrorage.getTicketsFilter(TICKETS_FILTER_NAME      , $stateParams.event);

                let event               = eventDashboardService.getEvent();
                let isCampsEvent        = (event.sales_type === CAMPS_SALES_TYPE);

                let searchParams = $location.search();

                var defer = $q.defer(),
                    resolveErrorObj = { payments: [], total: {}, total_rows: 0 },
                    queryParams = {
                        limit: 100,
                        stat: true,
                        sort: 'purchased',
                        is_camp: isCampsEvent,
                        revert: true
                    };

                if(campsFilters && campsFilters.camp) {
                    queryParams.camp = campsFilters.camp;
                }

                if(campsFilters && campsFilters.type) {
                    queryParams.type = campsFilters.type;
                }

                if(ticketsFilter) {
                    if(ticketsFilter.types) {
                        queryParams[ticketsFilter.types] = true;
                    }

                    if(ticketsFilter.statuses) {
                        queryParams[ticketsFilter.statuses] = true;
                    }

                    if(ticketsFilter.registration) {
                        queryParams[ticketsFilter.registration] = true;
                    }
                }

                if(searchParams && searchParams.barcode) {
                    $stateParams.barcode = searchParams.barcode;
                }

                if($stateParams.mode && $stateParams.value)
                    queryParams[$stateParams.mode] = $stateParams.value;
                ticketsService.payments($stateParams.event, queryParams)
                .then(function (resp) {
                    defer.resolve(resp.data || resolveErrorObj);
                }, function () {
                    defer.resolve(resolveErrorObj);
                });
                return defer.promise;
            },
            loadCKEditor    : loadCKEditor,
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TICKETS_PAYMENTS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB),
            loadMomentTimezone,
        },
        params: {
            event       : null,
            barcode     : null
        }
    }).state(APP_ROUTES.EO.TICKETS_MAP, {
        url: '/tickets-map',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'tickets/tickets-map.html',
                controller: 'Event.Tabs.TicketsMapController'
            }
        },
        resolve: {
            loadMap         : loadGoogleMaps,
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TICKETS_MAP),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB)
        }
    }).state(APP_ROUTES.EO.TICKETS_STATS, {
        url: '/accounting/tickets-stats',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'tickets/tickets-statistics.html',
                controller: 'Event.Tabs.TicketsStatistics'
            }
        },
        resolve: {
            loadData: function (ticketsService, $stateParams) {
                return ticketsService.statistics($stateParams.event, {})
                .then(function (resp) {
                    return resp.data;
                });
            },
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TICKETS_STATS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB)
        }
    }).state(APP_ROUTES.EO.CAMPS_STATS, {
        url: '/camps-stats',
        views: {
            '': {
                template: '<camps-stats></camps-stats>'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.CAMPS_STATS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB)
        }
    }).state(APP_ROUTES.EO.TICKETS_DISCOUNTS, {
        url: '/tickets-discounts',
        views: {
            '': {
                template: '<tickets-discounts-list></tickets-discounts-list>'
            }
        },
        resolve: {
            loadCKEditor: loadCKEditor,
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.TICKETS_DISCOUNTS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TICKETS_TAB)
        },
        params: {
            discount_id: null,
            event: null
        }
    }).state(APP_ROUTES.EO.TEAMS, {
        url: '/allteams?mode&value',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'teams/all-teams.tab.html',
                controller: 'Event.Tabs.AllTeamsController'
            }
        },
        resolve: {
            loadMyService       : loadCreditCardModules,
            loadMap             : loadGoogleMaps,
            loadCKEditor        : loadCKEditor,
            checkIsTabAvailable : checkIsTabAvailable(APP_ROUTES.EO.TEAMS),
            checkPermissions    : checkRoutePermissions(EVENT_OPERATIONS.TEAMS_TAB),
            lastUsedSortOrder   : ['eventStrorage', function (eventStrorage) {
                return eventStrorage.getTeamsSorting();
            }]
        }
    }).state(APP_ROUTES.EO.CHECK_IN, {
        url: '/checkin',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'check-in.html',
                controller: 'EventCheckInController'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.CHECK_IN),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.CHECKIN_TAB)
        },
    }).state(APP_ROUTES.EO.DIVISIONS, {
        url: '/divisions',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-divisions.html',
                controller : 'EventDivisionsController'
            }
        },
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.DIVISIONS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.DIVISIONS_TAB)
        },
    }).state(APP_ROUTES.EO.DIVISIONS_EDIT, {
        url: '/:division/edit',
        onEnter: function($stateParams, $state, $uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.EO.FOLDER + 'divisions/create-division.html',
                controller: 'Event.DivisionContoller',
                resolve: {
                    mode: function () {
                        return 'edit';
                    },
                    division: function ($q, divisionsService, $timeout, $state, DateService) {
                        var defer = $q.defer(),
                            timer = $timeout(function () {
                                defer.resolve({});
                            }, 5000);
                        divisionsService.getDivision($stateParams.event, $stateParams.division)
                        .success(function (data) {
                            $timeout.cancel(timer);
                            var d = data.division;
                            if(d.roster_deadline)
                                d.roster_deadline = DateService.parseUnix(d.roster_deadline);
                            if(d.date_reg_close)
                                d.date_reg_close = DateService.parseUnix(d.date_reg_close);
                            defer.resolve(data.division);
                        }).error(function () {
                            $timeout.cancel(timer);
                            defer.reject({});
                        });
                        return defer.promise;
                    }
                }
            }).result.then(function() {
                $state.transitionTo(APP_ROUTES.EO.DIVISIONS, null, {
                    reload: true,
                    inherit: true,
                    notify: true
                });
            }, function() {
                $state.go(APP_ROUTES.EO.DIVISIONS);
            });
        }
    }).state(APP_ROUTES.EO.DIVISIONS_NEW, {
        url: '/new',
        onEnter: function($stateParams, $state, $uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.EO.FOLDER + 'divisions/create-division.html',
                controller: 'Event.DivisionContoller',
                resolve: {
                    mode: function () {
                        return 'create';
                    },
                    division        : function () { return null; }
                }
            }).result.then(function() {
                $state.transitionTo(APP_ROUTES.EO.DIVISIONS, null, {
                    reload: true,
                    inherit: true,
                    notify: true
                });
            }, function() {
                $state.go(APP_ROUTES.EO.DIVISIONS);
            });
        }
    }).state(APP_ROUTES.EO.DIVISIONS_CREATE_GR,  {
        url: '/create',
        onEnter: function ($stateParams, $state, $uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.EO.FOLDER + 'divisions/create-divisions-group.html',
                controller: 'Event.DivisionsWizardController',
                size: 'lg',
                resolve: {
                    loadEvent: function (eventDashboardService) {
                        var event = eventDashboardService.getEvent();

                        var genders = [];

                        if(event.has_male_teams) {
                            genders.push({ name: 'male' });
                        }

                        if(event.has_female_teams) {
                            genders.push({ name: 'female' });
                        }

                        if(event.has_coed_teams) {
                            genders.push({ name: 'coed' });
                        }

                        return {
                            event   : event,
                            genders : genders
                        };
                    }
                }
            }).result.then(function() {
                $state.transitionTo(APP_ROUTES.EO.DIVISIONS, null, {
                    reload  : true,
                    inherit : true,
                    notify  : true
                });
            }, function() {
                $state.go(APP_ROUTES.EO.DIVISIONS);
            });
        }
    }).state(APP_ROUTES.EO.PAYMENTS, {
        url: '/payments',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-payments.html',
                controller: 'EventPaymentsController'
            }
        },
        onEnter: ['$stateParams', '$location', function ($stateParams, $location) {
            let queryParams = $location.search();

            if(queryParams) {
                if(queryParams.purchase_id) {
                    $stateParams.openPaymentModal = { purchase_id: Number(queryParams.purchase_id) };
                }
            }
        }],
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.PAYMENTS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.TEAMS_TAB)
        },
        params: {
            openPaymentModal: null
        }
    }).state(APP_ROUTES.EO.OFFICIALS, {
        url: '/officials',
        params: {
            isChanged: null,
            filters: null
        },
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-officials.html',
                controller: 'EventOfficialsController'
            }
        },
        resolve: {
            loadEditor      : loadCKEditor,
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.OFFICIALS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.OFFICIALS_TAB)
        }
    }).state(APP_ROUTES.EO.STAFFERS, {
        url: '/staffers',
        params: {
            filters: null,
            isChanged: null,
        },
        views: {
            '': {
                template: '<event-staff></event-staff>'
            }
        },
        resolve: {
            loadEditor      : loadCKEditor, loadMomentTimezone,
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.STAFFERS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.STAFF_TAB)
        }
    }).state(APP_ROUTES.EO.OFFICIALS_INFO, {
        url: '/:official/info',
        onEnter: ['$state', 'MembersInfoService',  '$stateParams', 'OFFICIAL_MEMBER_TYPE',
        function ($state, MembersInfoService, $stateParams, OFFICIAL_MEMBER_TYPE) {
            var close = function (changed, filters) {
                if (changed) {
                    $state.go('^', { isChanged: changed, filters: filters }, { reload: true});
                } else {
                    $state.go('^', { isChanged: false, filters: filters });
                }
            };

            MembersInfoService.openInfoModal($stateParams.event, $stateParams.official, {
                member: OFFICIAL_MEMBER_TYPE,
            })
            .then( function (changed) {
                close(changed, $stateParams.filters);
            }, function (changed) {
                close(changed, $stateParams.filters);
            });
        }],
        resolve: {
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.OFFICIALS_TAB)
        }
    })

    .state(APP_ROUTES.EO.STAFFERS_INFO, {
        url: '/:staffer/info',
        onEnter: ['$state', 'MembersInfoService',  '$stateParams', 'STAFF_MEMBER_TYPE',
        function ($state, MembersInfoService, $stateParams, STAFF_MEMBER_TYPE) {
            const close = function(changed, filters) {
                if (changed) {
                    $state.go('^', { isChanged: changed, filters }, { reload: true });
                } else {
                    $state.go('^', { isChanged: false, filters });

                }
            };

            MembersInfoService.openInfoModal($stateParams.event, $stateParams.staffer, {
                member: STAFF_MEMBER_TYPE,
            })
                .then(changed => close(changed, $stateParams.filters), changed => close(changed, $stateParams.filters))
        }],
        resolve: {
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.STAFF_TAB)
        }
    })

    .state(APP_ROUTES.EO.EMAIL_MODULE, {
        url: '/email',
        views: {
            '': {
                template    :'<aem base-state="{{currentState}}"></aem>',
                controller  : ['$scope', '$stateParams', '$state' ,function ($scope, $stateParams, $state) {
                    $scope.eventID      = $stateParams.event;
                    $scope.currentState = $state.current.name;
                }]
            }
        },
        resolve: {
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.EMAIL_MODULE_TAB)
        }
    }).state(APP_ROUTES.EO.EMAIL_MODULE_TMPLS, {
        url         : '/templates',
        template    : '<aem-templates service="service"></aem-templates>',
        controller  : ['$scope', '$state', '$stateParams', 'AEMEventFactory', function ($scope, $state, $stateParams, AEMEventFactory) {
            $scope.service = new AEMEventFactory($stateParams.event);
        }],
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.EMAIL_MODULE_TMPLS),
            BeePlugin: ['lazyLoadService', function  (lazyLoadService) {
                return lazyLoadService.loadBEEPlugin();
            }],
            ReactEmailEditor: ['lazyLoadService', function  (lazyLoadService) {
                return lazyLoadService.loadReactEmailEditor();
            }]
        }
    }).state(APP_ROUTES.EO.EMAIL_MODULE_SEND, {
        url         : '/send',
        template    : '{{2+2}}'
    }).state(APP_ROUTES.EO.EMAIL_CONTACT_LISTS, {
        url         : '/contact-lists',
        template    : '<contact-lists></contact-lists>'
    }).state(APP_ROUTES.EO.EMAIL_CONTACT_LIST, {
        url         : '/contact-lists/:list/info',
        template    : '<contact-list-info></contact-list-info>'
    })

    .state(APP_ROUTES.EO.TEMPLATES_ADMIN, {
        url         : '/admin-templates',
        views: {
            'rootView': {
                template: '<aem-admin></aem-admin>'
            }
        },
        resolve: {
            BeePlugin: ['lazyLoadService', function  (lazyLoadService) {
                return lazyLoadService.loadBEEPlugin();
            }],
            ReactEmailEditor: ['lazyLoadService', function  (lazyLoadService) {
                return lazyLoadService.loadReactEmailEditor();
            }]
        }
    })
    .state(APP_ROUTES.EO.STAFF,  {
        url: '/staff',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'staff/staff.html',
                controller: 'Event.StaffController'
            }
        }
    })
    .state(APP_ROUTES.EO.ADD_TEAM, {
        url: '/new-team',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'new-team/new-doubles-team.html',
                controller: 'Event.NewDoublesTeamController'
            }
        },
        resolve: {
            loadMyService: loadCreditCardModules
        }
    }).state(APP_ROUTES.EO.WAITLIST, {
        url: '/waitinglist',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-teams.html',
                controller: 'eventTeamsController'
            }
        },
        resolve: {
            tabToOpen: function () {
                return {
                    value: -1
                }
            }
        }
    }).state(APP_ROUTES.EO.HISTORY,  {
        url: '/history',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'event-history.html',
                controller: 'EventHistoryController'
            }
        },
        resolve: {
            historyList: function ($q, $timeout, eventsService, $stateParams) {
                var defer = $q.defer();
                var timer = $timeout(function () {
                    defer.resolve([]);
                }, 10000);
                eventsService.history($stateParams.event, null, function (data, status) {
                    $timeout.cancel(timer);
                    if(status === 200)
                        defer.resolve(data.history);
                    else
                        defer.resolve([]);
                })
                return defer.promise;
            },
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.HISTORY),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.HISTORY_TAB)
        }
    })

    .state(APP_ROUTES.CD.PARENT, {
        url: '/club',
        views: {
            'rootView': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'club-owner-menu.html',
                controller: 'Club.MenuController'
            }
        },
        resolve: {
            masterClub: function() {
                return {
                    value: {}
                };
            },
            loadClub: function ($q, $timeout, masterClubService) {
                var defer = $q.defer(),
                    timer = $timeout(function () {
                        defer.resolve({});
                    }, 15000);
                masterClubService.getClub().then(function (resp) {
                    $timeout.cancel(timer);
                    defer.resolve(resp.data.club);
                }, function () {
                    $timeout.cancel(timer);
                });
                return defer.promise;
            }
        }
    }).state(APP_ROUTES.CD.INFO, {
        url: '/info',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'club-info.html',
                controller: 'ClubInfoController'
            }
        },
        resolve: {
            masterClub: function() {
                return {
                    value: {}
                };
            }
        }
    }).state(APP_ROUTES.CD.INFO_NEW, {
        url         : '/new',
        onEnter     : openClubProfile
    }).state(APP_ROUTES.CD.INFO_UPDATE, {
        url         : '/update',
        onEnter     : openClubProfile
    }).state(APP_ROUTES.CD.TEAMS, {
        url: '/teams',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'teams/club-teams.html',
                controller: 'ClubTeamsController'
            }
        },
        resolve: {
            masterClubId: function() {
                return {
                    value: -1
                };
            },
            teamsData: ['ClubTeamsService', function (ClubTeamsService) {
                return ClubTeamsService.teamsList().then(function (resp) {
                    return resp.data;
                });
            }],
        }
    }).state(APP_ROUTES.CD.TEAMS_ARCHIVE, {
        url: '/teams-archive',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'teams/teams-archive.html',
                controller: 'Club.Teams.ArchiveController',
                controllerAs: 'archive'
            }
        },
        resolve: {
            archive: function ($q, $timeout, ClubTeamsService) {
                var defer = $q.defer(),
                    timer = $timeout(function () {
                        defer.resolve([]);
                    }, 5000);
                ClubTeamsService.teamsList({
                    archived: true
                }).success(function (data) {
                    $timeout.cancel(timer);
                    defer.resolve(data);
                }).error(function () {
                    $timeout.cancel(timer);
                });
                return defer.promise;
            }
        }
    }).state(APP_ROUTES.CD.TEAMS_IMPORT, {
        url: '/import',
        onEnter: function ($stateParams, $state, $uibModal, lazyLoadService, $q) {
            $q.all([
                lazyLoadService.xslxLoader(),
                lazyLoadService.fileUpload()
            ]).then(function () {
                $uibModal.open({
                    templateUrl     : APP_ROUTES.CD.FOLDER + 'teams/import-teams.html',
                    controller      : 'ImportTeamsController'
                });
            });
        }
    }).state(APP_ROUTES.CD.ATHLETES, {
        url: '/players',
        views:{
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'athletes/v2/club-athletes.html',
                controller: 'Club.MasterAthletesController'
            }
        },
        params: {
            master_athlete_id: null
        }
    }).state(APP_ROUTES.CD.STAFF, {
        url: '/staff',
        views:{
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'staff/club-staff.html',
                controller: 'ClubStaffController'
            }
        }
    }).state(APP_ROUTES.CD.CLUB_INVOICES, {
        url: '/invoices',
        views:{
            '': {
                template: '<clubs-club-invoices></clubs-club-invoices>'
            }
        },
        resolve: {
            loadMyService: ['lazyLoadService', '$q', function (lazyLoadService, $q) {
                return $q.all([
                    lazyLoadService.loadCard(),
                    lazyLoadService.loadPlaid()
                ]);
            }],
        }
    }).state(APP_ROUTES.CD.STAFF_UPDATE, {
        url: '/:master_staff_id',
        onEnter: function($stateParams, $state, $uibModal, loadClub) {
            $uibModal.open({
                templateUrl: APP_ROUTES.CD.FOLDER + 'staff/edit-staff.html',
                controller: 'EditStaffController',
                resolve: {
                    loadClub
                }
            });
        }
    }).state(APP_ROUTES.CD.EVENTS, {
        url: '/events',
        views:{
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'events/club-events.html',
                controller: 'ClubEventsController'
            }
        }
    }).state(APP_ROUTES.CD.CLUB_EVENTS, {
        url: '/my-events',
        views:{
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'events/my-teams-events.html',
                controller: 'MyTeamsEventsController'
            }
        }
    }).state(APP_ROUTES.CD.CLUB_BULK_REGISTRATION, {
        url: '/registration',
        template: '<club-bulk-registration-page></club-bulk-registration-page>'
    }).state(APP_ROUTES.CD.CLUB_UNPAID_EVENTS, {
        url: '/unpaid-events',
        views:{
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'unpaid-events/unpaid-events.html',
                controller: 'UnpaidEventsController'
            }
        }
    }).state(APP_ROUTES.CD.CLUB_UNPAID_EVENTS_PAY, {
        url: '/:event/pay',
        onEnter: function($stateParams, $state, $uibModal) {
            var onClose = function () {
                $state.go('^');
            };

            $uibModal.open({
                template    : `
                    <overlay-with-spinner is-shown="blockModal"></overlay-with-spinner>
                    <modal-wrapper><teams-entry-payment></teams-entry-payment></modal-wrapper>
                `,
                backdrop    : false,
                controller  : ['$scope', function ($scope) {
                    $scope.modalTitle = '<h4 class="modal-title">New Payment</h4>';
                    $scope.modalShowClose = true;
                    $scope.blockModal = false;

                    $scope.$on('team.payment.in-progress', function () {
                        $scope.blockModal = true;
                    });

                    $scope.$on('team.payment.processed', function () {
                        $scope.blockModal = false;
                    });
                }],
            }).result.then(onClose, onClose);
        },
        resolve: {
            loadMyService: ['lazyLoadService', '$q', function (lazyLoadService, $q) {
                return $q.all([
                    lazyLoadService.loadCard(),
                    lazyLoadService.loadPlaid()
                ]);
            }],
            PaymentHub: ['lazyLoadService', function  (lazyLoadService) {
                return lazyLoadService.loadPaymentHub();
            }]
        }
    }).state(APP_ROUTES.CD.EVENTS_ASSIGN, {
        url: '/:event/assign',
        onEnter: function($stateParams, $state, $uibModal, loadClub) {
            $uibModal.open({
                templateUrl: APP_ROUTES.CD.FOLDER + 'events/assign-event.html',
                controller: 'AssignEventController',
                resolve: {
                    clubNotCompleted: loadClub && loadClub.not_completed
                }
            });
        }
    }).state(APP_ROUTES.CD.CLUB_EVENTS_ASSING, {
        url: '/:event/assign',
        onEnter: function ($state, $uibModal, loadClub) {
            $uibModal.open({
                templateUrl: APP_ROUTES.CD.FOLDER + 'events/assign-event.html',
                controller: 'AssignEventController',
                resolve: {
                    clubNotCompleted: loadClub && loadClub.not_completed
                }
            });
        }
    }).state(APP_ROUTES.CD.MANAGE_EVENT, {
        url: '/event/:event',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'event-managing/event-managing.html',
                controller: 'Club.Event.ManagingController'
            }
        },
        resolve: {
            eventData: function ($q, eventsService, $stateParams) {
                var defer = $q.defer();
                eventsService.getTackedEvent($stateParams.event, function(resp) {
                    if (resp.status === 200) {
                        defer.resolve(resp.data.event);
                    } else {
                        defer.reject(resp.data);
                    }
                });
                return defer.promise;
            }
        }
    }).state(APP_ROUTES.CD.MANAGE_EVENT_TEAMS, {
        url: '/teams',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'event-managing/assign-teams.html',
                controller: 'Club.Event.AssignTeamsController'
            }
        },
        resolve: {
            checkCDProfileCompleted: checkCDProfileCompleted,
        }
    }).state(APP_ROUTES.CD.MANAGE_EVENT_MEMBERS, {
        url: '/members',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'event-managing/members-list.html',
                controller: 'Club.Event.MembersList'
            }
        },
        resolve: {
            checkCDProfileCompleted: checkCDProfileCompleted,
            teamsList: function ($q, $timeout, ClubTeamsService, $stateParams) {
                var defer = $q.defer(),
                    timer = $timeout(function () {
                        defer.resolve([]);
                    }, 5000);
                ClubTeamsService.roster.teamsList($stateParams.event)
                .success(function (data) {
                    $timeout.cancel(timer);
                    var choosen;
                    if($stateParams.team) {
                        for(var i = 0, l = data.teams.length, t; i < l; ++i) {
                            t = data.teams[i]
                            if(t.roster_team_id === $stateParams.team) {
                                choosen = t;
                                break;
                            }
                        }
                    }
                    defer.resolve({
                        list    : data.teams,
                        choosen : choosen
                    });
                }).error(function (data) {
                    $timeout.cancel(timer);
                    defer.reject(data);
                })
                return defer.promise;
            }
        },
        params: {
            team: null,
            event: null
        }
    }).state(APP_ROUTES.CD.MANAGE_EVENT_INFO, {
        url: '/info',
        views: {
            '': {
                templateUrl: APP_ROUTES.CD.FOLDER + 'event-managing/event-info.html',
                controller: 'Club.Events.EventInfoController'
            }
        },
        resolve: {
            eventData: function (eventsService, $stateParams, $q) {
                var defer = $q.defer();
                eventsService.club_owner.getEventByIdWithLocation($stateParams.event, function(resp) {
                    defer.resolve(resp.data.event)
                });
                return defer.promise;
            }
        }
    }).state(APP_ROUTES.CD.MANAGE_EVENT_TEAMS_PAY, {
        url: '/pay',
        onEnter: function($stateParams, $state, $uibModal) {
            const onClose = function () {
                $state.go('^');
            };

            $uibModal.open({
                template    : `
                    <overlay-with-spinner is-shown="blockModal"></overlay-with-spinner>
                    <modal-wrapper><teams-entry-payment></teams-entry-payment></modal-wrapper>
                `,
                backdrop    : false,
                controller  : ['$scope', function ($scope) {
                    $scope.modalTitle = '<h4 class="modal-title">New Payment</h4>';
                    $scope.modalShowClose = true;
                    $scope.blockModal = false;

                    $scope.$on('team.payment.in-progress', function () {
                        $scope.blockModal = true;
                    });

                    $scope.$on('team.payment.processed', function () {
                        $scope.blockModal = false;
                    });
                }]
            }).result.then(onClose, onClose);
        },
        resolve: {
            loadMyService: ['lazyLoadService', '$q', function (lazyLoadService, $q) {
                return $q.all([
                    lazyLoadService.loadCard(),
                    lazyLoadService.loadPlaid()
                ]);
            }],
            PaymentHub: ['lazyLoadService', function  (lazyLoadService) {
                return lazyLoadService.loadPaymentHub();
            }]
        }
    }).state(APP_ROUTES.CD.MANAGE_EVENT_CHECKIN, {
        url: '/checkin',
        views: {
            '': {
                template: '<club-checkin event="$parent.event"></club-checkin>'
            }
        },
        resolve: {
            checkCDProfileCompleted: checkCDProfileCompleted
        }
    }).state(APP_ROUTES.CD.PAYMENT_CARD, {
        url: '/payment-card',
        resolve: {
            loadMyService: loadCreditCardModules,
        },
        onEnter: ['CDPaymentCardService', '$state', '$stateParams', '$location',
            function (CDPaymentCardService, $state, $stateParams, $location) {

                let close = _onClose.bind(null, $state);

                CDPaymentCardService.openPaymentCardModal()
                    .then(close, close);
            }],
        params: {
            active_tab: null
        }
    })

    .state(APP_ROUTES.SM.PARENT, {
        url: '/sales',
        views: {
            'rootView': {
                templateUrl: APP_ROUTES.SM.FOLDER + 'index.html',
                controller: 'SalesController'
            }
        },
    }).state(APP_ROUTES.SM.EVENTS, {
        url: '/events',
        templateUrl: APP_ROUTES.SM.FOLDER + 'sales-events-list.html',
        controller: 'SalesEventsListController'
    }).state(APP_ROUTES.SM.EVENT_EXHIBITORS, {
        url: '/event/:event/exhibitors',
        template: '<exhibitors></exhibitors>',
        resolve: {
            loadMyService: loadCreditCardModules,
        },
        onEnter: function($rootScope, $stateParams, $timeout) {
            $timeout(() => {
                $rootScope.$broadcast('sales.event', $stateParams.event);
            }, 0);
        },
        params: {
            userExhibitorsState: APP_ROUTES.SM.EVENT_USER_EXHIBITORS,
            paymentsListState: APP_ROUTES.SM.EVENT_EXHIBITORS_PAYMENTS,
        },
    }).state(APP_ROUTES.SM.EVENT_USER_EXHIBITORS,  {
        url: '/event/:event/user-exhibitors',
        template: '<user-exhibitors></user-exhibitors>',
        resolve: {
            loadMyService: loadCreditCardModules,
        },
        onEnter: function($rootScope, $stateParams, $timeout) {
            $timeout(() => {
                $rootScope.$broadcast('sales.event', $stateParams.event);
            }, 0);
        },
        params: {
            exhibitorsState: APP_ROUTES.SM.EVENT_EXHIBITORS,
        },
    }).state(APP_ROUTES.SM.EVENT_EXHIBITORS_PAYMENTS, {
        url: '/event/:event/exhibitors-payments',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'exhibitors/payments/exhibitors-payments.html',
                controller: 'events.ExhibitorsPaymentsController'
            }
        },
        resolve: {
            loadMyService: loadCreditCardModules,
        },
        onEnter: function($rootScope, $stateParams, $timeout) {
            $timeout(() => {
                $rootScope.$broadcast('sales.event', $stateParams.event);
            }, 0);
        },
        params: {
            showStripeStatistic: null,
            filters: null,
        }
    }).state(APP_ROUTES.SM.EVENT_BOOTHS, {
        url: '/event/:event/booths',
        views: {
            '': {
                templateUrl: APP_ROUTES.EO.FOLDER + 'exhibitors/booths/booths.html',
                controller: 'events.ExhibitorsController'
            }
        },
        onEnter: function($rootScope, $stateParams, $timeout) {
            $timeout(() => {
                $rootScope.$broadcast('sales.event', $stateParams.event);
            }, 0);
        },
    }).state(APP_ROUTES.SM.EVENT_EXHIBITORS_TICKETS, {
        url: '/event/:event/exhibitors-tickets',
        views: {
            '': {
                templateUrl: APP_ROUTES.SM.FOLDER + 'tickets/exhibitors/exhibitors-tickets.html',
                controller: 'sales.ExhibitorsTicketsController',
                controllerAs: 'data'
            }
        },
        resolve: {
            exhibitorsTickets
        },
        onEnter: function($rootScope, $stateParams, $timeout) {
            $timeout(() => {
                $rootScope.$broadcast('sales.event', $stateParams.event);
            }, 0);
        }
    }).state(APP_ROUTES.SM.EXHIBITORS, {
        url: '/exhibitors',
        views: {
            '': {
                templateUrl: APP_ROUTES.SM.FOLDER + 'exhibitors.html',
                controller: 'ExhibitorsController'
            }
        },
        resolve: {
            loadData: function ($http) {
                // TODO: move to service
                return $http.get('/api/sales/sponsors')
                .then(function (resp) {
                    return resp.data && resp.data.sponsors || [];
                });
            }
        }
    }).state(APP_ROUTES.SM.EXHIBITORS_INFO, {
        url: '/:exhibitor/info',
        onEnter: function ($state, $uibModal) {
            $uibModal.open({
                templateUrl     : APP_ROUTES.EX.FOLDER + 'info.html',
                controller      : 'InfoExhibitorController'
            }).result.finally(function () {
                $state.transitionTo(APP_ROUTES.SM.EXHIBITORS, null, {
                    reload  : false,
                    inherit : true,
                    notify  : true
                });
            });
        }
    }).state(APP_ROUTES.SM.EXHIBITORS_CREATE, {
        url: '/new',
        onEnter: function($state, $uibModal) {
            $uibModal.open({
                templateUrl     : APP_ROUTES.EX.FOLDER + 'edit-profile.html',
                controller      : 'CreateExhibitorController'
            }).result.then(function() {
                $state.transitionTo(APP_ROUTES.SM.EXHIBITORS, null, {
                    reload      : true,
                    inherit     : true,
                    notify      : true
                });
            }, function() {
                $state.go('^');
            });
        }
    }).state(APP_ROUTES.SM.EXHIBITORS_UPDATE, {
        url: '/update/:exhibitor',
        onEnter: function ($state, $uibModal) {
            openExhibitorProfile($uibModal)
            .then(function () {
                $state.transitionTo(APP_ROUTES.SM.EXHIBITORS, null, {
                    reload  : true,
                    inherit : true,
                    notify  : true
                });
            }, function () {
                $state.go(APP_ROUTES.SM.EXHIBITORS);
            });
        }
    }).state(APP_ROUTES.SM.INVOICES, {
        url: '/invoices',
        params: {
            filters: null,
        },
        views: {
            '': {
                templateUrl: APP_ROUTES.SM.FOLDER + 'receipts.html',
                controller: 'ReceiptsController'
            }
        },
        resolve: {
            loadMyService: loadCreditCardModules,
        },
    }).state(APP_ROUTES.SM.REPORT, {
        url: '/report',
        views: {
            '': {
                templateUrl: APP_ROUTES.SM.FOLDER + 'reports.html',
                controller: 'ReportsController'
            }
        }
    }).state(APP_ROUTES.SM.REPORT_DETAILS, {
        url: '/event/:event',
        onEnter: function($stateParams, $state, $uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.SM.FOLDER + 'detailed-report.html',
                controller: 'DetailedReportController'
            })
            .result.finally(function () {
                $state.go(APP_ROUTES.SM.REPORT);
            });
        }
    })

    .state(APP_ROUTES.EX.PARENT, {
        url: '/exhibitor',
        views: {
            'rootView': {
                templateUrl: APP_ROUTES.EX.FOLDER + 'index.html',
                controller: 'Exhibitor.IndexController'
            }
        }
    }).state(APP_ROUTES.EX.PROFILE, {
        url: '/profile',
        views: {
            '': {
                templateUrl: APP_ROUTES.EX.FOLDER + 'profile.html',
                controller: 'Exhibitor.ProfileController'
            }
        }
    }).state(APP_ROUTES.EX.PROFILE_CREATE, {
        url: '/create',
        onEnter: function ($state, $uibModal) {
            $uibModal.open({
                templateUrl: APP_ROUTES.EX.FOLDER + 'edit-profile.html',
                controller: 'Exhibitor.ChangeProfileCtrl'
            }).result.then(function () {
                $state.transitionTo(APP_ROUTES.EX.PROFILE, null, {
                    reload  : true,
                    inherit : true,
                    notify  : true
                });
            }, function () {
                $state.go(APP_ROUTES.EX.PROFILE);
            });
        }
    }).state(APP_ROUTES.EX.PROFILE_UPDATE, {
        url: '/update',
        onEnter: function($stateParams, $state, $uibModal) {
            openExhibitorProfile($uibModal)
            .then(function() {
                $state.transitionTo(APP_ROUTES.EX.PROFILE, null, {
                    reload      : true,
                    inherit     : true,
                    notify      : true
                });
            }, function() {
                $state.go(APP_ROUTES.EX.PROFILE);
            });
        }
    }).state(APP_ROUTES.EX.RECEIPTS, {
        url: '/receipts',
        params: {
            filters: null,
        },
        views: {
            '': {
                 template: '<exhibitor-receipts></exhibitor-receipts>'
            }
        },
        resolve: {
            loadMyService: loadCreditCardModules,
        },
    })
    .state(APP_ROUTES.EX.EVENTS, {
        url: '/events',
        template: '<exhibitor-events-list></exhibitor-events-list>',
    })

    .state(APP_ROUTES.D.PARENT, {
        url: '/assign/:event/doubles',
        views: {
            'rootView': {
                templateUrl: APP_ROUTES.D.FOLDER + 'assign-doubles.html',
                controller: 'Doubles.AssignController'
            }
        }
    }).state(APP_ROUTES.D.TYPE, {
        url: '/type',
        views: {
            '': {
                templateUrl: APP_ROUTES.D.FOLDER + 'team-ages.html',
                controller: 'Doubles.TeamAgesController'
            }
        }
    }).state(APP_ROUTES.D.REGISTER, {
        url: '/register',
        views: {
            '': {
                 templateUrl: APP_ROUTES.D.FOLDER + 'register-doubles.html',
                 controller: 'Doubles.RegisterController'
            }
        }
    }).state(APP_ROUTES.SUPPORT.PARENT, {
        abstract: true,
        url: '/support',
        views: {
            'rootView': {
                template: '<ui-view/>'
            }
        }
    }).state(APP_ROUTES.SUPPORT.FAQ, {
        url             : '/faq',
        templateUrl     : APP_ROUTES.SUPPORT.FOLDER + 'faq/faq.html',
        controller      : ['$scope', function ($scope) {
            $scope.routes = { page: APP_ROUTES.FAQ_DETAILS_PAGE };
        }]
    }).state(APP_ROUTES.SUPPORT.ASK, {
        url             : '/ask-a-question',
        templateUrl     : APP_ROUTES.SUPPORT.FOLDER + 'question/question.html',
        controller      : 'questionController'
    })

    .state(APP_ROUTES.FAQ_DETAILS, {
        abstract    : true,
        url         : '/faq-details',
        views       : {
            'rootView': {
                template: '<ui-view/>'
            }
        }
    }).state(APP_ROUTES.FAQ_DETAILS_PAGE, {
        url         : '/:faqPageName',
        templateUrl : APP_ROUTES.SUPPORT.FOLDER + 'faq/details/faq-details.html',
        controller  : 'faqDetailsController'
    })

    .state(APP_ROUTES.HOUSING_EVENTS, {
        url: '/housing/events',
        views: {
            'rootView': {
                templateUrl: 'public/events/event.html',
                controller: 'Housing.Events.EventController'
            }
        }
    }).state(APP_ROUTES.HOUSING_TEAMS, {
        url: '/housing/event/:event/teams',
        views: {
            'rootView': {
                templateUrl: 'public/events/teams.html',
                controller: 'Housing.Events.TeamsController'
            }
        }
    })

    .state(APP_ROUTES.ACTIVATION, {
        url: '/activation?code',
        views: {
            'rootView': {
                templateUrl: 'homepage/homepage.html',
                controller: 'activateController'
            }
        }
    })

    .state(APP_ROUTES.ACCOUNT, {
        url: '/user',
        views: {
            'rootView': {
                template: '<user-reg mode="update"></user-reg>'
            }
        }
    })

    .state(APP_ROUTES.EVENT_INFO, {
        url: '/public/events/:event',
        views: {
            'rootView': {
                templateUrl: 'event-info.html',
                controller: 'eventInfoController'
            }
        }
    }).state(APP_ROUTES.FORGOT_PSWD, {
        url         : 'password',
        onEnter     : ['$uibModal', '$state', function ($uibModal, $state) {
            var onClose = function () {
                $state.go('^');
            };

            $uibModal.open({
                template        : '<modal-wrapper><recover-pswd></recover-pswd></modal-wrapper>',
                controller      : ['$scope', function ($scope) {
                    $scope.modalTitle = '<h2 class="text-info">Password Recovery</h2>';
                }]
            }).result.then(onClose, onClose);
        }]
    }).state(APP_ROUTES.TERMS, {
        url: '/terms',
        views: {
            'rootView': {
                templateUrl: 'terms.html'
            }
        }
    }).state(APP_ROUTES.PRIVACY, {
        url: '/privacy',
        views: {
            'rootView': {
                templateUrl: 'privacy.html'
            }
        }
    }).state(APP_ROUTES.RECOVERY, {
        url: '/recovery?code',
        views: {
            'rootView': {
                templateUrl: 'user/password-recovery.html',
                controller: 'PasswordRecoveryController'
            }
        }
    }).state(APP_ROUTES.NO_CONNECT, {
        url: '/noconnect',
        views: {
            'rootView': {
                template: '<p class="text-center">Connection lost. Please try again in a few minutes.</p>',
                controller: function () {}
            }
        }
    }).state(APP_ROUTES.TICKETS_LIST, {
        url: '/tickets',
        views: {
            'rootView': {
                template: '<tickets-list></tickets-list>'
            }
        }
    })

    .state(APP_ROUTES.ES.PARENT, {
        url         : '/event-supervisor',
        views: {
            'rootView': {
                template: '<event-supervisor></event-supervisor>'
            }
        },
    })

    .state(APP_ROUTES.UA.PARENT, {
        abstract    : true,
        url         : '/usav',
        views: {
            'rootView': {
                template: '<ui-view/>'
            }
        },
    }).state(APP_ROUTES.UA.EVENTS, {
        url         : '/events',
        template: '<usav-admin></usav-admin>'
    }).state(APP_ROUTES.UA.DASHBOARD, {
        url         : '/:event/dashboard',
        views: {
            '': {
                template: '<usav-admin-dashboard></usav-admin-dashboard>'
            }
        },
    }).state(APP_ROUTES.EO.OFFICIALS_PAYOUTS, {
        url: '/officials-payouts',
        template: '<officials-payouts></officials-payouts>',
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.OFFICIALS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.OFFICIALS_TAB)
        }
    }).state(APP_ROUTES.EO.STAFF_PAYOUTS, {
        url: '/staff-payouts',
        template: '<staff-payouts></staff-payouts>',
        resolve: {
            checkIsTabAvailable: checkIsTabAvailable(APP_ROUTES.EO.STAFFERS),
            checkPermissions: checkRoutePermissions(EVENT_OPERATIONS.STAFF_TAB)
        }
    })
});
