angular.module('SportWrench').controller('EventCheckInController', EventCheckInController);

function EventCheckInController (
    $scope, $stateParams, divisionsService, _, $timeout, CheckinStatusFactory,
    AllTeamsService, eventDashboardService, ngTableParams, CheckInDynamicColsFactory, 
    OnlineCheckinStatusFactory, $filter, AEMSendDialogService
) {
    var event_id = $scope.event_id = $stateParams.event;
    var event            = eventDashboardService.getEvent();
    var _teams_to_change = [];

    $scope.teams = [];
    $scope.defaultSort   = false;
    $scope.search = '';

    $scope.data = {
        divisions                   : [],
        checkin_statuses            : CheckinStatusFactory.getItems(),
        online_checkin_statuses     : OnlineCheckinStatusFactory.getItems()
    };

    $scope.filters = {
        limit: 100,
        page : 1
    };

    var getData = function ($defer, params) {

        var orderBy = params.orderBy();

        var urlQuery = {};

        var filter = params.filter();

        if (filter) {
            urlQuery = _.clone(filter);
        }

        if (orderBy && orderBy.length && !$scope.defaultSort) {
            urlQuery.order     = orderBy[0].substr(1);
            urlQuery.direction = (orderBy[0].charAt(0) === '+') ? 'asc' : 'desc';
        }

        AllTeamsService.getTeamsForCheckIn(event_id, urlQuery)
            .then(function (resp) {
                if (resp.data && resp.data.teams && resp.data.teams.length) {

                    $scope.teams = resp.data.teams;
                    $scope.total = resp.data.teams[0].count;

                    $defer.resolve(resp.data.teams);

                } else {
                    $scope.teams = [];
                    $scope.total = 0;
                    $defer.resolve([]);
                }

                $scope.utils.selected_teams_count = 0;
                $scope.utils.all_selected = false;
                $scope.utils.loaded = true;
                $scope.defaultSort  = false;

                $timeout(function () {
                    isLoadingFinished = true;
                }, 100);

            }, function () {
                $scope.total = 0;
                params.total(0);
                $scope.teams = [];
                $defer.resolve([]);
            });
    };

    $scope.checkin_table = new ngTableParams({
        page        : 1,
        count       : 1,
        filter      : $scope.filters,
        sorting     : { 'team_name': 'asc' }

    }, {
        filterDelay: 0,
        getData    : getData
    });

    var checkInTable = $scope.checkin_table;

    $scope.dynamic_cols = CheckInDynamicColsFactory.getColumns();

    $scope.columnClass = function (c) {
        var stylesClasses = {
            'text-center sortable': true,
            'sort-asc'            : checkInTable.isSortBy(c.sortable, 'asc'),
            'sort-desc'           : checkInTable.isSortBy(c.sortable, 'desc')
        };
        if (c.selectors) {
            stylesClasses[c.selectors] = true;
        }
        return stylesClasses;
    };

    $scope.sort = function (column) {
        if (column) {
            $scope.filters.page = 1;
            checkInTable.sorting(column.name, checkInTable.isSortBy(column.name, 'asc') ? 'desc'
                : checkInTable.isSortBy(column.name, 'desc') ? setSortDefault() : 'asc');
        }
    };

    function setSortDefault() {
        $scope.defaultSort = true;
        return 'text-center sortable';
    };

    var isLoadingFinished = true;

    $scope.loadMore = function () {
        var isLoading = checkInTable.settings().$loading,
            maxItems  = $scope.filters.limit * $scope.filters.page;

        if (!isLoadingFinished || isLoading || $scope.teams.length < maxItems) {
            return;
        }

        isLoadingFinished = false;

        ++$scope.filters.page;
    };

    var genderIcons = { 'male': 'fa-male', 'female': 'fa-female', coed: 'fa-male fa-female' };
    function getGenderIcon (gender) {
        return genderIcons[gender];
    }

    function loadDivisions () {
        divisionsService.getDivisions(event_id, { sort: true })
            .then(function (divisions) {
                $scope.data.divisions = _.map(divisions, function (d) {

                    d.class         = 'fa ' + getGenderIcon(d.gender);
                    d.id            = d.division_id;
                    d.item_class    = d.gender;
                    d.short         = d.short_name;

                    return d;
                });

                if($scope.filters.division && $scope.filters.division[0]) {
                    $scope.utils.division = findPickedDivision($scope.filters.division[0]);
                }
            });
    }

    $scope.filterDivision   = function (selection) {
        if(selection.length === 1) {
            $scope.utils.division = findPickedDivision(selection[0]);
        } else {
            $scope.utils.division = null;
        }

        $scope.filterStatus('division', selection);
    };

    function findPickedDivision (id) {
        var divisions = $scope.data.divisions;

        for(var i = 0, d; i < divisions.length; ++i) {

            d = divisions[i];

            if(d.id == id) {
                return d;
            }
        }

        return null;
    }

    $scope.filterStatus = function (status, selection) {
        console.log('filter status', status, selection);
        $scope.filters[status] = selection;
        $scope.filters.page    = 1;
    };

    $scope.filterSearch     = function () {
        if(checkInTable.settings().$loading) return;

        $scope.filters.search   = $scope.search;
        $scope.filters.page     = 1;
    };

    $scope.filterCheckin          = $scope.filterStatus.bind(null, 'checkin');
    $scope.filterOnlineCheckin    = $scope.filterStatus.bind(null, 'online_checkin');

    var clearHandlers = [];

    $scope.addClearFilterHandler = function (handler) {
        clearHandlers.push(handler);
    };

    $scope.clearFilters = function () {
        clearHandlers.forEach(function (handler) {
            if (_.isFunction(handler)) {
                handler();
            }
        });

        $scope.filters.search         = undefined;
        $scope.filters.checkin        = undefined;
        $scope.filters.online_checkin = undefined;

        $scope.utils.division = null;
    };

    loadDivisions();

    $scope.utils = {
        all_selected: false,
        selected_teams_count: 0
    };

    $scope.teamsNotes = function (t) {
        if(!t.notes || t.notes.length === 0) return '';
        var notes = '';
        for(var i = 0; i < t.notes.length; ++i) {
            notes += '<div class="row">' +
                        '<div class="col-sm-10" style="text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">'
                        + t.notes[i].note +
                        '</div>' +
                        '<div class="col-sm-1">'
                            + getStausUI(t.notes[i].status) +
                        '</div>' +
                    '</div>';
        }
        return notes;
    };

    function getStausUI (status) {
        if(status === 'checkedin')
            return '<span class="gl-success fa fa-check"></span>';
        if(status === 'notcheckedin')
            return '<span class="gl-danger fa fa-times"></span>';
        if(status === 'pending')
            return '<span class="gl-info fa fa-clock-o"></span>';
        if(status === 'alert')
            return '<span class="gl-danger fa fa-exclamation-circle"></span>';
        return '';
    }

    $scope.select_team = function (t) {
        if(t.checked) 
            $scope.utils.selected_teams_count++;
        else {
            if ($scope.utils.selected_teams_count > 0) {
                $scope.utils.selected_teams_count--;
            }
        }

        if($scope.utils.selected_teams_count == $scope.teams.length)
            $scope.utils.all_selected = true;
        else if ($scope.utils.selected_teams_count === 0)
            $scope.utils.all_selected = false;
    };

    $scope.teamsContacts = function (t, staff) {
        if(!staff) return 'No data';
        var template = '';
        var telFilter = $filter('tel');

        if(t.director_phone && !staff[0].staff_type) {
            template +=  t.director_first + ' ' + t.director_last + '<br/>';
            template += '<b>Club Director</b><br/>';
            template += '<b>Cell:</b> ' + telFilter(t.director_phone) + '<hr/>';
        }

        for(var i = 0; i < staff.length; ++i) {
            if(!staff[i].staff_type)
                template += staff[i].first + ' ' + staff[i].last + '<br/>';
            if(staff[i].role_name)
                template += '<b>' + staff[i].role_name + '</b><br/>';
            if(staff[i].staff_type) {
                var staffType = staff[i].staff_type === 1 ? 'E-signed by' : 'Additional Person Approved<br> to Pick Up Wristbands:';
                template += staffType + '<br/>';
                template += '<b>' +staff[i].first + ' ' + staff[i].last + '</b><br/>';
            }
            if(staff[i].phone)
                template += '<b>Cell: </b>' + telFilter(staff[i].phone) + '<br/>';
            if(staff[i].phonew)
                template += '<b>Work: </b>' + telFilter(staff[i].phonew) + '<br/>';
            if(staff[i].phoneh)
                template += '<b>Home: </b>' + telFilter(staff[i].phoneh) + '<br/>';
            if(staff[i].phoneo)
                template += '<b>Other: </b>' + telFilter(staff[i].phoneo) + '<br/>';
            if(staff[i].email)
                template += '<a href="mailto:' + staff[i].email + '">' + staff[i].email + '</a><br/>';
            if(staff[i].checkin_description_link)
                template += '<a href="' + _.escape(staff[i].checkin_description_link) + '" target="_blank">QR-Code</a><br/>';
            if(staff[i+1])
                template += '<hr/>';
        }
        return template;
    };


    $scope.openCheckInModal = function (t) {        
        if(t) {
            _teams_to_change = [t];
        } else {
            _teams_to_change = [];
            for(var i = 0; i < $scope.teams.length; ++i)
                if($scope.teams[i].checked)
                    _teams_to_change.push($scope.teams[i]);
        }
        _checkin_modal();
    };

    $scope.openEmailModal = function () {
        AEMSendDialogService.openDialog({
            eventID: event_id, 
            filters: { teams: $scope.getSelectedTeamsIds() },
            replyTo: event.email,
            group  : 'clubs',
            showRecipients : true
        }).catch(function (err) {
            if (err instanceof Error) {
                console.error(err);
            }
        });
    };

    function _findTeamsForCheckIn () {
        var teamsIds = [];

        _.each(_teams_to_change, function (team) {
            teamsIds.push(team.roster_team_id);
        });

        return AllTeamsService.getCurrentTeamsDataForCheckin(event_id, {teams: teamsIds})
            .then(function (resp) {
                if (resp.data && resp.data.teams && resp.data.teams.length) {
                    var data = resp.data.teams;

                    if (data.length && data.length === _teams_to_change.length) {
                        _.each(_teams_to_change, function (teamToChange) {
                            _.each(data, function (team) {
                                if (teamToChange.roster_team_id === team.roster_team_id) {
                                    teamToChange.status_checkin     = team.status_checkin;
                                    teamToChange.team_alert_note    = team.team_alert_note;
                                }
                            })
                        })
                    };
                }
                return _teams_to_change;

            }, function (err) {
                return _teams_to_change;
            });
    }

    function _checkin_modal () {
        _findTeamsForCheckIn().then(function (result) {

            AllTeamsService.showCheckInModal(event_id, result)
                .then(function (updTeams) {
                    if (updTeams && updTeams.length) {
                        synchStatuses(updTeams);
                    }
                });

        });
    }

    function synchStatuses (teams) {
        $scope.teams.forEach(function (filtTeam) {
            teams.forEach(function (updTeam) {
                if (updTeam.roster_team_id === filtTeam.roster_team_id) {
                    filtTeam.status_checkin = updTeam.status_checkin;
                    filtTeam.checkin_date = updTeam.checkin_date;
                    filtTeam.checked = false;
                    $scope.select_team(filtTeam);
                }
            });
        });
    }

    $scope.showTeamInfo = function(id, _tab) {
        // TODO: move to AllTeamsService
        $scope.$parent.showTeamInfoModal(id, _tab);
    };

    $scope.getSelectedTeamsIds = function () {
        return _.reduce($scope.teams, function (list, team) {
            if (team.checked) {
                list.push(team.roster_team_id);
            }
            return list;
        }, []);
    };

    $scope.select_all = function () {
        if(!$scope.utils.all_selected)
            $scope.utils.selected_teams_count = 0;
        else $scope.utils.selected_teams_count = $scope.teams.length;
        for(var i = 0; i < $scope.teams.length; ++i) {
            $scope.teams[i].checked = $scope.utils.all_selected;
        }
    };


    $scope.printRosters = function () {
        AllTeamsService.printRosters(event_id, { teams: $scope.getSelectedTeamsIds() });
    };
}
