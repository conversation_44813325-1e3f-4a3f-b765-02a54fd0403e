angular.module('SportWrench').directive('statementDescriptorValidator', function () {
    const NUMBER_REG_EXP = new RegExp('^[0-9]+$');
    const ALLOWED_LETTERS = new RegExp('^[a-zA-Z0-9()_\\-!#$%^&,.|\\][]+$');
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.forbiddenLetters = function (modelValue) {
                if(typeof modelValue === 'string') {
                    const containsForbiddenLetters = !ALLOWED_LETTERS.test(modelValue);
                    const containsNumberOnly = NUMBER_REG_EXP.test(modelValue);

                    return !(containsForbiddenLetters || containsNumberOnly);
                }

                return true;
            }
        }
    }
})
