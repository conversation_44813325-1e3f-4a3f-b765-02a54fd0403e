<form name="detailsForm" class="form-horizontal">
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && detailsForm.long_name.$invalid) }">
            <label class="col-sm-3 control-label">Event Name</label>
            <div class="col-sm-7">
                  <input
                    type="text" 
                    class="form-control" 
                    placeholder="Event Name ..."
                    name="long_name"
                    ng-model="tournament.long_name"
                    required>
            </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && detailsForm.name.$invalid) }">
            <label class="col-sm-3 control-label">Short Name</label>
            <div class="col-sm-7">
                  <input
                    type="text" 
                    class="form-control" 
                    placeholder="Short Event Name ..."
                    name="name"
                    ng-model="tournament.name"
                    required
                    maxlength="{{MAX_EVENT_SHORT_NAME_LENGTH}}">
                <p class="help-block">Maximum of {{MAX_EVENT_SHORT_NAME_LENGTH}} characters</p>
            </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && validateEventPurpose()) }">
            <label class="col-sm-3 control-label">Event will be using SW for</label>
            <div class="col-sm-7">
                  <div class="checkbox">           
                        <label>                        
                              <input
                                type="checkbox" 
                                ng-model="tournament.allow_teams_registration"
                                name="allow_teams_registration"                            
                                > Teams
                        </label>
                  </div>
                  <div class="checkbox">
                        <label>                           
                              <input
                                type="checkbox" 
                                name="allow_ticket_sales"
                                ng-model="tournament.allow_ticket_sales"
                                > Ticket Sales
                        </label>
                  </div> 
                  <div class="checkbox">           
                        <label>                           
                              <input 
                                type="checkbox" 
                                name="has_officials"
                                ng-model="tournament.has_officials"
                                > Online Officials Registration
                        </label>
                  </div>
                  <div class="checkbox">
                      <label>
                          <input
                              type="checkbox"
                              name="has_staff"
                              ng-model="tournament.has_staff"
                          > Online Staff Registration
                      </label>
                  </div>
                  <div class="checkbox">
                      <label>
                          <input
                              type="checkbox"
                              name="has_exhibitors"
                              ng-model="tournament.has_exhibitors"
                          > Online Exhibitors Registration
                      </label>
                  </div>
                <form-error-block ng-if="utils.formSubmitted && validateEventPurpose()" message="Choose at least one value"></form-error-block>
            </div>
    </div>
    <div class="form-group" ng-if="showHousingBox()">
        <label class="col-sm-3 control-label no-pd-top">This Event uses Online Teams Check-in</label>
        <div class="col-sm-2">         
            <label>
                <input type="checkbox" ng-model="tournament.online_team_checkin_available">
            </label>      
        </div>
    </div>
    <div class="form-group" ng-if="showHousingBox()">
        <label class="col-sm-3 control-label no-pd-top">This Event uses a housing company</label>
        <div class="col-sm-2">         
            <label>
                <input type="checkbox" ng-model="tournament.has_status_housing">
            </label>      
        </div>
    </div>
    <fieldset ng-if="tournament.allow_teams_registration">
        <div class="form-group">
            <label class="col-sm-3 control-label no-pd-top">Print Match Barcode on Score Sheet</label>
            <div class="col-sm-4">
                <label>
                    <input 
                        type="checkbox"
                        ng-model="tournament.has_match_barcodes"
                        name="print_barcodes">
                </label>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label no-pd-top">Schedule Published</label>
            <div class="col-sm-1">
                <input 
                    type="checkbox" 
                    ng-model="tournament.schedule_published"
                    name="schedule_published"
                    ng-disabled="!tournament.has_matches"
                    >
            </div>
            <div class="col-sm-7" ng-if="tournament.event_id">
                <a href="{{tournament.esw_link}}" class="btn btn-default" target="_blank"><i class="fa fa-table"></i> ESW Schedule Link</a>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label no-pd-top">This event uses Baller TV</label>
            <div class="col-sm-4">
                <label>
                    <input
                        type="checkbox"
                        ng-model="tournament.teams_settings.baller_tv_available"
                        name="baller_tv_available">
                </label>
            </div>
        </div>
    </fieldset>
    <div class="form-group" ng-if="showTeamsSortBy()">
        <label class="col-sm-3 control-label no-pd-top">Show teams on Pool & Brackets page</label>
        <div class="col-sm-2 radio">
            <label>
                <input type="radio" name="teams_sort_by" value="seed_current" ng-model="tournament.teams_settings.sort_by">
                Seed order
            </label>
        </div>
        <div class="col-sm-2 radio">
            <label>
                <input type="radio" name="teams_sort_by" value="team_name" ng-model="tournament.teams_settings.sort_by">
                Alphabetically
            </label>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && detailsForm.timezone.$invalid) }">
        <label class="col-sm-3 control-label">Event Time Zone</label>
        <div class="col-sm-4">
              <select class="form-control" name="timezone" ng-model="tournament.timezone" required>
                    <option value="" selected>Choose a Timezone ...</option>
                    <option value="America/New_York">America/New_York</option>
                    <option value="America/Los_Angeles">America/Los_Angeles</option>
                    <option value="America/Chicago">America/Chicago</option>
                    <option value="America/Detroit">America/Detroit</option>
                    <option value="America/Phoenix">America/Phoenix</option>
                    <option value="America/Indiana/Indianapolis">America/Indiana/Indianapolis</option>
                    <option value="America/Denver">America/Denver</option>
                    <option value="Pacific/Honolulu">Hawaii</option>
              </select>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && detailsForm.sport.$invalid) }">
        <label class="col-sm-3 control-label">Sport</label>
        <div class="col-sm-4">
              <select class="form-control" name="sport" ng-model="tournament.sport_id" ng-change="loadSportInfo()" required>
                    <option value="" selected="">Choose Sport ...</option>
                    <option value="2">Volleyball</option>
                    <option value="4">Basketball</option>
                    <option value="6">Softball</option>
                    <option value="8">Field Hockey</option>
                    <option value="10">Soccer</option>                        
                    <option value="12">Track & Field</option>
                    <option value="14">Drone Racing</option>
              </select>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && detailsForm.sport_variation.$invalid) }">
        <label class="col-sm-3 control-label">Sport Variation</label>
        <div class="col-sm-4">
             <spinner active="utils.variationsLoading"></spinner>
              <select
                ng-model="tournament.sport_variation_id" 
                class="form-control" 
                name="sport_variation"
                ng-options="v.id as v.name for v in sportInfo.variations"
                ng-show="!utils.variationsLoading" 
                ng-change="sportVariationChanged()"
                required>
                    <option value="" selected>Choose Sport Variation ...</option>
              </select>
        </div>             
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (detailsForm.date_start.$invalid) }">
        <label class="col-sm-3 control-label">Date Start</label>
        <div class="col-sm-4">
            <date-time-form-control 
                date="tournament.date_start" 
                format="MM/dd/yyyy HH:mm"
                name="date_start"
                field-required="true"
                max-date="tournament.date_end"
                on-change="dateStartChanged()"
                >
            </date-time-form-control>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (detailsForm.date_end.$invalid),
                    'has-warning': (detailsForm.$error.date_end)}">
        <label class="col-sm-3 control-label">Date End</label>
        <div class="col-sm-4">
            <date-time-form-control
                ng-model="tournament.date_end"
                date="tournament.date_end" 
                format="MM/dd/yyyy HH:mm"
                name="date_end"
                field-required="true"
                min-date="tournament.date_start"
                deftime="23:59:59"
                on-change="dateEndChanged()"
                time-validator
            >
            </date-time-form-control>
        </div>
        <div ng-if="validators.date_end" class="col-sm-5 has-warning-div">
            <label ng-bind="validators.date_end"></label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Event Notes</label>
        <div class="col-sm-7">
            <html-editor-panel info="tournament.event_notes" modal-title="Event Notes"></html-editor-panel>
            <p class="help-block">Information will appear when someone clicks into the event name</p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Admin Security PIN for Score Entry</label>
        <div class="col-sm-4">
            <input
                type="text"
                class="form-control"
                placeholder="Security pin ... "
                ng-model="tournament.admin_security_pin"
                name="security_pin"
            >
        </div>
    </div>
    <div class="form-group" ng-if="tournament.allow_teams_registration">
        <label class="col-sm-3 control-label no-pd-top">Allow Officials Score Entry on Mobile</label>
        <div class="col-sm-4">
            <label>
                <input
                    type="checkbox"
                    ng-model="tournament.score_entry_allowed"
                    name="score_entry">
            </label>
        </div>
    </div>
    <div class="form-group" ng-if="tournament.score_entry_allowed">
        <label class="col-sm-3 control-label">Officials Score Entry</label>
        <div class="col-sm-7">
            <div class="checkbox">
                <label>
                    <input
                        type="checkbox"
                        ng-model="tournament.score_entry_live"
                        name="score_entry_live"
                    > Score Entry Available to Live
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input
                        type="checkbox"
                        ng-model="tournament.require_match_end_time"
                        name="require_match_end_time"
                    > Require Match End Time
                </label>
            </div>
        </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Make Event Live to Public</label>
        <div class="col-sm-7">
            <div class="live-to-public-group">
                <div>
                    <input type="checkbox" ng-model="tournament.live_to_public">
                </div>
                <p class="help-block">If enabled, event will appear in the SW list of Current &
                    Upcoming Events and other enabled features will be visible.  If not checked,
                    features such as team and officials entry will be disabled. Disable to take event off
                    line in case of the event being  cancelled
                </p>
            </div>
            <div ng-if="tournament.live_to_public" class="checkbox">
                <label>
                    <input
                        type="checkbox"
                        ng-model="tournament.show_on_home_page"
                        name="show_on_home_page"
                    > Show Event on SW Home page and SW Tickets
                </label>
            </div>

        </div>
      </div>
</form>
