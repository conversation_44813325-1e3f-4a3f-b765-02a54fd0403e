<form class="form-horizontal">
    <div ng-class="{'form-group validation-required': true, 'has-error': $ctrl.hasErrors }">
        <label class="col-sm-3 control-label">Payment Card</label>
        <div class="col-sm-7">
            <spinner active="$ctrl.isLoading"></spinner>
            <select
                name="stripe_account"
                class="form-control"
                ng-model="$ctrl.tournament.payment_method_id"
                ng-options="card.stripe_payment_method_id as $ctrl.cardLabel(card) + (card.offline_is_active ? '' : ' (Inactive)') for card in $ctrl.paymentCards"
                ng-if="!$ctrl.isLoading"
                required
            >
                <option value="" ng-if="!$ctrl.tournament.payment_method_id">Choose Payment Card ...</option>
            </select>
        </div>
    </div>
</form>
