<div class="row assign-template">
    <div class="col-xs-12">
        <div class="panel panel-info" ng-repeat="(group, types) in $ctrl.triggers">
            <div class="panel-heading"><h4 class="panel-title">{{$ctrl.capitalizeFirstLetter(group)}}</h4></div>
            <div class="panel-body">
                <div class="row assign-template-types" ng-repeat="(typeName, type) in types">
                    <div class="col-xs-12">
                        <!-- TODO: create a component for a template's row -->
                        <div class="row">
                            <div class="col-xs-12 col-sm-3">
                                <h5>{{type.title}}</h5>
                            </div>
                            <div class="col-xs-8 col-sm-3">
                                <!-- TODO: create a component for the dropdown -->
                                <div class="btn-group" uib-dropdown>
                                    <button
                                        id="single-button"
                                        type="button"
                                        class="btn btn-default"
                                        uib-dropdown-toggle
                                        ng-disabled="$ctrl.isLoading">
                                        {{ $ctrl.templates[group][typeName][type.email_template_id].title || 'Not Assigned' }} <span class="caret"></span>
                                    </button>
                                    <ul ng-if="$ctrl.templates[group][typeName]" class="dropdown-menu" role="menu">
                                        <li role="menuitem"
                                            ng-click="$ctrl.setTemplate(group, typeName, templateID)"
                                            ng-repeat="(templateID, template) in $ctrl.templates[group][typeName]"
                                        >
                                            <a href="">
                                                {{template.title}}
                                                <span ng-if="$ctrl.templates[group][typeName][templateID].is_default">
                                                    (Default)
                                                </span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <!-- === -->
                            </div>
                            <div class="col-xs-1" align="left" ng-if="type.email_template_id">
                                <h5>
                                    <i class="fa fa-eye pointer"
                                       aria-hidden="true"
                                       ng-click="$ctrl.previewTmpl($ctrl.templates[group][typeName][type.email_template_id])">
                                    </i>
                                </h5>
                            </div>
                        </div>
                        <!-- === -->
                    </div>
                </div>
            </div>
        </div>
        <button class="btn btn-primary pull-right" ng-if="!$ctrl.isLoading && !$ctrl.isTriggersEmpty()" ng-click="$ctrl.save()" ng-disabled="$ctrl.isSaving">
            <i class="fa fa-floppy-o"></i> Save
        </button>
    </div>
</div>
