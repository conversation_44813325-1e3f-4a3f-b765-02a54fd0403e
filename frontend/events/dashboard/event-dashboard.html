<ol class="sw_header_breadcrumbs breadcrumb bottom-pressed">
    <li><a ui-state="states.events">Events</a>
    </li>
    <li class="active text-grey selected-row" ng-class="{'show-row': eventName}">                
        <a ui-state="states.update" ui-state-params="{ event: eventId }" ng-if="editEventLinkVisible()">
            {{eventName}}
            <i class="fa fa-pencil-square-o edit-icon"></i>
        </a>
        <a ng-if="!editEventLinkVisible()">
            {{eventName}}
        </a>
    </li>
</ol>
<div class="row rowm0">
    <div class="col-lg-12">
        <sw-tabmenu-head
            tabs="tabs"
            header-class="eo-menupanel"
        ></sw-tabmenu-head>        
        <div class="tab-content eo-dashboard">
            <div class="tab-pane active">
                <div ui-view autoscroll="true"></div>
            </div>       
        </div>
    </div>
</div>
