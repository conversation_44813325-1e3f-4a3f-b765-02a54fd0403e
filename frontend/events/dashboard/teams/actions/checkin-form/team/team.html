<li class="list-group-item">
    <span ng-class="$ctrl.teamClass()">{{(!$ctrl.team.status_checkin)?'N/A':''}}</span>
    <span ng-bind="$ctrl.team.organization_code"></span>
    <span ng-bind="$ctrl.team.team_name"></span>
    <uib-accordion ng-if="$ctrl.team.validation_errors.roster.length > 0">
        <uib-accordion-group class="payments-list--sm panel-danger">
            <uib-accordion-heading>
                <i class="fa fa-exclamation-triangle"></i> Team Roster Check In Validation Errors
            </uib-accordion-heading>
            <checkin-team-errors errors="$ctrl.team.validation_errors"></checkin-team-errors>
        </uib-accordion-group>
    </uib-accordion>
</li>
