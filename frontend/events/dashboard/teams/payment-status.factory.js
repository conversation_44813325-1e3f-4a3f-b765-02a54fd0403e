angular.module('SportWrench').factory('PaymentStatusFactory', function (TEAM_STATUS) {
    return {
        getItems: function () {
            return [
                {
                    id: TEAM_STATUS.PAYMENT.PAID,
                    name: 'Paid',
                    class: 'glyphicon glyphicon-ok-sign green'
                }, {
                    id: TEAM_STATUS.PAYMENT.PENDING,
                    name: 'Pending',
                    class: 'glyphicon glyphicon-minus-sign blue'
                }, {
                    id: TEAM_STATUS.PAYMENT.NONE,
                    name: 'Not paid',
                    class: 'glyphicon glyphicon-remove-circle red'
                }, {
                    id: TEAM_STATUS.PAYMENT.REFUNDED,
                    name: 'Refunded',
                    class: 'glyphicon glyphicon glyphicon-remove violet'
                }, {
                    id: TEAM_STATUS.PAYMENT.DISPUTED,
                    name: 'Disputed',
                    class: 'glyphicon glyphicon-minus-sign red'
                }
            ]
        }
    }
})
