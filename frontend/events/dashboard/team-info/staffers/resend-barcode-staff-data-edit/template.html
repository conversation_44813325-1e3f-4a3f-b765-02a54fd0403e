<div class="modal-header text-center"><h4 class="modal-title">Send Check In Barcode to:</h4></div>
<div class="modal-body">
    <form class="form-horizontal" name="$ctrl.editStaffDataForm">
        <div ng-class="{ 'form-group': true, 'has-error': $ctrl.fieldHasError('email') }">
            <label class="col-sm-3 control-label">Email:</label>
            <div class="col-sm-7">

                <input type="email" name="email" class="form-control" email-validator ng-model="$ctrl.email">
                <p
                    ng-if="$ctrl.fieldHasError('email')"
                    class="help-block text-error"
                >
                    Invalid Email Address
                </p>
            </div>
        </div>
        <div ng-class="{ 'form-group': true, 'has-error': $ctrl.fieldHasError('phone') }">
            <label class="col-sm-3 control-label">Phone:</label>
            <div class="col-sm-7">
                <input type="tel" name="phone" class="form-control" ng-model="$ctrl.phone" ui-mask="(*************">
                <p
                    ng-if="$ctrl.fieldHasError('phone')"
                    class="help-block text-error"
                >
                    Invalid Phone Number
                </p>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="$ctrl.onClose()">Close</button>
    <button type="submit" class="btn btn-success" ng-click="$ctrl.send()">Send</button>
</div>
