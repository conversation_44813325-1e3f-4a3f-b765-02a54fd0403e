angular.module('SportWrench').component('deactivateStafferBarcode', {
    templateUrl: 'events/dashboard/team-info/staffers/deactivate-barcode/deactivate-barcode.html',
    bindings: {
        isBarcodeDeactivated: '<',
        reason: '<',
        onSave: '&',
        staffer: '<'
    },
    controller: Component,
});

Component.$inject = ['ACTIVATE', 'DEACTIVATE'];

function Component(ACTIVATE, DEACTIVATE) {
    this.showSaveButton = false;
    this.notes = '';
    this.saveInProgress = false;

    this.getAction = () => {
        return this.isBarcodeDeactivated ? ACTIVATE : DEACTIVATE;
    };

    this.getReasonPrefix = () => {
        return this.isBarcodeDeactivated ? 'Deactivate reason:' : 'Activate reason:';
    };

    this.onShowSaveButton = () => {
        this.showSaveButton = true;
    };

    this.save = () => {
        if (!this.saveInProgress) {
            this.saveInProgress = true;

            const data = {
                reason: this.notes
            };

            this.onSave({ data, staffer: this.staffer })
                .then(() => {
                    this.showSaveButton = false;
                    this.notes = '';
                })
                .finally(() => {
                    this.saveInProgress = false;
                })
        }
    }
}
