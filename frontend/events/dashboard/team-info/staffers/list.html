<uib-accordion>
    <uib-accordion-group
        class="row-space payments-list--sm staffers-list"
        ng-repeat="s in staff"
        is-open="utils.isOpened[s.id]">
        <uib-accordion-heading>
            <div ng-click="toggleCollapse(s)" class="pointer">
                <div class="mt1">
                    <span ng-class="{ 'label': true, 'label-info': (s.role_name !== 'N/A'), 'label-danger': (s.role_name === 'N/A') }">{{s.role_name}}</span>
                    <safesport-adult-badge safesport-status="s.safesport_statusid"></safesport-adult-badge>
                    <genders
                        m="s.gender === GENDER_VALUES.MALE"
                        f="s.gender === GENDER_VALUES.FEMALE"
                        nb="s.gender === GENDER_VALUES.NON_BINARY"
                    ></genders>
                    <span ng-if="s.gender">&nbsp;</span>
                    <span ng-class="{ 'font-bold': s.primary }">{{s.name}}</span>&nbsp;
                    <span ng-if="!s.primary && s.primary_team">({{s.primary_team.usav}})&nbsp;</span>
                    <span ng-if="s.checkin_description_link" ng-click="$event.stopPropagation()">
                        <a ng-click="openStaffQRCodePage(s)" href="">QR Code</a>
                    </span>
                    <span ng-click="$event.stopPropagation();$event.preventDefault()">
                        <button class="btn btn-danger btn-xs pull-right" type="button" ng-click="removeMember(s)">
                            Remove
                        </button>
                    </span>
                </div>
                <div class="staffers-phones">
                    <a href="" ng-click="$event.stopPropagation();send({email:s.email})">{{s.email}}</a>
                    <span ng-if="s.phonem"><i class="fa fa-mobile"></i> {{s.phonem | tel}}&nbsp;</span>
                    <span ng-if="s.phoneh"><i class="fa fa-home"></i> {{s.phoneh | tel}}&nbsp; </span>
                    <span ng-if="s.phonew"><i class="fa fa-building-o"></i> {{s.phonew | tel}}&nbsp;</span>
                    <span ng-if="s.phoneo"><i class="fa fa-plus-circle"></i> {{s.phoneo | tel}}&nbsp;</span>
                </div>
            </div>
        </uib-accordion-heading>
        <form class="form-horizontal" name="eoStafferForm" ng-submit="save(s, eoStafferForm)">
            <fieldset ng-disabled="utils.updating">
                <div ng-class="{ 'form-group': true, 'has-error': eoStafferForm.$submitted &&  eoStafferForm.email.$invalid }">
                    <label class="col-sm-3 control-label">Email <span style="color:red">*</span></label>
                    <div class="col-sm-7">

                        <input type="email" name="email" class="form-control" ng-model="utils.editor[s.id].email" email-validator required>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true, 'has-error': eoStafferForm.$submitted &&  eoStafferForm.phonem.$invalid }">
                    <label class="col-sm-3 control-label">Cell Phone </label>
                    <div class="col-sm-7">
                        <input type="tel" name="phonem" class="form-control" ng-model="utils.editor[s.id].phonem" phone-formatter phone-validator>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true, 'has-error': eoStafferForm.$submitted &&  eoStafferForm.phonew.$invalid }">
                    <label class="col-sm-3 control-label">Work Phone </label>
                    <div class="col-sm-7">
                        <input type="tel" name="phonew" class="form-control" ng-model="utils.editor[s.id].phonew" phone-formatter phone-validator>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true, 'has-error': eoStafferForm.$submitted &&  eoStafferForm.phoneh.$invalid }">
                    <label class="col-sm-3 control-label">Home Phone </label>
                    <div class="col-sm-7">
                        <input type="tel" name="phoneh" class="form-control" ng-model="utils.editor[s.id].phoneh" phone-formatter phone-validator>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true, 'has-error': eoStafferForm.$submitted &&  eoStafferForm.phoneo.$invalid }">
                    <label class="col-sm-3 control-label">Other Phone </label>
                    <div class="col-sm-7">
                        <input type="tel" name="phoneo" class="form-control" ng-model="utils.editor[s.id].phoneo" phone-formatter phone-validator>
                    </div>
                </div>
                <div ng-class="{ 'form-group': true, 'has-error': eoStafferForm.$submitted &&  eoStafferForm.role.$invalid }">
                    <label class="col-sm-3 control-label">Role </label>
                    <div class="col-sm-7">
                        <select
                            class="form-control"
                            name="role"
                            ng-model="utils.editor[s.id].role_id"
                            ng-options="r.id as r.name for r in utils.roles"
                            required
                        >
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">Certificate </label>
                    <div ng-class="{'col-sm-5': !utils.editor[s.id].is_impact || utils.editor[s.id].certChanged,
                                    'col-sm-7': utils.editor[s.id].is_impact && !utils.editor[s.id].certChanged}">
                        <input type="text" class="form-control" ng-model="utils.editor[s.id].cert" placeholder="Certificate name..."/>
                    </div>
                    <div lass="col-sm-3" ng-if="!utils.editor[s.id].is_impact && !utils.editor[s.id].certChanged">
                        <label class="control-label">
                            <a href="" class="staff-safesport" ng-click="setCertImpact(s)">Set IMPACT</a>
                        </label>
                    </div>
                    <div class="col-sm-2" ng-if="utils.editor[s.id].certChanged">
                        <label class="control-label">
                            <a href="" class="staff-safesport" ng-click="cancelSetCertImpact(s)">Cancel</a>
                        </label>
                    </div>
                </div>
                <div class="form-group" ng-if="utils.editor[s.id]">
                    <set-safesport-ok
                        adult="utils.editor[s.id]"
                        set-ok="setSafeSportOk(adult)"
                        cancel="cancelSafeSportChange(adult)"
                    ></set-safesport-ok>
                </div>
                <div class="form-group">
                    <label class="checkbox col-sm-offset-3 col-sm-7">
                        <input type="checkbox" ng-model="utils.editor[s.id].primary"> Is Primary
                    </label>
                </div>
                <div ng-if="showDeactivatedButton(s)">
                    <deactivate-staffer-barcode 
                        is-barcode-deactivated="s.is_deactivated" 
                        reason="s.deactivated_reason" 
                        on-save="deactivateBarcode(data, staffer)"
                        staffer="s"
                    >
                    </deactivate-staffer-barcode>
                </div>
                <input type="submit" class="btn btn-primary col-sm-offset-3" value="Save">
                <button type="button" class="btn btn-primary pull-right"
                       ng-click="openEditStaffDataModal(s)"
                       ng-if="utils.editor[s.id].primary_staff_barcodes_checkin"
                       ng-disabled="utils.disableSendCheckinBarcodeBtn[s.id]"
                >Send Check In Barcode</button>
                <div class="mt-5">
                    <staff_online_checkin_history online-checkin-history="s.staff_online_checkin_history"></staff_online_checkin_history>
                </div>
            </fieldset>
        </form>
    </uib-accordion-group>
</uib-accordion>
