 angular.module('SportWrench')

.controller('Events.TeamInfo.TabEventController',
function (
    $scope, toastr, rosterTeamService, RosterTeamMembersService, masterClubService, userService, $http,
    $stateParams, eswEventService, SANCTIONING_BODY, ENV, masterTeamService, PrevQualificationService,
    BID_AGREEMENT_SENDER, TEAM_AGREEMENT_VALUE, ConfirmationService, $q, TEAM_STATUS, ESW_URL
) {
    $scope.teamId = $scope.$parent.modalParams.team_id;
    $scope.notes = {};
    $scope.isLoggedInUnderCD = false;
    $scope.selectedDivisionIsFull = false;

    $scope.hasManualClubNames = $scope.$parent.modalParams.event.has_manual_club_names;

    $scope.isEventSancByUSAV =  $scope.$parent.modalParams.event &&
        $scope.$parent.modalParams.event.sport_sanctioning_id === SANCTIONING_BODY.USAV;

    $scope.showTeamQualificationSuccessMessage = false;
    $scope.showTeamInfoSuccessMessage = false;

    $scope.ages = masterTeamService.getAges();

    let needUpdateTeamInfo = true;
    let needUpdateTeamQualification = true;

    let teamInfoUpdateInProccess = false;
    let teamQualificationUpdateInProccess = false;

    const PREV_FIELDS = ['earned_at', 'prev_qual_age', 'prev_qual_division'];

    function calculateDiffs () {
        var mt = $scope.masterTeam || {};
        var rt = $scope.teamInfo || {};
        var d = $scope.diffs = {};

        if (mt.team_name !== rt.team_name)
            $scope.diffs.team_name = mt.team_name;
        if (+mt.age !== +rt.age)
            $scope.diffs.age = mt.age;      
        if (mt.organization_code !== rt.organization_code)
            $scope.diffs.organization_code = mt.organization_code;
        if(mt.gender !== rt.gender)
            $scope.diffs.gender = rt.gender;

        if(!isNaN(mt.rank) && !isNaN(rt.rank)) {
            if(parseInt(mt.rank, 10) !== parseInt(rt.rank, 10)) {
                $scope.diffs.rank = mt.rank;
            }
        } else if (mt.rank !== rt.rank) {
            $scope.diffs.rank = mt.rank;
        }
    }

    const checkFormChange = (originalFormData, currentFormData) => {
        return !angular.equals(
            originalFormData,
            currentFormData
        );
    };

    const toggleTeamInfoSuccessMessage = (flag) => {
        $scope.showTeamInfoSuccessMessage = flag;
    };

    const toggleTeamQualificationSuccessMessage = (flag) => {
        $scope.showTeamQualificationSuccessMessage = flag;
    };

    const updateTeamInfo = () => {
        needUpdateTeamInfo = true;
        needUpdateTeamQualification = false;

        toggleTeamInfoSuccessMessage(true);
    };

    const updateTeamQualificationInfo = () => {
        needUpdateTeamInfo = false;
        needUpdateTeamQualification = true;

        toggleTeamQualificationSuccessMessage(true);
    };


    $scope.notEmpty = function (obj) {
        return !_.isEmpty(obj)
    };

    $scope.validateTeamProblems = function (entryStatus) {
        $scope.validationErrors = [];

        if (parseInt(entryStatus) === 12 && $scope.originalEntryStatus !== 12) {
            if (__validationTeamProblems()) {
                $scope.validationErrors.push('Roster validation errors.');
            }
        }
    };

    function __validationTeamProblems() {
        return $scope.notEmpty($scope.rosterValidation);
    }

    $scope.validateDivision = function (d) {

        if(d.is_full && $scope.originalEntryStatus === 12 && $scope.originalTeamInfo.division_id !== d.division_id) {
            $scope.selectedDivisionIsFull = true;
        } else {
            $scope.selectedDivisionIsFull = false;
        }

        $scope.selectedDivisionName = d.name;
        $scope.fullDivisionMaxTeamsNumber = d.max_teams;
        $scope.fullDivisionAcceptedTeamsNumber = d.accepted;
    };

    $scope.rankList = masterTeamService.getRanks();

    // Get values from parrent controller's scope when they'll have loaded
    $scope.$parent.$watch('tabs.team', function(v) {
        if (!v) return;
        $scope.loading = {
            errors_loaded: false
        }

        $scope.eventIsEnded = v.event_is_ended;
        $scope.isQualifyingDivision = v.is_qualifying_division;

        if (needUpdateTeamInfo) {
            $scope.teamInfo = {
                team_name: v.team_name,
                division_id: v.division_id,
                organization_code: v.organization_code,
                age: v.age,
                rank: v.rank,
                gender: v.gender,
                distance: v.distance,
                is_local: v.local,
                profile_completed: v.profile_completed,
                manual_club_name: v.manual_club_name,
                division_price_change_agreed: false,
                seasonality: v.seasonality,
            };
        }

        if (needUpdateTeamQualification) {
            $scope.teamQualificationInfo = {
                show_accepted_bid: Boolean(v.show_accepted_bid),
                prev_qual: Boolean(v.prev_qual),
                prev_qual_age: v.prev_qual_age,
                prev_qual_division:  v.prev_qual_division,
                earned_at: v.earned_at,
            };
        }

        $scope.bidAgreementAccepted = v.bid_agreement_accepted;

        $scope.originalEntryStatus = v.status_entry;
        $scope.originalPaidStatus = v.status_paid;

        RosterTeamMembersService.validateRoster(
            $scope.$parent.modalParams.event_id, v.roster_team_id
        ).then(function (resp) {
            $scope.rosterValidation = resp.data && resp.data.rosterValidation;
            $scope.loading.errors_loaded = true;
        });

        $scope.masterTeam = v.master_team;

        $scope.team = {
            status_entry: v.status_entry,
            entry_status: v.entry_status
        };
        $scope.status_entry = {};

        if (needUpdateTeamInfo) {
            $scope.originalTeamInfo = angular.copy($scope.teamInfo);
        }

        if (needUpdateTeamQualification) {
            $scope.originalTeamQualificationInfo = angular.copy($scope.teamQualificationInfo);
        }

        $scope.clubInfo = {
            club_name           : v.club_name,
            address             : v.address,
            city                : v.city,
            state               : v.state,
            zip                 : v.zip,
            country             : v.country,
            country_name        : v.country_name,
            code                : v.code,
            region              : v.region,
            region_name         : v.region_name, 
            director_first      : v.director_first,
            director_last       : v.director_last,
            director_email      : v.director_email,
            director_phone      : v.director_phone,
            director_user_email : v.director_user_email,
            director_token      : v.directors_token,
            eo_email            : v.eo_email,
            eo_restore_hash     : v.eo_restore_hash,
            administrative_email: v.administrative_email,
        };

        calculateDiffs();
    });
    $scope.$parent.$watch('divisionsDropDown', function(v) {
        if (!v) return;
        $scope.divisions = v;
    });

    $scope.updateBidAgreement = async function () {
        $scope.bidAgreementUpdateIsInProgress = true;

        let newValue = !$scope.bidAgreementAccepted;

        let agreementLabel = !!newValue
            ? TEAM_AGREEMENT_VALUE.ACCEPTED
            : TEAM_AGREEMENT_VALUE.DECLINED;

        let params = { [agreementLabel]: [$scope.teamId] };
        let sender = BID_AGREEMENT_SENDER.EVENT;

        try {
            await PrevQualificationService.updateBidAgreementForTeams($stateParams.event, params, sender);

            toastr.success('Updated');
        } catch (err) {
            toastr.warning('Error. Please, try again later.');
        } finally {
            $scope.bidAgreementUpdateIsInProgress = false;
        }
    };

    $scope.$watchCollection('teamQualificationInfo', function(v) {
        if (!v) return;
        if (!$scope.masterTeam) return;

        let origin = angular.copy($scope.originalTeamQualificationInfo);
        let current = angular.copy($scope.teamQualificationInfo);

        if (!$scope.originalTeamQualificationInfo.prev_qual) {
            origin = _.omit($scope.originalTeamQualificationInfo, PREV_FIELDS);
            current = _.omit($scope.teamQualificationInfo, PREV_FIELDS);
        }

        $scope.isQualificationFormChanged = checkFormChange(
            origin,
            current
        );

        if ($scope.isQualificationFormChanged) {
            toggleTeamQualificationSuccessMessage(false);
        }
    });


    $scope.$watchCollection('teamInfo', function(v) {
        if (!v) return;
        if (!$scope.masterTeam) return;

        $scope.isFormChanged = checkFormChange($scope.originalTeamInfo, $scope.teamInfo);

        if ($scope.isFormChanged) {
            toggleTeamInfoSuccessMessage(false);
            angular.element('form[name="teamInfoForm"] .server-error').remove();
        }

        calculateDiffs();
    });

    $scope.$watchCollection('[teamInfo.age, teamInfo.rank, teamInfo.gender]', function (v, o) {
        if (!$scope.isEventSancByUSAV || !v[0] || !v[1] || !o[0] || !o[1] || !v[2] || !o[2]) return;

        $scope.teamInfo.organization_code = masterClubService.getTeamcode(
            {
                code: $scope.clubInfo.code,
                region: $scope.clubInfo.region
            },
            v[2], v[0], v[1]);
    });

    $scope.discard = function() {
        angular.element('form[name="teamInfoForm"] .server-error').remove();

        $scope.teamInfo = angular.copy($scope.originalTeamInfo);

        $scope.notes.teamChange         = null;
        $scope.selectedDivisionIsFull   = false;
    };

    $scope.discardTeamQualification = function() {
        $scope.teamQualificationInfo = angular.copy($scope.originalTeamQualificationInfo);
    };

    $scope.saveQualification = function() {
        if (teamQualificationUpdateInProccess) {
            return;
        }

        if(!__validateQualifiedDivision()) {
            return;
        }

        teamQualificationUpdateInProccess = true;

        const teamQualificationInfo = !$scope.teamQualificationInfo.prev_qual
            ? _.omit($scope.teamQualificationInfo, PREV_FIELDS)
            : $scope.teamQualificationInfo;

        PrevQualificationService.updateQualification($stateParams.event, $scope.teamId, teamQualificationInfo)
            .then(() => {
                $scope.isQualificationFormChanged = false;
                $scope.$parent.teamInfoChanged = true;
                $scope.originalTeamQualificationInfo = angular.copy($scope.teamQualificationInfo);

                updateTeamQualificationInfo();
                $scope.$emit('team.info.data-changed');
            })
            .finally(() => {
                teamQualificationUpdateInProccess = false;
            })
    };

    $scope.newDivisionRequiresAdditionalPayment = function () {
        let teamIsPaid = $scope.originalPaidStatus === TEAM_STATUS.PAYMENT.PAID;

        if(!teamIsPaid) {
            return false;
        }

        let initialFee = this.event.reg_fee;
        let newFee = this.event.reg_fee;

        $scope.divisions.forEach(division => {
            if(Number(division.division_id) === Number($scope.originalTeamInfo.division_id)) {
                initialFee = !_.isNull(division.reg_fee) && division.reg_fee > 0 ? division.reg_fee : initialFee;
            }

            if(Number(division.division_id) === Number($scope.teamInfo.division_id)) {
                newFee = !_.isNull(division.reg_fee) && division.reg_fee > 0 ? division.reg_fee : newFee;
            }
        });

        return initialFee < newFee;
    }

    $scope.save = function() {
        if (teamInfoUpdateInProccess) {
            return;
        }

        teamInfoUpdateInProccess = true;

        if ($scope.eventIsEnded) {
            toastr.warning('Event is Closed');
            return;
        }

        angular.element('form[name="teamInfoForm"] .server-error').remove();

        var saved_division_name;

        $scope.divisions.forEach(function(div) {
            if (div.division_id == $scope.teamInfo.division_id) {
                saved_division_name = div.name;
            }
        });

        var teamInfo = angular.copy($scope.teamInfo);

        teamInfo.age                = teamInfo.age;
        teamInfo.division_name      = saved_division_name;
        teamInfo.notes              = $scope.notes.teamChange;
        teamInfo.seasonality        = undefined;

        let newDivisionRequiresAdditionalPayment;

        if($scope.originalTeamInfo.division_id !== $scope.teamInfo.division_id) {
            newDivisionRequiresAdditionalPayment = $scope.newDivisionRequiresAdditionalPayment();
        }

        return (newDivisionRequiresAdditionalPayment
                ? ConfirmationService.ask(`
                    The registration fee for the initial division 
                    is lower than that of the ${saved_division_name} division. 
                    Do You want to reset team's payment and entry statuses 
                    to allow Club Director to make an additional payment?`,
                    {
                        title 			: 'Confirm team change:',
                        disableNoBtn 	: true
                    }).then(answer => answer === ConfirmationService.YES_RESP)
                : Promise.resolve(true)
        ).then(agreed => {
            if(!agreed) {
                return;
            } else if(newDivisionRequiresAdditionalPayment) {
                teamInfo.division_price_change_agreed = true;
            }

            return rosterTeamService.update(teamInfo, $scope.teamId, $stateParams.event).then(() => {
                $scope.notes.teamChange = null;
                $scope.isFormChanged = false;
                $scope.$parent.teamInfoChanged = true;
                $scope.originalTeamInfo = angular.copy(teamInfo);
                $scope.selectedDivisionIsFull = false;

                $scope.originalTeamInfo.team_id = Number($scope.teamId);

                updateTeamInfo();
                $scope.$emit('team.info.data-changed');
            })
        }).finally(() => {
            teamInfoUpdateInProccess = false;
        })
    };
    
    function __validateQualifiedDivision() {
        let valid = true;

        if($scope.teamQualificationInfo.prev_qual) {
            if(!$scope.teamQualificationInfo.prev_qual_age) {
                valid = false;
                toastr.warning('Prev qualified age required!');
            } else if(!$scope.teamQualificationInfo.prev_qual_division) {
                toastr.warning('Prev qualified division required');
                valid = false;
            } else if(!$scope.teamQualificationInfo.earned_at) {
                toastr.warning('Earned at required');
                valid = false;
            }
        }

        return valid;
    }

    $scope.showLoginBlock = function () {
        return userService.isAllowedToLoginAsCD() || (userService.hasGodRole() && (this.event
            && this.event.registration_method !== 'doubles' && this.event.has_clubs));
    }

    $scope.showFixBlock = function () {
        return $scope.isLoggedInUnderCD && (this.event && this.event.registration_method !== 'doubles')
    }

    $scope.saveStatusEntry = function () {
        $http.post('/api/v2/event/' + $stateParams.event + '/teams/entry/change', {
            teams: [$scope.teamId],
            status: $scope.team.status_entry,
            notes: $scope.status_entry.notes,
        }).success(function () {
            $scope.status_entry.changed = false;
            $scope.status_entry.notes = '';
            $scope.$emit('team.info.data-changed');
        })
    };

    $scope.loginAsClubDirector = function() {
        userService.loginAs($scope.clubInfo.director_user_email, $scope.clubInfo.director_token, function (res) {
            $scope.isLoggedInUnderCD = true;
            userService.logIn(res.data.user);

            const link = createLinkElement('#/club/info');

            link[0].click()
            
            link.remove();
        });
    };

    $scope.fixSession = function() {
        userService.loginAs($scope.clubInfo.eo_email, $scope.clubInfo.eo_restore_hash, function(res) {
            $scope.isLoggedInUnderCD = false;
            userService.logIn(res.data.user);
        });
    };

    $scope.getStandingPageLink = function () {
        let eventID     = this.event.esw_id;
        let divisionID  = this.teamInfo.division_id;

        return `${ESW_URL}/#/events/${eventID}/divisions/${divisionID}/standings?withCacheCleaning`;
    }

    function createLinkElement(href, target = '_blank') {
        const link = angular.element(`<a></a>`);

        link[0].href = href;
        link[0].target = target;

        angular.element(document.body).append(link);

        return link;
    }
});
