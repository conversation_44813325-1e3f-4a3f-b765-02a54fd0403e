angular.module('SportWrench').service('RosterTeamMembersService', RosterTeamMembersService);

function RosterTeamMembersService ($http, $window, $uibModal, ClubCheckinService) {
	this.$http 		= $http;
	this.$window 	= $window;
    this.urlPrefix 	= '/api/event/';
    this.modal = $uibModal;
    this.ClubCheckinService = ClubCheckinService;
}

RosterTeamMembersService.prototype.printRoster = function (eventId, teamId) {
	var url = this.urlPrefix + eventId + '/roster/checkin?team[]=' + teamId;        

    var w = this.$window.open(url, '_blank');

    if(!w) {
    	window.location = url;
    }

}
RosterTeamMembersService.prototype.removeMember = function (eventId, teamId, memberId, type) {
	return this.$http.delete(
            this.urlPrefix + eventId + '/team/' + teamId + '/member/' + memberId + '/' + type
    );
}

RosterTeamMembersService.prototype.getMembers = function (eventId, teamId) {
	return this.$http.get(this.urlPrefix + eventId + '/team/' + teamId+ '/members');
}

RosterTeamMembersService.prototype.updateStaffer = function (eventId, teamId, stafferId, data) {
	return this.$http.put(this.urlPrefix + eventId + '/team/' + teamId + '/staffer/' + stafferId + '/update', {
		staffer: data
	})
}

RosterTeamMembersService.prototype.validateRoster = function (eventId, teamId) {
	return this.$http.get(this.urlPrefix + eventId + '/team/' + teamId + '/validate-roster')
}

RosterTeamMembersService.prototype.unlockTeamRoster = function (eventId, teamId) {
    return this.$http.get(this.urlPrefix + eventId + '/team/' + teamId + '/unlock')
}

RosterTeamMembersService.prototype.lockTeamRoster = function (eventId, teamId) {
    return this.$http.get(this.urlPrefix + eventId + '/team/' + teamId + '/lock')
}

RosterTeamMembersService.prototype.markRosterValid = function (eventId, teamId) {
    return this.$http.get(this.urlPrefix + eventId + '/team/' + teamId + '/set-valid')
}

RosterTeamMembersService.prototype.removeRosterValidMark = function (eventId, teamId) {
    return this.$http.get(this.urlPrefix + eventId + '/team/' + teamId + '/remove-validation')
}

RosterTeamMembersService.prototype.updateAthlete = function (eventId, teamId, data) {
    return this.$http.put(this.urlPrefix + eventId + '/team/' + teamId + '/athlete/' + data.id + '/update', {
        athlete: data
    })
}

RosterTeamMembersService.prototype.openStaffersListModal = function(eventID, teamID) {
    return this.modal.open({
        size: 'lg',
        template: '<checkin-staffers-list event-id="eventID" team-id="teamID" on-close="$close()"></checkin-staffers-list>',
        controller: ['$scope', function($scope) {
            $scope.eventID = eventID;
            $scope.teamID = teamID;
        }]
    }).result
}

RosterTeamMembersService.prototype.checkInTeam = function(eventID, teamID, staffers) {
    return this.$http.post(`${this.urlPrefix}${eventID}/team/${teamID}/online-checkin`, { staffers });
}

RosterTeamMembersService.prototype.getStaffers = function(eventID, teamID) {
    return this.$http.get(`${this.urlPrefix}${eventID}/team/${teamID}/online-checkin/staffers`);
}
