angular.module('SportWrench').factory('PaymentsFiltersFactory', function () {
    return {
        getAvailability: function () {
            return [
                {
                    id: 'null',
                    name: 'Pending',
                    class: 'glyphicon glyphicon-minus-sign blue'
                }, {
                    id: 'not_null',
                    name: 'Available',
                    class: 'glyphicon glyphicon-ok-sign green'
                }
            ];
        },
        getTypes: function () {
            return [
                {
                    id: 'card',
                    name: 'Card',
                    class: 'fa fa-credit-card'
                }, {
                    id: 'check',
                    name: 'Check',
                    class: 'fa fa-pencil-square-o'
                }, {
                    id: 'ach',
                    name: 'ACH',
                    class: 'fa fa-university'
                }
            ]
        },
        getStatuses: function () {
            return [
                {
                    id: 'paid',
                    name: 'Paid',
                    class: 'glyphicon glyphicon-ok-sign green'
                }, {
                    id: 'pending',
                    name: 'Pending',
                    class: 'glyphicon glyphicon-minus-sign blue'
                }, {
                    id: 'canceled',
                    name: 'Canceled',
                    class: 'glyphicon glyphicon-remove-circle red'
                }, {
                    id: 'disputed',
                    name: 'Disputed',
                    class: 'glyphicon glyphicon-minus-sign red'
                }
            ]
        }
    }
});
