<div class="row rowm0 row-space--small" ng-if="titles.length" ng-repeat="t in titles">
    <div class="col-xs-4">
        <div class="checkbox no-p">
            <div class="input-prepend">
                <span class="add-on">
                    <input type="checkbox" ng-model="selection[t.id]" ng-change="dataChanged();">
                </span>
                <input 
                    type="text" 
                    class="form-control input-sm" 
                    ng-model="t.title" 
                    placeholder="Custom"
                    ng-disabled="!selection[t.id]"> 
            </div>
        </div>
    </div>    
    <div class="col-xs-2">
        <input type="text" 
            class="form-control input-sm" 
            ng-model="t.abbrev" 
            ng-disabled="!selection[t.id]"
            maxlength="10">
    </div>
    <div class="col-xs-3">
        <input type="text" class="form-control input-sm" ng-model="t.max_teams" ng-disabled="!selection[t.id]">
    </div>
    <div class="col-xs-3">
        <input type="number" class="form-control input-sm" ng-model="t.level_sort_order" ng-disabled="!selection[t.id]">
    </div>
</div>
<i class="fa fa-spinner fa-pulse" ng-if="!titles.length"></i>
