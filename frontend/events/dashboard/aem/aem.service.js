angular.module('SportWrench').service('AEMService', ['$http', '$uibModal', AEMService]);

function AEMService ($http, $uibModal) {
	this._$http  			= $http;
	this._$uibModal 		= $uibModal;
	this._generalEndpoint 	= '/api/aem/';
}

Object.defineProperty(AEMService.prototype, 'BASIC_LAYOUT_TYPE', {
    value           : 'content',
    writable        : false,
    configurable    : false
});

Object.defineProperty(AEMService.prototype, 'ADMIN_USER_ROLE', {
    value           : 'admin',
    writable        : false,
    configurable    : false
});

/* 
	Note: this service will contain actions for 3 parts of the AEM module: 
	1. General actions (like editor's keys getting)
	2. Admin Actions
	3. Event Actions
*/

/* === */

AEMService.prototype.getEditorToken = function () {
	return this._$http.get(this._generalEndpoint + 'editor-token');
};

AEMService.prototype.getBasicLayouts = function () {
	return this._$http.get(this._generalEndpoint + 'basic-layouts');
}


/* TEMPLATE PREVIEW */
AEMService.prototype.openPreviewModal = function (
    templateID, link, tmplTitle, emailPreviewDataSource, group, modalTitle
) {
    let title;

    if(modalTitle) {
        title = modalTitle;
    } else if(templateID) {
        title = 'Preview: #' + templateID + ' ' + tmplTitle;
    } else {
        title = 'Email Preview';
    }

    return this._$uibModal.open({
        	template 	: [
        		'<modal-wrapper>',
                    `<preview-modal-wrapper 
                        link="previewSrc"
                        data="previewData"
                        template-id="templateId"
                        group="group"
                    >
                    </preview-modal-wrapper>`,
        		'</modal-wrapper>'
        	].join(''),
        size 		: 'lg',
        controller 	: ['$scope', function ($scope) {
            $scope.modalTitle  		= '<h4>' + title + '</h4>';
            $scope.modalSkipFooter 	= true;
            $scope.modalShowClose 	= true;

            $scope.previewSrc = link;
            $scope.previewData = emailPreviewDataSource;
            $scope.templateId = templateID;
            $scope.group = group;
        }.bind(this)]
    }).result;
};


/* TEMPLATE TYPES */
AEMService.prototype.getTemplateGroupVariables = function (group) {
	return this._$http.get(this._generalEndpoint + 'group/' + group + '/variables')
	.then(function (resp) {
		return resp.data && resp.data.variables || [];
	})
}

AEMService.prototype.getEvents = function(eventID, query) {
    let URL = `/api/event/${eventID}/aem/mass-send`;

    if (query) {
        URL += `?${query}`;
    }

    return this._$http.get(URL)
        .then(({ data, status }) => ({ data, status }))
        .catch(({ data, status }) => ({ data, status }));
};

AEMService.prototype.massSend = function(data) {
    return this._$http.post(`${this._generalEndpoint}mass-sending`, data)
        .then(response => response)
        .catch(error => error);
};

AEMService.prototype.retrieveJobs = function(eventId, templateId) {
    return this._$http.get(`/api/event/${eventId}/aem/retrieve-jobs/${templateId}`)
        .then(({ data, status }) => ({ data, status }))
        .catch(({ data, status }) => ({ data, status }));
};

AEMService.prototype.filterSubjectVariables = function(variables) {
    return variables.filter(variable => __isVariableAvailableForSubject(variable));
}

AEMService.prototype.validateTemplateVariables = function(subject, variables) {
    if(!Array.isArray(variables)) {
        throw new Error('Variables should be array');
    }

    if(!_.isString(subject)) {
        throw new Error('Subject should be string');
    }

    for(let variable of variables) {
        if(__variableIsInSubject(subject, variable)) {
            if(!__isVariableAvailableForSubject(variable)) {
                return `Variable ${variable.title} is not allowed to be in subject!`;
            }

            if(__subjectHasVariablesDuplicates(subject, variable)) {
                return `Variable ${variable.title} has duplicates in subject!`;
            }
        }
    }
}

function __isVariableAvailableForSubject (variable) {
    return variable.is_available_for_subject;
}

function __subjectHasVariablesDuplicates (subject, variable) {
    let regex = new RegExp(`(${variable.pattern}).*\\1`,"g");

    return regex.test(subject);
}

function __variableIsInSubject (subject, variable) {
    return subject.includes(variable.pattern);
}
