angular.module('SportWrench').component('contactLists', {
    templateUrl: 'events/dashboard/aem/contact-lists/contact-lists.html',
    controller : ContactListsController
});

ContactListsController.$inject = ['ContactListsService', '$stateParams', 'APP_ROUTES', '$state', '$rootScope'];

function ContactListsController (ContactListsService, $stateParams, APP_ROUTES, $state, $rootScope) {
    let self = this;

    this.openCreateModal = function () {
        ContactListsService.openListModal($stateParams.event, ContactListsService.CREATE_MODE)
            .then(createdList => {
                if (createdList) {
                    this.lists.push(createdList);
                }
            });
    };

    this.$onInit = function () {
        this.lists = [];

        getLists();
    };

    this.getListScope = function (list) {
        return list.is_for_all_events ? '[ALL MY EVENTS]' : '[CURRENT EVENT]';
    };

    this.openList = function (list) {
        $state.go(APP_ROUTES.EO.EMAIL_CONTACT_LIST, { event: $stateParams.event, list: list.list_id });
    };

    function getLists () {
        ContactListsService.getLists($stateParams.event)
            .then(({lists}) => {
                if(lists && lists.length) {
                    self.lists = lists;
                }
            })
            .finally(() => self.dataLoaded = true);
    }
}
