<form name="$ctrl.form" class="aem-tmpl-updating-form form-horizontal" novalidate>
    <fieldset ng-disabled="$ctrl.disabled">
        <div ng-class="$ctrl.ctrlClass('title')">
            <label 
                class="col-xs-4 type-transclusion control-label" 
                ng-transclude="type">
            </label>
            <div class="col-xs-8">
                <div class="input-group" ng-if="$ctrl.showTitleInput">
                    <input 
                    	type="text" 
                        name="title"
                    	class="form-control" 
                    	ng-model="$ctrl.tmplCreds.title" 
                    	ng-change="$ctrl.onDataChanged()" 
                    	ng-model-options="{ debounce: 300 }"
                    	required>
                    <div class="input-group-addon pointer" ng-click="$ctrl.toggleTitleInput()">
                        <i class="fa fa-times" aria-hidden="true"></i>
                    </div>
                </div>  

                <p 
                    class="lead pointer text-center tmpl-title" 
                    ng-click="$ctrl.toggleTitleInput()" 
                    ng-if="!$ctrl.showTitleInput"
                    >
                    <span ng-bind="$ctrl.tmplCreds.title"></span>
                    <i class="fa fa-pencil-square-o text-grey"></i>
                </p>
            </div>
        </div>
        <div ng-class="$ctrl.ctrlClass('subject', 'email-subject')" ng-if="$ctrl.showSubjectControl()">
            <label class="col-xs-4 control-label">Email Subject</label>
            <div class="col-xs-8 control-wrapper">
                <div class="row subject-control">
                    <div class="col-xs-8">
                        <input 
                            type="text" 
                            ng-model="$ctrl.tmplCreds.email_subject" 
                            class="form-control"
                            name="subject"
                            ng-change="$ctrl.onDataChanged()" 
                            ng-model-options="{ debounce: 300 }"
                            ng-maxlength="$ctrl.MAX_EMAIL_SUBJECT_LENGTH"
                            required>
                    </div>
                    <div class="col-xs-4">
                        <div class="pull-right">
                            <aem-variable-picker
                                tmpl-group="$ctrl.tmplGroup" 
                                dropdown-width="$ctrl.subjectWidth"
                                on-var-picked="$ctrl.addVariable(pattern)"
                                variables="$ctrl.variables">
                            </aem-variable-picker>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </fieldset>
</form>

