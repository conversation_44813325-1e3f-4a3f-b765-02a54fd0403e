angular.module('SportWrench').component('eventTransfers', {
	templateUrl 	: 'events/dashboard/transfers/transfers.html',
	controller 		: [
		'EventTransfersService',
        'eventDashboardService',
        '$filter',
        'UtilsService',
        '$q',
        '$timeout',
        '$rootScope',
        'PAYMENT_PROVIDER',
		EventTransfersController
    ]
})

function EventTransfersController (
    EventTransfersService, eventDashboardService, $filter, UtilsService, $q, $timeout, $rootScope, PAYMENT_PROVIDER
) {
	var _self 	= this;
	var _event = eventDashboardService.getEvent();

	var eventID = _event.event_id;

	var curFilter = $filter('currency');

	this.stats 					= {};
	this.usdAvailable 			= 0;
	this.enableTickets 			= _event.allow_ticket_sales;
	this.ticketsCardsAllowed 	= _event.tickets_cards_allowed;
	this.teamsCardsAllowed 		= _event.teams_cards_allowed;
	this.enabledAssignedPayments = _event.event_has_assigned_payment_method;
    this.isTicketsTilledPaymentProvider = _event.tickets_payment_provider === PAYMENT_PROVIDER.TILLED;

	this.initLoad 				= true;
	this.applyAnimatedShowing 	= false;

	loadData();

	function loadData () {
		return $q.all([
			EventTransfersService.loadTransfersData(eventID, 'teams'),
            EventTransfersService.loadTransfersData(eventID, 'tickets'),
		]).then(function (result) {

            this.teams = result[0];
            this.tickets = result[1];

			this.initLoad = false;

			$timeout(function () {
				this.applyAnimatedShowing = true
			}.bind(this))
		}.bind(_self));
	}

	this.getAmount = function (currency, val) {
		return currency + ' ' + curFilter(val, '');
	}

	this.showAutoTransfers = function (account) {
		return (+account.last_transfer_days_ago >= 10);
	}

    // show common account if event is using the same account for teams and tickets
    this.showCommonAccount = function () {
        return this.teams.account !== null
            && this.tickets.account !== null
            && this.teams.account.id === this.tickets.account.id;
    }

    this.showTeamsAccount = function () {
        return this.teams.account !== null;
    }

    this.showTicketsAccount = function () {
        return this.tickets.account !== null;
    }

	this.panelBodyCls = function () {
		return {
			'panel-body panel-smooth': true,
 			'showed': this.applyAnimatedShowing
		}
	}

    $rootScope.$on('reloadMerchantStatisticsData', function () {
        loadData();
    })
}
