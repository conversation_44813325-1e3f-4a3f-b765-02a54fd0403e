angular.module('SportWrench').component('eventStaff', {
    templateUrl: 'events/dashboard/event-staff/event-staff.html',
    controller : EventStaffController
});

EventStaffController.$inject = [
    'EventStaffService', '$stateParams', 'PAYMENT_OPTION_LABEL', '$filter', 'moment',
    'STAFF_MEMBER_TYPE', '$state', 'APP_ROUTES', '$rootScope', 'SAFESPORT_FIELD', 'MBR_FIELD', 'BG_FIELD',
    'AEMSendDialogService', 'eventDashboardService', 'SANCTIONING_BODY', 'AAU_FIELD', 'AAU_BG_FIELD', 'AAU_SAFESPORT_FIELD'
];

function EventStaffController(
    EventStaffService, $stateParams, PAYMENT_OPTION_LABEL, $filter, moment,
    STAFF_MEMBER_TYPE, $state, APP_ROUTES, $rootScope, SAFESPORT_FIELD, MBR_FIELD, BG_FIELD,
    AEMSendDialogService, eventDashboardService, SANCTIONING_BODY, AAU_FIELD, AAU_BG_FIELD, AAU_SAFESPORT_FIELD
) {
    let self = this;

    this.filteredStaffers   = [];
    this.filters            = $stateParams.filters || {};

    const WITHDRAWN_STATUS = 'Withdrawn';

    this.STAFF_MEMBER_TYPE      = STAFF_MEMBER_TYPE;
    this.SAFESPORT_FIELD        = SAFESPORT_FIELD;
    this.MBR_FIELD              = MBR_FIELD;
    this.BG_FIELD               = BG_FIELD;
    this.SANCTIONING_BODY       = SANCTIONING_BODY;
    this.AAU_FIELD              = AAU_FIELD;
    this.AAU_BG_FIELD           = AAU_BG_FIELD;
    this.AAU_SAFESPORT_FIELD    = AAU_SAFESPORT_FIELD;

    this.order = {
        column  : '',
        reverse : false
    };

    this.$onInit = function () {
        loadData();
    };

    this.event = eventDashboardService.getEvent();
    this.eventHasUSAVSanctioning = function () {
        return this.eventSportSanctioningId === SANCTIONING_BODY.USAV;
    };

    this.eventHasAAUSanctioning = function () {
        return this.eventSportSanctioningId === SANCTIONING_BODY.AAU;
    };

    this.showWithdrawn = false;

    this.selection = {
        total_checked: 0,
        all_selected: false
    };

    const showAdditionalModalsButtons = () => {
        return this.filteredStaffers.length && this.filters.work_status !== 'schedule_name_duplicates';
    };

    const openSizing = () => {
        if(this.filteredStaffers.length) {
            EventStaffService.openSizingModal($stateParams.event, STAFF_MEMBER_TYPE, this.filters);
        }
    };

    const openTravelInfo = () => {
        if(this.filteredStaffers.length) {
            EventStaffService.openTravelInfoModal($stateParams.event, STAFF_MEMBER_TYPE, this.filters);
        }
    };

    this.actions = [
        {
            label: 'Clothing Sizes',
            icon: 'fa fa-user-o',
            handler: openSizing,
            isShow: showAdditionalModalsButtons,
        },
        {
            label: 'Travel Info',
            icon: 'fa fa-plane',
            handler: openTravelInfo,
            isShow: showAdditionalModalsButtons,
        },
        {
            label: 'Staff Payouts',
            icon: 'fa fa-money',
            handler: openStaffPayouts,
            isShow: showAdditionalModalsButtons,
        },
    ];
    
    this.isWithdrawnMember = function (of) {
        return of.staff_deleted;
    };

    this.showReverse = function (col) {
        return this.order.column === col;
    };

    this.toggleStaffSelection = function (staff) {
        if(staff.checked) {
            this.selection.total_checked++;
        } else if(this.selection.total_checked > 0) {
            this.selection.total_checked--;
        }

        this.selection.all_selected = this.selection.total_checked === this.filteredStaffers.length;
    };

    this.selectAll = function () {
        let isSelected  = this.selection.all_selected;

        this.selection.total_checked = isSelected ? this.filteredStaffers.length : 0;

        this.filteredStaffers = this.filteredStaffers.reduce((staffers, staff) => {
            staff.checked = isSelected;
            staffers.push(staff);

            return staffers;
        }, []);
    };

    this.getLength = function () {
        let totalLength = this.filteredStaffers && this.filteredStaffers.length || 0;

        if(totalLength > 0) {
            let clearCount = this.staffers.filter(staff => {
                return staff.work_status_cap !== WITHDRAWN_STATUS;
            });

            if(clearCount.length !== totalLength) {
                return totalLength + '/' + clearCount.length;
            }
        }

        return totalLength;
    };

    this.getPaymentOptionLabel = function (payment_option) {
        return PAYMENT_OPTION_LABEL[payment_option] && PAYMENT_OPTION_LABEL[payment_option].label;
    };

    this.onFilterChange = function (status, filter) {
        this.filters[filter] = status;

        collectList();
        this.clearSelection();
    };

    this.clearSelection = function () {
        this.filteredStaffers.forEach((s) => {
            s.checked = false;
        });
        this.selection.all_selected = false;
        this.selection.total_checked = 0;
    };

    this.changeOrder = function (column) {
        this.order.column   = column;
        this.order.reverse  = !this.order.reverse;

        collectList()
    };

    let collectList = function () {
        let predicate = getOrderPredicate(self.order.column);

        self.filteredStaffers = __orderList(
            __filterList(self.staffers, self.filters), predicate, self.order.column, self.order.reverse
        );

        recountSelectedStaffers(self.filteredStaffers);
    };

    let getOrderPredicate = function (orderColumn) {
        return (orderColumn === 'need_hotel_room' || orderColumn === 'has_restrictions')
                ?orderPredicates.booleanOrder
                :orderPredicates.stringOrder;
    };

    let orderPredicates = {
        stringOrder     : (item, prop) => item[prop],
        booleanOrder    : (item, prop) => !!item[prop]
    };

    let __orderList = function (list, predicate, col, reverse) {
        return $filter('orderBy')(list, [(item) => predicate(item, col), 'last'], reverse);
    };

    let __filterList = function (list, filters) {
        return $filter('filter')(list, function (item) {
            return __searchFilter(item, filters.search) &&
                   __workStatusFilter(item, filters.work_status) &&
                   __hotelFilter(item, filters.hotel)
        });
    };

    let __searchFilter = function(item, search) {
        function lower (str) {
            return str && str.toLowerCase();
        }

        if(!search) {
            return true;
        } else {
            search = lower(search);
        }

        let first       = lower(item.first),
            last        = lower(item.last),
            usav_num    = lower(item.usav_num),
            email       = lower(item.email);

        return (
            (first && first.includes(search) ) || 
            (last && last.includes(search)) || 
            (usav_num && usav_num.includes(search)) || 
            (email && email.includes(search))
        );
    };

    let __workStatusFilter = function (item, status) {
        if(!status) {
            self.showWithdrawn = false;

            if (item.work_status_cap !== WITHDRAWN_STATUS) {
                return true;
            }
        }

        if (status === WITHDRAWN_STATUS) {
            self.showWithdrawn = true;
        }

        if (status === 'schedule_name_duplicates') {
            return item.schedule_name_duplicates;
        }

        return item.work_status_cap === status;
    };

    let __hotelFilter = function (item, hotel) {
        if(!hotel) {
            return true;
        }

        return ((hotel === 'yes' && item.need_hotel_room)    ||
                (hotel === 'no'  && !item.need_hotel_room)   ||
                (hotel === 'notSelected' && item.need_hotel_room && _.isEmpty(item.hotel_nights_required)));
    };

    this.formatDate = function (date) {
        return moment(date).toDate();
    };

    const getChosenStaffers = () => {
        return this.filteredStaffers
            .filter(staff => staff.checked)
            .map(staff => staff.official_id);
    };

    this.openSanctioningCheckUpdateModal = function ($event, staff) {
        $event.stopPropagation();
        EventStaffService.openSanctioningCheckUpdateModal(staff, STAFF_MEMBER_TYPE, this.eventSportSanctioningId);
    };

    this.openRestrictionsUpdateModal = function (ev, staff) {
        ev.stopPropagation();
        EventStaffService.openOfficialEditModal(staff);
    };

    function loadData() {
        EventStaffService.getAll($stateParams.event)
            .then(data => {
                self.staffers           = data.role_data;
                self.eventSportSanctioningId = data.event.sport_sanctioning_id;
                collectList();
            })
    }

    function reloadStaffer (data) {
        self.staffers = self.staffers.map(staffer => {
            if(staffer.event_official_id === data.event_official_id) {
                staffer = data;
                staffer.has_restrictions =
                    _.isString(data.additional_restrictions) && data.additional_restrictions.length > 0;
            }

            return staffer;
        });
        collectList();
    }

    const recountSelectedStaffers = (staffers) => {
        this.selection.total_checked = staffers.filter(staff => staff.checked).length;
    };

    $rootScope.$on('reload.staff.list', function (ev, data) {
        reloadStaffer(data);
    });
    $rootScope.$on('reload.official.list', function (ev, data) {
        reloadStaffer(data);
    });

    this.openInfoModal = function(e) {
        var id = angular.element(e.target).closest('tr').attr('of-id');

        $state.go(APP_ROUTES.EO.STAFFERS_INFO, { staffer: id, filters: this.filters }, { notify: false });
    }

    this.changeWorkStatus = function () {
        if(!this.group_work_status) {
            return;
        }

        if(this.selection.total_checked === 0) {
            return;
        }

        EventStaffService.updateStaffersInfo(
            $stateParams.event,
            getChosenStaffers(),
            { staff_work_status: this.group_work_status }
        ).then(() => {
            this.selection.all_selected = false;
            this.selection.total_checked = 0;
            loadData();
        })
    };

    this.exportExcel = function() {
        const timezone = moment.tz.guess(true);

        EventStaffService.excelExport($stateParams.event, getChosenStaffers(), timezone);
        this.selection.all_selected = false;
        this.selection.total_checked = 0;
        this.selectAll();
    }

    this.openEmailDialog = () => {
        AEMSendDialogService.openDialog({
            eventID: $stateParams.event,
            filters: { staffers: getChosenStaffers() },
            replyTo: eventDashboardService.event.email,
            group  : 'staff'
        })
        .then(() => {
            this.selection.all_selected = false;
            this.selection.total_checked = 0;
            this.selectAll();
        })
        .catch(function (err) {
            if (err instanceof Error) {
                console.error(err);
            }
        });
    }

    function openStaffPayouts () {
        $state.go(APP_ROUTES.EO.STAFF_PAYOUTS, { event: $stateParams.event });
    }
}
