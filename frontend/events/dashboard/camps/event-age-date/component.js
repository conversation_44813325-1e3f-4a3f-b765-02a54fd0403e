class Component {
    constructor(
        $timeout,
        toastr,
        $stateParams,
        campsService,
        $rootScope,
        APP_ROUTES,
        $state,
        ConfirmationService
    ) {
        this.$timeout               = $timeout;
        this.toastr                 = toastr;
        this.eventId                = $stateParams.event;
        this.campsService           = campsService;
        this.$rootScope             = $rootScope;
        this.UNTRACKED_STATES       = [APP_ROUTES.EO.CAMPS];
        this.$state                 = $state;
        this.confirmationService    = ConfirmationService;

        this.isOpen                 = false;
        this.alreadyAsked           = false;
    }

    $onInit() {
        this.$rootScope.$on('$stateChangeStart', this.onStateChangeStart.bind(this));
    }

    get askToSave() {
        return !angular.equals(this.eventAgeDate, this.lastSavedAgeDate);
    }

    get isNotEqual() {
        const dates = [this.eventDateStart, this.defaultAgeDate];

        return dates.every(date => !angular.equals(this.eventAgeDate, date));
    }

    toggleDatePicker() {
        this.isOpen ^= true;
    }

    saveEventAgeDate(byClickButton = false) {
        if (byClickButton && this.form.$invalid) {
            this.form.$setSubmitted();
            return false;
        }

        const formattedEventAgeDate = moment(this.eventAgeDate).format('MM/DD/YYYY');

        return this.campsService.saveEventAgeDate(this.eventId, formattedEventAgeDate)
            .then(() => {
                this.lastSavedAgeDate = this.eventAgeDate;
                this.toastr.success('Event Age Date Updated');
            });
    }


    checkForAutoSave() {
        // autosave when event age date set from null to not null
        if (!this.lastSavedAgeDate) {
            this.saveEventAgeDate();

            this.$timeout(() => {
                this.isOpen = false
            }, 0);
        }
    }

    isAgeDateEqualTo(date) {
        return angular.equals(this.eventAgeDate, date);
    }

    setEventAgeDateTo(date) {
        this.eventAgeDate = date;
        this.checkForAutoSave();
    }

    onStateChangeStart(event, toState) {
        if (this.UNTRACKED_STATES.indexOf(toState.name) === -1) {
            if (!this.alreadyAsked && this.askToSave) {
                event.preventDefault();

                const message = 'Event Age has been changed. Save?';

                this.confirmationService.ask(message)
                    .then(result => {
                        if (result === this.confirmationService.YES_RESP) {
                            // save and go
                            this.saveEventAgeDate()
                                .then(() => {
                                    this.$state.go(toState.name);
                                })
                                .catch(() => {
                                    // leave anyway
                                    this.alreadyAsked = true;
                                    this.$state.go(toState.name);
                                });
                        } else if (result === this.confirmationService.NO_RESP) {
                            // do not save and go
                            this.alreadyAsked = true;
                            this.$state.go(toState.name);
                        }
                    });
            }
        }
    }
}

Component.$inject = [
    '$timeout',
    'toastr',
    '$stateParams',
    'campsService',
    '$rootScope',
    'APP_ROUTES',
    '$state',
    'ConfirmationService',
];

window.EventAgeDateComponent = Component;


