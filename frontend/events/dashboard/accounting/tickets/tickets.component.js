angular.module('SportWrench').component('accountingTickets', {
    templateUrl 	: 'events/dashboard/accounting/tickets/tickets.html',
    controller 		: [
        'EventTransfersService', 'eventDashboardService', 'UtilsService', '$q', '$timeout', '$rootScope',
        AccountingEntryFeesController]
})

function AccountingEntryFeesController (
    EventTransfersService, eventDashboardService, UtilsService, $q, $timeout, $rootScope
) {
    var _self 	= this;
    var _event = eventDashboardService.getEvent()

    var eventID = _event.event_id;

    this.stats 					= {};
    this.usdAvailable 			= 0;
    this.transfers 		        = [];
    this.enableTickets 			= _event.allow_ticket_sales;
    this.ticketsCardsAllowed 	= _event.tickets_cards_allowed;

    this.initLoad 				= true;
    this.applyAnimatedShowing 	= false;

    loadData();

    function loadData () {
        return EventTransfersService.loadTransfersData(eventID, 'tickets').then(function (result) {
            this.accountID      = result.account && result.account.id;
            this.usdAvailable   = result.usdAvailable;
            this.stats          = result.stats;
            this.transfers      = result.transfers;

            this.initLoad = false;

            $timeout(function () {
                this.applyAnimatedShowing = true
            }.bind(this))
        }.bind(_self));
    }

    this.getAmount = function (cur, val) {
        return UtilsService.getAmount(cur, val);
    }

    this.panelBodyCls = function () {
        return {
            'panel-body panel-smooth': true,
            'showed': this.applyAnimatedShowing
        }
    }

    $rootScope.$on('reloadMerchantStatisticsData', function () {
        loadData();
    })
}
