angular.module('SportWrench')
.service('TotalsAndNetProfitService', ['$http', TotalsAndNetProfitService]);

function TotalsAndNetProfitService ($http) {
    this._$http      = $http;
    this._urlPrefix  = '/api/event/';
    this._page       = 'accounting';
}

TotalsAndNetProfitService.prototype.getTotals = function (eventID, type) {
    return this._$http.get(`${this._urlPrefix}${eventID}/${this._page}/${type}/totals`)
    .then(res => res.data.stats);
}
