angular.module('SportWrench').service('StripeDetailsService', StripeDetailsService);

function StripeDetailsService ($http) {
	this.$http 		= $http;
	this.urlPrefix 	= '/api/event/';
}

StripeDetailsService.prototype.getDetails = function(eventId, paymentFor) {
    return this.$http.get(`${this.urlPrefix}${eventId}/accounting/${paymentFor}/details`)
        .then(res => { 
            return res.data && res.data.details 
        });
}