<div class="row">
    <div class="col-sm-12">
        <spinner active="$ctrl.loading.inProcess"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error">
        <div class="col-sm-12 exhibitors-list_filters_wrapper">
            <exhibitors-filters
                    exhibitors="$ctrl.exhibitors"
                    on-filter="$ctrl.onFilter(filteredExhibitors)"
            ></exhibitors-filters>
            <button ng-click="$ctrl.goToUserExhibitors()" class="btn btn-primary exhibitors-list_add-exhibitor-btn">Add Exhibitor</button>
        </div>
        <div class="col-sm-12">
            <exhibitors-list
                    exhibitors="$ctrl.filteredExhibitors"
                    on-reload-table="$ctrl.loadExhibitors()"
            >
            </exhibitors-list>
        </div>
    </div>
</div>
