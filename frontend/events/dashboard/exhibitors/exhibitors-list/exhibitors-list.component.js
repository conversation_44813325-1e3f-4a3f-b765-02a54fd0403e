class ExhibitorsListComponent {
    constructor(ExhibitorsService, $state, $stateParams) {
        this.ExhibitorsService = ExhibitorsService;
        this.$state = $state;
        this.$stateParams = $stateParams;

        this.eventID = $stateParams.event;
        this.dateFormat = 'MMM d, yyyy';
    }

    openExhibitorInfoModal({ company_name: companyName, sponsor_id: exhibitorID, purchase_id: purchaseId, payment_status: paymentStatus }) {
        this.ExhibitorsService.openExhibitorInfoModal({
            eventID: this.eventID,
            companyName,
            exhibitorID,
            onSave: this.ExhibitorsService.updateExhibitorRegistrationInfo.bind(this.ExhibitorsService),
        })
            .then(reloadTable => {
                if(reloadTable) {
                    this.onReloadTable();
                }
            });
    }

    goToPaymentsList(sponsorId) {
        this.$state.go(this.$stateParams.paymentsListState, {
            filters: { sponsor: sponsorId },
            event: this.$stateParams.event
        })
    }
}

angular.module('SportWrench').component('exhibitorsList', {
    templateUrl: 'events/dashboard/exhibitors/exhibitors-list/exhibitors-list.html',
    bindings: {
        exhibitors: '<',
        onReloadTable: '&',
    },
    controller: [
        'ExhibitorsService',
        '$state',
        '$stateParams',
        ExhibitorsListComponent],
})
