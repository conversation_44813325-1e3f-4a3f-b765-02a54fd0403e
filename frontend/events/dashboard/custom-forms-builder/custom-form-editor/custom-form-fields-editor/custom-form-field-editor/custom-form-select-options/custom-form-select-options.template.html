<div class="row rowm0 additional-select-modal">
    <div class="col-xs-12">
        <div class="form-group" ng-if="!$ctrl.readOnly">
            <label>New option:</label>
            <div class="input-group">
                <input type="text" class="form-control" ng-model="$ctrl.newOption">
                <span class="input-group-btn">
                    <button class="btn btn-success btn-block" ng-click="$ctrl.addOption()">
                        Add
                    </button>
                </span>
            </div>
        </div>
        <ul class="list-group">
            <li class="list-group-item" ng-repeat="option in $ctrl.options track by $index">
                <span
                    class="glyphicon glyphicon-remove red pointer"
                    ng-if="!$ctrl.readOnly"
                    ng-click="$ctrl.removeOption($index)"></span>
                <embedded-edit
                    readonly="$ctrl.readOnly"
                    value="$ctrl.options[$index].label"
                    type="text"
                    size="sm"
                    on-edit-start=""
                    on-edit-end="$ctrl.saveChanges(callback)"
                    hover-view-class="editable-field"
                ></embedded-edit>
            </li>
        </ul>
    </div>
</div>
<external-button
    class="btn btn-default pull-right"
    ng-click="$ctrl.close()"
>Close</external-button>
<external-button
    class="btn btn-success pull-right"
    ng-click="$ctrl.save()"
    ng-if="!$ctrl.readOnly"
>Save</external-button>

