<div class="row custom-form-fields-editor">
    <div class="col-xs-12">
        <div class="row">
            <div class="col-xs-6">
                <h4>Form Fields</h4>
                <p class="text-warning" ng-if="$ctrl.fields.length">Use drag and drop to change the sort order</p>
            </div>
            <div class="col-xs-6 text-right">
                <button class="btn btn-primary" ng-click="$ctrl.addField()">
                    <i class="fa fa-plus" aria-hidden="true"></i>&nbsp;Add New Field
                </button>
            </div>
        </div>
        <div class="row">
            <table class="table table-borderless">
                <thead>
                <tr>
                    <th class="col-xs-4 text-center">Label</th>
                    <th class="col-xs-2 text-center">Type</th>
                    <th class="col-xs-1 text-center">Section</th>
                    <th class="col-xs-3 text-center">Help Text</th>
                    <th class="col-xs-1 text-center">Required</th>
                    <th></th>
                    <th></th>
                </tr>
                </thead>
                <tbody>
                <tr ng-if="!$ctrl.fields.length">
                    <td colspan="7">
                        <uib-alert type="danger text-center">No fields created</uib-alert>
                    </td>
                </tr>
                <tr ng-repeat-start="field in $ctrl.fields track by field.sort_order"
                    data-drop="field.id > 0"
                    jqyoui-droppable="$ctrl.dropRowOptions(field, field.sort_order)"
                    data-drag="field.id > 0"
                    jqyoui-draggable="$ctrl.dragRowOptions(field, field.sort_order)"
                    data-jqyoui-options="$ctrl.jquiDraggableOptions"
                    id= "{{ 'row-' + field.sort_order }}"
                    ng-class="{'table-bottom-border': !$ctrl.utils.fields[field.id].changed && field.id, 'pointer': true}">
                    <td class="col-xs-3 text-center">
                        <textarea class="form-control"
                                  name="label"
                                  ng-model="field.label"
                                  ng-change="$ctrl.onFieldChange(field)"
                                  style="resize: vertical"
                        ></textarea>
                    </td>
                    <td class="col-xs-1 text-center">
                        <select
                            ng-model="field.type"
                            ng-options="type.id as type.name for type in $ctrl.types"
                            class="form-control"
                            ng-disabled="!$ctrl.allowEditing && !field.is_new"
                            ng-change="$ctrl.onFieldChange(field)"
                        ></select>
                    </td>
                    <td class="col-xs-1 text-center">
                        <select
                            ng-model="field.section"
                            ng-options="s.id as s.id for s in $ctrl.sections"
                            class="form-control"
                            ng-change="$ctrl.onFieldChange(field)"
                        ></select>
                    </td>
                    <td class="col-xs-2 text-center">
                        <textarea ng-change="$ctrl.onFieldChange(field)"
                                  class="form-control"
                                  name="help_text"
                                  ng-if="!$ctrl.hideHelpText(field)"
                                  ng-model="field.help_text"
                                  style="resize: vertical"
                        ></textarea>
                    </td>
                    <td class="col-xs-1 text-center">
                        <input type="checkbox"
                               ng-if="!$ctrl.hideIsRequired(field)"
                               ng-change="$ctrl.onFieldChange(field)"
                               ng-model="field.is_required"/>
                    </td>
                    <td class="col-xs-3 text-center">
                    <span ng-if="field.type === 'text'">
                        Variation (<custom-form-text-variation-dropdown
                                    aria-id="new-line-dropdown"
                                    variation="field.variation"
                                    on-save="$ctrl.onVariationChange(field, variation)"
                                ></custom-form-text-variation-dropdown>)
                    </span>
                        <span ng-if="field.type === 'select' || field.type === 'multiselect'">
                        Options <span ng-bind="field.options.length"></span> (<a href="" ng-click="$ctrl.openOptionsEditModal(field)">edit</a>)
                    </span>
                    </td>
                    <td class="col-xs-3 text-center">
                        <button type="button"
                                class="btn btn-danger btn-xs"
                                ng-if="$ctrl.allowEditing || field.is_new"
                                ng-click="$ctrl.remove(field)"
                        >Remove</button>
                    </td>
                </tr>
                <tr ng-repeat-end
                    ng-class="{'table-bottom-border': true}"
                    ng-show="$ctrl.utils.fields[field.id].changed || !field.id">
                    <td colspan="7" class="text-right">
                        <button ng-if="true"
                                class="btn btn-primary"
                                ng-disabled="$ctrl.disableSaveButton(field)"
                                ng-click="$ctrl.save(field)"
                        >Save</button>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
