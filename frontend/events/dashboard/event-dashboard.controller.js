angular.module('SportWrench')

.controller('EventDashboardController', EventDashboardController);

function EventDashboardController(
    $scope, $stateParams, $state, $rootScope, EventACLService, EVENT_OPERATIONS, $uibModal, loadEvent, APP_ROUTES,
    EventDashboardTabsService, userService, SHOW_OFFICIALS_PAYOUTS_TAB_ACTION
) {
    var defaultState            = APP_ROUTES.EO.INFO;
    $scope.eventId              = $stateParams.event;
    $scope.event                = loadEvent;
    $scope.eventName            = $scope.event.long_name;
    $scope.event_statistics     = {};
    $scope.states               = {
        events: APP_ROUTES.EO.EVENTS,
        update: APP_ROUTES.EO.UPDATE_EVENT
    };

    $scope.opts = {
        loading_started     : true,
        loading_finished    : true
    };

    $scope.editEventLinkVisible = function () {
        return userService.hasGodRole() || EventDashboardTabsService.isAllowedByAcl(EVENT_OPERATIONS.EDIT_EVENT);
    };

    $scope.tabs = EventDashboardTabsService.getTabs($scope.event);

    $rootScope.$broadcast('edashboard.eventloaded', $scope.event);

    $scope.isActiveTab = function (state) {
        return $state.is(state);
    };

    $scope.getLocation = function (event) {
        var _location = '';
        if(event.address)
            _location += event.address;
        if(event.city)
            _location += ((_location.length > 0)?', ':'') + event.city;
        if(event.state)
            _location += ((_location.length > 0)?', ':'') + event.state;
        if(event.zip)
            _location += event.zip;
        return (_location === '')?'N/A':_location;
    }

    $scope.getGenders = function (event) {
        var _g = '';
        if(event.has_male_teams)
            _g += 'Male';
        if(event.has_female_teams)
            _g += ((_g.length > 0)?', ':'') + 'Female';
        if(event.has_coed_teams)
            _g += ((_g.length > 0)?', ':'') + 'Coed';
        return _g;
    }

    $scope.showTeamInfoModal = function (team, tab, e, scope) {
        return $uibModal.open({
            templateUrl     : APP_ROUTES.EO.FOLDER + 'event-teams-info.html',
            controller      : 'Event.Team.InfoController',
            scope           : scope,
            resolve         : {
                modalParams: function () {
                    return {
                        event_id    : $stateParams.event,
                        team_id     : team,
                        event       : $scope.event
                    };
                },
                openTab: function () {
                    var tabId = e && e.target.attributes['tabid'] && e.target.attributes['tabid'].value;
                    return (tabId || tab || 1)
                }
            }
        })
    }


    $scope.$on('$destroy', function() {
        $rootScope.$broadcast(SHOW_OFFICIALS_PAYOUTS_TAB_ACTION, false);
    });
}
