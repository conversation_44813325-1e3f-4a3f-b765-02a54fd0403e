angular.module('SportWrench').controller('Event.NewDoublesTeamController', NewDoublesTeamController);

function NewDoublesTeamController($scope, divisionsService, $stateParams, rosterTeamService, $q) {
    $scope.data = {
        divisions: [],
        player_one: {},
        player_two: {}
    };

    $scope.errors = {
        player_one: {},
        player_two: {}
    };

    $scope.payment = {};

    $scope.pay = false;

    $scope.createNewTeam = function () {
        $scope.error = undefined;
        $scope.submitted = true;
        if($scope.pay) {
            _stripePayments().catch((error) => {
                $scope.error = error;
                $scope.submitted = false;
            });
        }
    }

    divisionsService.getDivisions($stateParams.event).then(function(divisions) {
        $scope.data.divisions = divisions;
    });

    $scope.divisionLabel = function (d) {
        var name = d.name;
        if(d.gender === 'male')
            name += ' (M)';
        else if(d.gender === 'female')
            name += ' (F)';
        else if(d.gender === 'coed')
            name += ' (C)';
        if(d.reg_fee && d.reg_fee > 0)
            name += ' $' + d.reg_fee;
        return name;
    }

    function _stripePayments () {
        var defer = $q.defer();
        try {
            Stripe.setPublishableKey($scope.$parent.event.stripe_publishable_key);
            Stripe.card.createToken($('#pay_doubles_card'), function(status, response) {
                if (response.error) {
                    defer.reject(response.error.message);
                } else {
                    defer.resolve(response.id);
                }
            });
        } catch (e) {
            defer.reject(e.message);
        }
        return defer.promise;
    }
}   
