<form class="form-inline row-space">
    <sw-searchbox
        placeholder="Coupon, <PERSON>ail, First, Last"
        input-model="filters.search"
        reload="reloadList()"
    ></sw-searchbox>
    <a class="btn btn-default" ng-click="openImportMenu()">Import</a>
    <a class="btn btn-default" ng-click="openEmailMenu()">Send Emails</a>
    <a class="btn btn-primary" ng-click="createMenu()">Create New</a>
    <div class="form-group">
        <label>
            <input type="checkbox" ng-model="filters.unused" ng-change="reloadList()"> Show Unused
        </label>
    </div>
    <div class="form-group">
        <label>
            <input type="checkbox" ng-model="filters.not_notified" ng-change="reloadList()"> Show not Notified
        </label>
    </div>
</form>
<pagination
    page="pageNum"
    limit="defaultLimit"
    total="data.total"
    current="data.discountsList.length"
    change-page="changePage(page)"
></pagination>
<table class="table table-condensed pointer">
    <thead>
        <tr>
            <th>
                <input type="checkbox" ng-model="utils.selection.all" ng-change="toggleAllItems()">
                <span class="badge badge-dark" ng-bind="utils.selection.selectedItemsCount"></span>
            </th>
            <th>
                <a href="" ng-click="order('email')">Email</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'email'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('first')">First</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'first'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('last')">Last</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'last'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('coupon')">Coupon</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'coupon'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('amount')">Amount</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'amount'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('quantity')">Quantity</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'quantity'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('used')">Used</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'used'}}"></reverse-arrow>
            </th>
            <th>
                <a href="" ng-click="order('type')">Ticket Type</a>
                <reverse-arrow reverse="filters.reverse" show="{{filters.order === 'type'}}"></reverse-arrow>
            </th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat="d in data.discountsList"
            ng-click="getInfo(d)"
            ng-class="rowClass(d)">
            <td ng-click="$event.stopPropagation()">
                <input type="checkbox" ng-model="utils.selection.items[d.id]" ng-change="toggleItem(d.id)">
            </td>
            <td ng-bind="d.email"></td>
            <td ng-bind="d.first"></td>
            <td ng-bind="d.last"></td>
            <td ng-click="$event.stopPropagation()"><a href="{{d.coupon_link}}" target="_blank" ng-bind="d.coupon"></a></td>
            <td ng-bind="d.amount"></td>
            <td ng-bind="d.quantity"></td>
            <td ng-bind="d.used"></td>
            <td ng-bind="d.type_name"></td>
        </tr>
        <tr ng-if="!(utils.loading || data.discountsList.length)">
            <td colspan="9" class="text-center"><i class="fa fa-exclamation-circle"></i> No Discounts found</td>
        </tr>
    </tbody>
</table>
<pagination
    page="pageNum"
    limit="defaultLimit"
    total="data.total"
    current="data.discountsList.length"
    change-page="changePage(page)"
></pagination>
