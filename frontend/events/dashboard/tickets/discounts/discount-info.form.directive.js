angular.module('SportWrench').directive('discountInfoForm', discountInfoForm);

function discountInfoForm (FORM_VALIDATION_ERROR_KEY) {
	return {
		restrict 	: 'E',
		replace 	: true,
		templateUrl : 'events/dashboard/tickets/discounts/discount-info.form.html',
		link 		: function (scope) {
			scope.errors = {};

			scope.submit = function () {
				clearFormErrors();
				scope.sendFormData()
				.then(null, function (resp) {
					var data = resp.data;
					if(data && data.validationErrors) {
						handleValidationErrors(data.validationErrors);
					}
				});
			};

			scope.freeModeChanged = function () {
				scope.discountInfo.amount = (scope.discountInfo.is_free?-1:0);
			};

			scope.showRemoveBtn = function () {
				return !scope.isFormCreateMode && !!scope.removeDiscount;
			}

			function handleValidationErrors (validationErrors) {
				for(var i = 0, l = validationErrors.length, e; i < l; ++i) {
					e = validationErrors[i];
					if(scope.discountInfoForm[e.path[0]]) {
						scope.discountInfoForm[e.path[0]].$setValidity(FORM_VALIDATION_ERROR_KEY, false);
						scope.errors[e.path[0]] = e.message;
					}
				}
			}

			function clearFormErrors () {
				angular.forEach(scope.discountInfoForm.$error[FORM_VALIDATION_ERROR_KEY], function (value) {
					scope.discountInfoForm[value.$name].$setValidity(FORM_VALIDATION_ERROR_KEY, true);
				});
			}
		}
	};
}
