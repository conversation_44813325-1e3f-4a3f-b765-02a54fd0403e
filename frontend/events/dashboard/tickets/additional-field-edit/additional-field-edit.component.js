angular.module('SportWrench').component('additionalFieldEdit', {
    templateUrl: 'events/dashboard/tickets/additional-field-edit/additional-field-edit.html',
    bindings: {
        payment             : '<',
        additionalField     : '<',
        onUpdate            : '&',
        inlineEditingField  : '<'
    },
    controller: [AdditionalFieldEditController]
});

function AdditionalFieldEditController () {
    this.update = function (fieldName, fieldValue) {
        if(this.inprogress) return;

        this.onUpdate({ payment: this.payment, fieldName, fieldValue })
    };

    this.focusField = function () {
        this.inprogress = false;
    };

    this.isTextField   = this.inlineEditingField.type === 'text';
    this.isSelectField = this.inlineEditingField.type === 'select';

    this.getValue = function () {
        if(this.isTextField) {
            return this.additionalField.value;
        } else if(this.isSelectField) {
            return this.inlineEditingField.options[this.additionalField.value];
        }
    };
    
    this.onKeypress = function (e, value) {
        if(e.charCode === 13) {
            this.inprogress = true;
            this.onUpdate({ payment: this.payment, fieldName: this.additionalField.field, fieldValue: value })
        }
    }
}
