angular.module('SportWrench').directive('ticketsPaymentDebt', function () {
	return {
		restrict: 'E',
		scope: {
			amount 		: '=',
			saveCheck 	: '&',
			refundDebt 	: '&'
		},
		templateUrl: 'events/dashboard/tickets/payment/debt/debt.html',
		link: function (scope) {
			scope.debtRefund = function () {
				scope.refundInProgress = true;
				scope.refundDebt().finally(()=>scope.refundInProgress = false);
			}
			scope.moduleDebt = function () {
				if(!scope.amount) return 0;
				return Math.abs(scope.amount);
			}

			scope.debtor = function () {
				if(scope.amount > 0) {
					return 'Buyer'
				} else {
					return 'Event Owner'
				}
			}

			scope.payOffCheck = function (data, done) {
				scope.saveCheck({
					data 	: data,
					done 	: done
				})
			}
		}
	}
})