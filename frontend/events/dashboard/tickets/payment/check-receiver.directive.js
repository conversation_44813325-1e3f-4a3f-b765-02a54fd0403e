angular.module('SportWrench').directive('checkReceiver', function () {
	return {
		restrict: 'E',
		scope: {
			title	: '@',
			action  : '&'
		},
		transclude 		: true,
		templateUrl 	: 'events/dashboard/tickets/payment/check-receiver.html',
		link: function (scope) {
			scope.checkEntryMenu = {
				isOpen 			: false,
				isPickerOpen 	: false,
				formSubmitted 	: false
			}
			scope.check = {};
			scope.test = {}
			scope.toggleCheckForm = function () {
				scope.checkEntryMenu.isOpen = !scope.checkEntryMenu.isOpen;
			}
			scope.accept = function () {
				scope.checkEntryMenu.formSubmitted = true;
				if(scope.checkEntryMenu.checkForm.$invalid) return;
				scope.action({
					data: scope.check,
					done: function (err) {
						scope.checkEntryMenu.formSubmitted = false;
						if(!err) {
							scope.toggleCheckForm();
							scope.check.notes = '';
							scope.check.received_at = null;
							scope.check.check_num = '';
						}
					}
				})
			}
		}
	}
})