<div class="panel panel-info">
	<div class="panel-heading">Update Available Ticket Count</div>
	<div class="panel-body">
		<div ng-repeat="t in ticketsList"
			 class="form-group">
			<label ng-bind="label(t)"></label>
			<select class="form-control"
					ng-model="redeemable[t.purchase_ticket_id]"
					ng-init="redeemable[t.purchase_ticket_id] = t.available"
				    ng-options="r as r for r in availableTickets(t)"
				    ng-disabled="isNotScanned(t)"
				    >
			</select>
		</div>
		<button class="btn btn-danger" ng-click="redeem()" ng-disabled="disableRedeem()">Hold Tickets</button>
		<div class="alert-block">
	      <result-alert
	          type="resultAlert.type"
	          msg="resultAlert.msg">
	      </result-alert>
	    </div>
	</div>
</div>