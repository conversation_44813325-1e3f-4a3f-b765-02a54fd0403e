<form class="form-horizontal mt-5" name="$ctrl.exhibitorTicketForm" ng-submit="$ctrl.submit()">
    <div ng-class="{ 'form-group': true, 'validation-required': true, 'has-error': $ctrl.exhibitorTicketForm.$submitted && $ctrl.exhibitorTicketForm.first.$invalid }">
        <label class="control-label col-sm-3">First</label>
        <div class="col-sm-8">
            <input type="text" name="first" class="form-control" ng-model="$ctrl.exhibitorTicketInfo.first" required>
        </div>
    </div>
    <div ng-class="{ 'form-group': true, 'validation-required': true, 'has-error': $ctrl.exhibitorTicketForm.$submitted && $ctrl.exhibitorTicketForm.last.$invalid }">
        <label class="control-label col-sm-3">Last</label>
        <div class="col-sm-8">
            <input type="text" name="last" class="form-control" ng-model="$ctrl.exhibitorTicketInfo.last" required>
        </div>
    </div>
    <div ng-class="{ 'form-group': true, 'validation-required': true, 'has-error': $ctrl.exhibitorTicketForm.$submitted && $ctrl.exhibitorTicketForm.email.$invalid}">
        <label class="control-label col-sm-3">Email</label>
        <div class="col-sm-8">

            <input type="email" name="email" class="form-control" email-validator ng-model="$ctrl.exhibitorTicketInfo.email" required>
        </div>
    </div>
    <div ng-class="{ 'form-group': true, 'validation-required': true, 'has-error': $ctrl.exhibitorTicketForm.$submitted && $ctrl.exhibitorTicketForm.phone.$invalid}">
        <label class="control-label col-sm-3">Phone</label>
        <div class="col-sm-8">
            <input type="tel" name="phone" class="form-control" ng-model="$ctrl.exhibitorTicketInfo.phone" ui-mask="(*************" phone-validator required>
        </div>
    </div>
    <free-ticket-border-colour-form border-colour="$ctrl.exhibitorTicketInfo.border_colour" form="$ctrl.exhibitorTicketForm"></free-ticket-border-colour-form>
    <div class="form-group">
        <label class="control-label col-sm-3">Ticket Type:</label>
        <div class="col-sm-8 center-form-text">
            <span>{{$ctrl.getTicketTypeLabel()}}</span>
            <label class="to-right" for="is-free">
                <input disabled id="is-free" type="checkbox" checked>
                Is free
            </label>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-11">
            <button class="btn btn-primary pull-right" type="submit">Send</button>
        </div>
    </div>
</form>
