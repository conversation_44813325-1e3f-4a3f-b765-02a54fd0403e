<div class="btn-group camps-filter" uib-dropdown>
    <button type="button" class="btn btn-default" uib-dropdown-toggle id="chooseCampButton">
        {{$ctrl.campsFilterTitle}} <span class="caret"></span>
    </button>
    <ul class="dropdown-menu" role="menu" ng-click="$ctrl.onChooseCamp($event)">
        <li
            role="menuitem"
            ng-repeat="c in $ctrl.campsList"
            camp-id="{{c.id}}"
            ng-class="{ 'active': c.id == $ctrl.filters.camp }"
        >
            <a href=""><strong ng-bind="c.name"></strong> {{c.date_start}}</a>
            <ul class="sub-drop-down" ng-if="c.types.length > 1">
                <li
                    ng-repeat="t in c.types | orderBy:c.order"
                    type-id="{{t.id}}"
                    ng-class="{ 'active': $ctrl.filters.type == t.id }"
                >
                    <a href=""><i class="fa fa-angle-double-right"></i> {{t.label}}</a>
                </li>
            </ul>
        </li>
    </ul>
</div>
