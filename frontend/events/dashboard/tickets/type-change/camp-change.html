<div class="panel panel-info">
    <div class="panel-heading">Camp change</div>
    <div class="panel-body">
        <form>
        	<div class="form-group"
        		 ng-repeat="t in receipt"
                 ng-if="t.quantity > 0">
        		<label class="control-label">{{t.camp_name}} ({{t.label}}) - {{t.price_formatted}}</label>
        		<div>
        			<select class="form-control"
        					ng-model="receiptChanges[t.purchase_ticket_id]"
        					ng-options="type as label(type) group by type.camp_name for type in filterTypes(t)"
        					ng-change="recountPrice()">
        				<option value="">Choose New Camp ...</option>
        			</select>
        		</div>
        	</div>
            <div class="form-group">
                <strong>Total:</strong> {{data.total | currency}}
            </div>
            <div class="form-group" ng-if="data.total !== initialTotal">
                <strong>Old Total:</strong> {{initialTotal |currency }}
            </div>
            <div class="form-group" ng-if="data.debt">
                <strong>{{debtorName()}}: </strong> {{data.debt | currency}}
            </div>
        	<button class="btn btn-primary" ng-click="save()">Apply</button>
        </form>
        <div class="alert-block">
          <result-alert
              type="resultAlert.type"
              msg="resultAlert.msg">
          </result-alert>
        </div> 
    </div>
</div>
