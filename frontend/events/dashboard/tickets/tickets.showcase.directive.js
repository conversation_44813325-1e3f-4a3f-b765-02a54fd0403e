angular.module('SportWrench').directive(
    'ticketsShowcase',
    ['$filter', 'UtilsService', 'eventDashboardService', ticketsShowcase]
);
function ticketsShowcase ($filter, UtilsService, eventDashboardService) {
    var DATE_FORMAT     = 'MM/dd/yyyy HH:mm+00';
    var DATE_FORMAT_UI  = 'MM/dd/yyyy HH:mm';
    return {
        restrict: 'E',
        scope: {
            load: '&',
            exportReport: '&',
            data: '=statData'
        },
        templateUrl: 'events/dashboard/tickets/tickets.showcase.html',
        link: function (scope, elem) {
            var dateFilter  = $filter('date'), fromDateVal, toDateVal;
            var approx      = UtilsService.approxNumber.bind(UtilsService);
            const currentEvent = eventDashboardService.getEvent();

            scope.filters   = {};
            scope.utils     = {
                typesQty: scope.data.ticket_types.length
            };

            scope.changeDate = function () {
                fromDateVal = scope.filters.from    && dateFilter(scope.filters.from, DATE_FORMAT);
                toDateVal   = scope.filters.to      && dateFilter(scope.filters.to, DATE_FORMAT);
                
                scope.load({
                    from    : fromDateVal,
                    to      : toDateVal
                }).then(function (data) {
                    scope.utils.typesQty = data.ticket_types.length;
                });
            };

            scope.export = function () {
                fromDateVal = scope.filters.from    && dateFilter(scope.filters.from, DATE_FORMAT);
                toDateVal   = scope.filters.to      && dateFilter(scope.filters.to, DATE_FORMAT);

                scope.exportReport({
                    from    : fromDateVal,
                    to      : toDateVal,
                });
            };

            scope.showDate = function (date) {
                return dateFilter(date, DATE_FORMAT_UI) || 'edit date';
            };

            scope.toggleFrom = function () {
                scope.utils.fromOpen = !scope.utils.fromOpen;
            };

            scope.toggleTo = function () {
                scope.utils.toOpen = !scope.utils.toOpen;
            };

            scope.getBalanceDesc = function () {
                if (scope.data.to_transfer > 0) {
                    return 'Amount SportWrench Owes You'
                } else if (scope.data.to_transfer < 0) {
                    return 'Amount You Still Owe to SportWrench'
                } else {
                    return '';
                }
            }

            scope.trTypeIcon = function (t) {
                return {
                    fa                          : true,
                    'fa-cc-stripe text-info'    : (t.type === 'payment_method_fee'),
                    'fa-university'             : (t.type === 'check')
                };
            };

            scope.hightlightElem = function (id) {
                 elem.find('#' + id).addClass('bg-warning');
            }

            scope.fadeElem = function (id) {
                 elem.find('#' + id).removeClass('bg-warning')
            }

            scope.toTransferClass = function () {
                return {
                    'text-danger'   : (scope.data.to_transfer < 0)
                };
            };

            scope.isFiltersClear = function () {
                return !(fromDateVal || toDateVal);
            };

            scope.isVisiblePending = function () {
                return currentEvent.require_tickets_names === false;
            };

            scope.showScannedUniqueTickets = function() {
                return currentEvent.require_tickets_names && scope.data.scanned_unique_tickets.length;
            }

            scope.showUnclaimedFilter = function() {
                return scope.data.filters.unclaimed !== null
            }

            scope.showParkingFilter = function() {
                return scope.data.filters.parking && scope.data.filters.parking.qty;
            }

            scope.showVipFilters = function() {
                const rules = [
                    scope.isFiltersClear(),
                    currentEvent.enable_free_tickets,
                ];

                return rules.every(rule => rule);
            }
        }
    };
}

angular.module('SportWrench').directive('paymentsLink',['$state', '$filter', 'APP_ROUTES', '_', paymentsLink]);
function paymentsLink ($state, $filter, APP_ROUTES, _) {
    return {
        restrict    : 'E',
        scope       : {
            title       : '@',
            param       : '@',
            value       : '='
        },
        template: 
            '<div class="inline-block padding-sm">' +
                '<strong>{{::title}}</strong><span ng-if="isObj()"> ({{value.qty}})</span>: ' + 
                '<span>{{amount()}}</span>;' +
            '</div>',
        link: function (scope) {
            var currencyFilter = $filter('currency');

            scope.openPaymentsPage = function () {
                $state.go(APP_ROUTES.EO.TICKETS_PAYMENTS, {
                    mode    : scope.param,
                    value   : true
                });
            }

            scope.isObj = function () {
                return _.isObject(scope.value);
            }

            scope.amount = function () {
                return (scope.isObj())?currencyFilter(scope.value.amount):scope.value;
            }
        }
    }
};
