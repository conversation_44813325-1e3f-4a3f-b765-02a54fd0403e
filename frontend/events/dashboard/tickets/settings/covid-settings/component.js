
class Controller {
    $onInit () {
        this.require_covid_test_for_each_ticket = this.event.require_covid_test_for_each_ticket;
    }

    updateSettings () {
        this.updateTicketsSettings({
            settings: { require_covid_test_for_each_ticket: this.require_covid_test_for_each_ticket }
        })
    }
}

angular.module('SportWrench').component('covidSettings', {
    templateUrl: 'events/dashboard/tickets/settings/covid-settings/template.html',
    bindings: {
        event: '<',
        updateTicketsSettings: '&',
    },
    controller: Controller,
});
