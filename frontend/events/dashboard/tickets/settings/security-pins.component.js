angular.module('SportWrench').component('securityPins', {
    templateUrl: 'events/dashboard/tickets/settings/security-pins.html',
    bindings: {
        event: '<',
    },
    controller: [ '$stateParams', 'ticketsService', SecurityPins ],
});

function SecurityPins($stateParams, ticketsService) {

    this.$onInit = () => {
        ticketsService.getEventPins($stateParams.event)
            .then(pins => {
                this.pins = pins;
            });
    };

    this.save = () => {
        ticketsService.saveEventPins($stateParams.event, _.mapObject(this.pins, v => v || null));
    };

    this.isVisible = () => this.event.camps_sales === false && this.pins;

}
