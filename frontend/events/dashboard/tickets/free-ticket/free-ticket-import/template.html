<form class="form-horizontal mt-5" novalidate name="$ctrl.form" ng-submit="$ctrl.save()">
    <div class="import-progress" ng-if="$ctrl.submitting">
        <div class="text-center">
            Generating...
        </div>
        <progress min="0" max="1" ng-value="$ctrl.progress" style="width: 100%"></progress>
    </div>
    <free-ticket-border-colour-form border-colour="$ctrl.borderColour" form="$ctrl.form"></free-ticket-border-colour-form>
    <div ng-class="{'form-group validation-required': true, 'has-error': $ctrl.form.$submitted && $ctrl.form.file.$invalid }">
        <label class="control-label col-sm-3">Import File</label>
        <div class="col-sm-8">
            <input type="file"
                   id="file"
                   name="file"
                   ng-model="$ctrl.file"
                   file-change="$ctrl.onChanges()"
                   required
            > Upload file (XLSX, CSV)
            <br/>
            <a href="/data/import/vip_tickets_import_demo.xlsx">Download a sample</a>
        </div>
        <div class="col-sm-12" ng-if="$ctrl.isErrorVisible()">
            <uib-alert type="danger">{{ $ctrl.error }}</uib-alert>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-sm-3">Ticket Type:</label>
        <div class="col-sm-8 center-form-text">
            <span>{{$ctrl.getTicketTypeLabel()}}</span>
            <label class="to-right" for="is-free">
                <input disabled id="is-free" type="checkbox" checked>
                Is free
            </label>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-11">
            <button class="btn btn-success pull-right" type="submit" ng-disabled="$ctrl.isSubmitDisabled()">Add</button>
        </div>
    </div>
</form>
