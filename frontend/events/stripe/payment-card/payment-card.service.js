angular.module('SportWrench').service('PaymentCardService', [
    '$http', '$uibModal', 'STRIPE_PAYMENT_TYPE', '$stateParams', '$location', PaymentCardService
]);

function PaymentCardService ($http, $uibModal, STRIPE_PAYMENT_TYPE) {
    this._$http = $http;
    this._$uibModal = $uibModal;
    this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;
}

Object.defineProperty(PaymentCardService.prototype, 'CARD_BRAND_ICON', {
    value: {
        visa: 'fa-cc-visa',
        unionpay: 'fa-credit-card',
        mastercard: 'fa-cc-mastercard',
        jcb: 'fa-cc-jcb',
        discover: 'fa-cc-discover',
        diners_club: 'fa-cc-diners-club',
        cartes_bancaires: 'fa-credit-card',
        amex: 'fa-cc-amex'
    },
    writable        : false,
    configurable    : false
});

Object.defineProperty(PaymentCardService.prototype, 'UNKNOWN_CARD_BRAND_ICON', {
    value: 'fa-credit-card',
    writable: false,
    configurable: false
});

PaymentCardService.prototype.getCardBrandClass = function (cardBrand) {
    if(cardBrand && !Object.keys(this.CARD_BRAND_ICON).includes(cardBrand)) {
        return this.UNKNOWN_CARD_BRAND_ICON;
    }

    return this.CARD_BRAND_ICON[cardBrand];
}

PaymentCardService.prototype.getCardLabel = function (card) {
    if(_.isEmpty(card) || !_.isObject(card)) {
        return;
    }

    let { card_brand, card_last_4, card_exp_month, card_exp_year, holder_name, type, bank_name, bank_account_last_4 } = card;

    if(type === this.STRIPE_PAYMENT_TYPE.CARD) {
        return `${holder_name} - ${card_brand.toUpperCase()}  **** ${card_last_4} (${card_exp_month}/${card_exp_year})`;
    }

    if(type === this.STRIPE_PAYMENT_TYPE.ACH) {
        return `${holder_name} - ${bank_name}  **** ${bank_account_last_4}`;
    }
    
    return ''
}

PaymentCardService.prototype.openPaymentCardModal = function (activeTab) {
    return this._$uibModal.open({
        template: '<modal-wrapper><payment-card-modal active-tab="activeTab"></payment-card-modal></modal-wrapper>',
        controller: ['$scope', function ($scope) {
            $scope.modalTitle = '<h4>Payment Cards</h4>';
            $scope.modalShowClose = true;
            $scope.activeTab = activeTab;
        }]
    }).result;
}

PaymentCardService.prototype.getActiveTab = function (stateParams, locationParams) {
    let activeTabFromSearch = !_.isEmpty(locationParams) && Number(locationParams.active_tab);

    return stateParams.active_tab || activeTabFromSearch;
}

PaymentCardService.prototype.getPaymentCards = function (eventID) {
    return this._$http.get('/api/eo/payment-card', {
        params: { event: eventID },
        paramSerializer: '$httpParamSerializerJQLike'
    }).then(response => response.data);
}

PaymentCardService.prototype.paymentCardCreationSettings = function () {
    return this._$http.get('/api/eo/payment-card/settings').then(response => response.data);
}

PaymentCardService.prototype.savePaymentCard = function (paymentMethodID) {
    return this._$http.post('/api/eo/payment-card', { payment_method_id: paymentMethodID });
}

PaymentCardService.prototype.saveBankAccount = function (financialAccountID) {
    return this._$http.post('/api/eo/payment-bank-account', { financial_account_id: financialAccountID })
}

PaymentCardService.prototype.createBankAccountSession = function () {
    return this._$http.post('/api/eo/payment-bank-account/session').then(response => response.data);
}

PaymentCardService.prototype.removePaymentCard = function (paymentMethodID) {
    return this._$http.delete('/api/eo/payment-card', { data: { payment_method_id: paymentMethodID } });
}

PaymentCardService.prototype.setDefaultPaymentCard = function (paymentMethodID) {
    return this._$http.put('/api/eo/payment-card/default', { payment_method_id: paymentMethodID });
}
