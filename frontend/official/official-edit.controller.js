angular.module('SportWrench')
.controller('OfficialEditController', OfficialEditController);

function OfficialEditController (
    $scope, $rootScope, $uibModalInstance, $state, userService, geoService, officialService, $q, toastr, INVALID_FORM_ERROR_MSG,
    APP_ROUTES, moment, ProfileRestoreAlertService, USER_ROLE
) {
	var mode                       = $state.is(APP_ROUTES.OF.INFO_NEW) ? 'create' : 'update';

    $scope.details                      = {};
	$scope.details.is_official          = false;
    $scope.details.is_staff             = false;
    $scope.details.sanctioned_by_usav   = false;
    $scope.details.sanctioned_by_aau    = false;
    $scope.loaded                       = false;
    $scope.title_prefix                 = mode;
    $scope.pickerStartOpened            = false;

    $scope.user                         = {};
    $scope.geo_data                     = {};

    $scope.showRestoreProfileAlert      = false;
    $scope.isSubmitted                  = false;

    loadData();

    $scope.ranks                        = officialService.getRanks();

    function loadData () {
        $q.all([
            officialService.officialRes().find(),
            userService.getUser()
        ]).then(function (results) {
            angular.extend($scope.details, results[0].official[0]);
            $scope.user              = results[1];
            $scope.details.country   = $scope.user.country;

            $scope.details.sanctioned_by_usav = !!$scope.details.usav_num;
            $scope.details.sanctioned_by_aau = !!$scope.details.aau_number;

            if ($scope.details.birthdate) {
                $scope.details.birthdate = new Date($scope.details.birthdate);
            }

            $scope.initClothes = angular.copy($scope.details.clothes);

            const queryMethods = [
                geoService.getStates($scope.user.country),
                geoService.getRegions($scope.user.country)
            ];

            if (mode === 'create') {
                queryMethods.push(officialService.getClothes());
            }

            return $q.all(queryMethods);
        }).then(function (results) {
            $scope.geo_data.states      = results[0];
            $scope.geo_data.regions     = results[1];
            $scope.loaded = true;

            if (mode === 'create') {
                $scope.details.clothes = results[2];
            }

            $scope.initDetails = angular.copy($scope.details);
            $scope.showProfileRestoreAlert = ProfileRestoreAlertService.showAlert(USER_ROLE.OFFICIAL, $scope.details.modified);
        });
    }

    $scope.isStaffOnly = function () {
        return $scope.details.is_staff && !$scope.details.is_official;
    };

    $scope.hasUSAVSanctioning = function () {
        return $scope.details.sanctioned_by_usav;
    };

    $scope.hasAAUSanctioning = function () {
        return $scope.details.sanctioned_by_aau;
    };

    $scope.checkSelectedRole = function(role) {
		$scope.details[role] = true;
	};

	$scope.isUS = function() {
	    return $scope.user.country === 'US';
    };

    $scope.getAlias = function(gender) {
        // in case gender Undefined we are using male as default
        return officialService.getGenderAlias(gender, 'male');
    };

	$scope.submit = function() {
        if($scope.profileForm.$invalid) {
            toastr.warning(INVALID_FORM_ERROR_MSG);
            return;
        }

		if ($scope.isStaffOnly()) {
			delete $scope.details.advancement;
			delete $scope.details.rank;
		}

        if(!$scope.hasUSAVSanctioning()) {
            $scope.details.region = null;
            $scope.details.usav_num = null;
        }

        if(!$scope.hasAAUSanctioning()) {
            $scope.details.aau_region = null;
            $scope.details.aau_number = null;
        }

        $scope.details.state         = $scope.details.state || null;
        $scope.details.is_staff_only = $scope.isStaffOnly();

        let official = angular.copy($scope.details);


        official.clothes = $scope.details.clothes
            .map(item => _.pick(item, ['size', 'common_item_id']));

        if (mode === 'update') {
            official.clothes = getChangedClothesFields(official.clothes);
        }

        official.birthdate = moment($scope.details.birthdate).utc().format('YYYY-MM-DD');

        officialService.officialRes()[mode](official, function({usavValidationError, aauValidationError}) {
            $rootScope.$broadcast('off.reload');
            toastr.success('Saved!');

            if(usavValidationError && usavValidationError.validation) {
                toastr.error(usavValidationError.validation);
            }

            if(aauValidationError && aauValidationError.validation) {
                toastr.error(aauValidationError.validation);
            }

            ProfileRestoreAlertService.removeUnsavedFieldsFromLocalStorage(USER_ROLE.OFFICIAL);
            $scope.isSubmitted = true;
            $uibModalInstance.close();
		});
	};

    $scope.getStateLabel = function() {
        return $scope.user.country === 'CA' ? 'Province' : 'State';
    };

    $scope.restoreProfileData = function() {
        $scope.details = ProfileRestoreAlertService.joinUnsavedFieldsWithProfile(USER_ROLE.OFFICIAL, $scope.details);

        if ($scope.details.birthdate) {
            $scope.details.birthdate = new Date($scope.details.birthdate);
        }

        ProfileRestoreAlertService.removeUnsavedFieldsFromLocalStorage(USER_ROLE.OFFICIAL);

        $scope.showProfileRestoreAlert = false;
    };

    const getChangedClothesFields = (clothes) => {
        const changedFields = [];

        if (_.isEqual($scope.initClothes, clothes)) {
            return changedFields;
        }

        for (let i = 0; i < clothes.length; i++) {
            if (clothes[i].size !== $scope.initClothes[i].size) {
                changedFields.push(clothes[i]);
            }
        }

        return changedFields;
    }

    const onModalClosing = function() {
        if ($scope.isSubmitted) {
            return;
        }

        // remove temporary flags
        const _details = _.omit($scope.details, ['is_staff_only']);

        const unsavedFields = ProfileRestoreAlertService.getUnsavedProfileFields($scope.initDetails, _details);

        if (!_.isEmpty(unsavedFields)) {
            ProfileRestoreAlertService.saveUnsavedFieldsToLocalStorage(USER_ROLE.OFFICIAL, unsavedFields, $scope.details.modified);
        }
    };

    $scope.$on('modal.closing', onModalClosing);

    // detect page reload and closing tab
    window.onbeforeunload = function() {
        onModalClosing();
    };
}
