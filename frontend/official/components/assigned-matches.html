<div class="row row-space">
	<div ng-if="showName()" class="col-md-4">
		<h4>{{event.event_name}}</h4>
	</div>
    <div class="col-xs-6" ng-transclude></div>
	<div ng-class="{ 'col-md-2': showName(), 'col-xs-6': !showName() }">
		<button class="btn btn-success pull-right" ng-click="print()">Print Schedule</button>
	</div>
</div>
<div ng-repeat="day in data.eventDays">
    <div ng-if="event.assignments[day].length !== 0" class="panel panel-info">
    	<div class="panel-heading">{{day}}</div>
    	<div class="panel-body">
    		<table class="table table-condensed" ng-show="event.assignments[day].length > 0">
    			<thead>
    				<tr>
    					<th>Time</th>
    					<th>Court</th>
    					<th>Name</th>
                        <th ng-if="!event.past">Score</th>
    				</tr>
    			</thead>
    			<tbody>
    				<tr ng-repeat="m in event.assignments[day]">
    					<td ng-bind="m.start_hour"></td>
    					<td>
                            <span ng-bind="m.court_name"></span>
                            <span ng-if="m.ref_qty > 1">
                                 - <em class="font-bold">R{{m.ref_num}}</em>
                            </span>               
                        </td>
    					<td>{{m.match_name}} - {{m.division_name}}</td>
                        <td ng-if="!event.past">
                            <div ng-show="isFinished(m) || m.edit_scored_link_mode">{{m.results}}</div>
                            <a
                                ng-if="showLink(m)"
                                href="{{getScoresLink(m.match_barcode)}}"
                            >
                                {{getLinkName(m)}}
                            </a>
                        </td>
    				</tr>
    			</tbody>
    		</table>
        </div>
    </div>
</div>
