angular.module('SportWrench').component('editAauPrimaryMembershipId', {
    templateUrl: 'club/edit-club/aau-primary-membership.html',
    bindings: {
        aauPrimaryMembershipId    : '<',

        parentForm                : '<',
        onChange                  : '&',
        hasError                  : '&',
    },
    controller: ['$scope', EditAauPrimaryMembershipIdController]
});

function EditAauPrimaryMembershipIdController ($scope) {
    this.onAAUPrimaryMembershipIdChange = function () {
        let isValid = this.checkAauPrimaryMembershipIdValidity();

        if (isValid) {
            this.onChange({ membership: this.aauPrimaryMembershipId });
        }
    }

    this.checkAauPrimaryMembershipIdValidity = function () {
        if (!$scope.aauPrimaryMembershipComponent) {
            return;
        }

        if (!this.aauPrimaryMembershipId) {
            return false;
        }

        return true;
    }
}
