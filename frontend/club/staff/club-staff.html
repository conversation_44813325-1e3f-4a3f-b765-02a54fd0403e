<form class="form-inline row-space">
    <div class="form-group">
        <div class="search-box">
            <input  type="search" class="form-control search_teams" placeholder="Search ..." ng-model="filters.search">
            <span class="glyphicon glyphicon-search"></span>
        </div>
    </div>
    <gender-dropdown gender="filters.gender"></gender-dropdown>
    <teams-dropdown
        title="Team"
        btn-class="btn btn-default"
        checking="true"
        assigned="filters.assigned"
        selection="filters.teams"
    ></teams-dropdown>
    <div class="btn-group" uib-dropdown>
        <button type="button" class="btn btn-default" uib-dropdown-toggle>
            Coaching Cert <span class="caret"></span>
        </button>
        <ul class="dropdown-menu large-drop-down-menu" role="menu" ng-click="$event.stopPropagation()">
            <li class="row rowm0" ng-repeat="item in filters.cert">
                <div class="col-xs-1">
                    <input type="checkbox" ng-model="filters.certsSelection[item]" >
                </div>
                <div class="col-xs-4">{{getCertLabel(item)}}</div>
            </li>
        </ul>
    </div>
    <sanctioned-body-checkboxes
        sanctioned-body="filters.sanctionedBody"
        ng-show="clubHasUsavAndAauSanctioning"
    ></sanctioned-body-checkboxes>
    <div class="form-group">
        <club-personal-info-merge-button
            club="club"
            members-count="staff.length"
            type="STAFF_MEMBER_TYPE"
            on-update="reloadList()"
        ></club-personal-info-merge-button>
    </div>
</form>
<div class="row row-space">
    <div class="col-sm-3 center-form-text">
        <div>
            <b>Total: </b><span class="badge badge-dark">{{staff.length}}</span>&nbsp;
            <b>Showing: </b><span class="badge badge-dark">{{opts.filtered_staff.length}}</span>
        </div>
    </div>
    <div class="col-sm-9 selected-row over-vis" ng-class="{'show-row': (opts.selected_count > 0)}">
        <b>With selected:</b>
        <span class="badge badge-dark">{{opts.selected_count}}</span>
        <button class="btn btn-danger" ng-click="findEventsAssignedTo()">Remove from teams</button>
        <teams-dropdown
            title="Add to Team"
            btn-class="btn btn-success"
            on-select="findEventsAssignedTo(id)"
        ></teams-dropdown>
        <button class="btn btn-danger"
                sw-confirm="Are you sure you want to remove the selected member(s) from the club?"
                sw-confirm-do="removeFromClub"
                sw-confirm-hide-no>Remove from club
        </button>
    </div>
    <div class="row" ng-if="opts.remove_response">
        <div class="col-sm-12">
            <uib-alert close="closeRemoveAlert()">{{opts.remove_response}}</uib-alert>
        </div>
    </div>
</div>
<table class="table table-condensed" ng-if="!queue_data.requested">
    <thead>
        <tr>
            <th>
                <input type="checkbox" ng-model="opts.all_selected" ng-change="check_all();" ng-disabled="!opts.filtered_staff.length">
            </th>
            <th></th>
            <th class="hideable">
                <a ng-click="order.orderData('gender');" href>G</a>
                <reverse-arrow show="{{order.column == 'gender'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order.orderData('primary_team.organization_code');" href>Primary Team</a>
                <reverse-arrow show="{{order.column == 'primary_team.organization_code'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order.orderData('first');" href>First</a>
                <reverse-arrow show="{{order.column == 'first'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order.orderData('last');" href>Last</a>
                <reverse-arrow show="{{order.column == 'last'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th class="hideable">
                <a ng-click="order.orderData('cert');" href>Cert</a>
                <reverse-arrow show="{{order.column == 'cert'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th class="hideable">
                <a ng-click="order.orderData('cell');" href>Cell</a>
                <reverse-arrow show="{{order.column == 'cell'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th class="hideable">
                <a ng-click="order.orderData('email');" href>Email</a>
                <reverse-arrow show="{{order.column == 'email'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>SafeSport</th>
            <th>BKG</th>
            <th class="mw-100">Sanctioned body</th>
            <th ng-if="false">Webpoint</th> <!-- SW-2011 -->
        </tr>
    </thead>
    <tbody class="pointer">
        <tr ng-repeat="st in opts.filtered_staff | orderBy:order.column:order.reverse"
            ng-click="editStaff(st.master_staff_id)"
            ng-style="utils.webpointLoading[st.master_staff_id]?{'background': '#d9dbdd'}:{}"
        >
            <td ng-click="$event.stopPropagation()">
                <input
                    type="checkbox"
                    ng-model="opts.selection[st.master_staff_id]"
                    ng-change="staffCheckedChanged(st.master_staff_id);">
            </td>
            <td ng-click="$event.stopPropagation()" class="text-left">
                <span
                    class="big-icon glyphicon glyphicon-envelope text-dark"
                    uib-popover-html="st.contacts"
                    popover-trigger="outsideClick"
                    popover-append-to-body="false">
                </span>
            </td>
            <td class="hideable">
                <genders
                    m="st.gender === GENDER_VALUES.MALE"
                    f="st.gender === GENDER_VALUES.FEMALE"
                    nb="st.gender === GENDER_VALUES.NON_BINARY"
                ></genders>
            </td>
            <td ng-click="$event.stopPropagation()"
                td-primary-team
                primary-team="st.primary_team"
                all-teams="st.teams"
                minor-teams-count="st.minorTeamsCount"
                on-primary-team-click="openTeamModal(team)"
                on-minor-teams-click="openMinorTeamsModal(teams, st.first, st.last)"
            ></td>
            <td ng-bind="st.first"></td>
            <td ng-bind="st.last"></td>
            <td class="hideable" ng-bind="st.cert"></td>
            <td class="hideable" ng-bind="st.cell"></td>
            <td class="hideable" ng-bind="st.email"></td>
            <td ng-if="st.safesport_statusid">OK</td>
            <td ng-if="!st.safesport_statusid">NO</td>
            <td ng-bind="st.bkg"></td>
            <td>
                <span ng-if="st.usav_number">USAV</span>
                <span ng-if="st.aau_membership_id" class="list-text-right">AAU</span>
            </td>
            <td ng-click="$event.stopPropagation()" ng-if="false"> <!-- SW-2011 -->
                <a href="" ng-if="!checkedWebpoint[st.master_staff_id]" ng-click="updateWebpoint(st.master_staff_id)">Check</a>
            </td>
        </tr>
        <tr
            ng-if="!opts.filtered_staff && !loading"
            no-data-row
            cs="10"
            text="No available staff found for current season."
        ></tr>
    </tbody>
</table>
<div class="alert alert-warning text-center" ng-if="queue_data.requested">
    Import is still in progress. Please wait...
</div>
