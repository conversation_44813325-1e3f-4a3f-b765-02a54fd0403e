angular.module('SportWrench')

.controller('ClubTeamsController', ClubTeamsController);

function ClubTeamsController (
    $scope, $rootScope, $state, $stateParams, masterClubService, $filter,
    masterClubId, masterTeamService, ClubTeamsService, $uibModal, $q,
    INTERNAL_ERROR_MSG, APP_ROUTES, toastr, teamsData
) {
    var initialTeamsList        = teamsData.teams;

    $scope.clubTeams            = []; 
    $scope.selected_teams_count = 0; 
    masterClubId.value          = $scope.club.sport_id; // ???   
    $scope.utils                = {
        all_selected    : false,
        season          : teamsData.sw_season
    };
    $scope.states               = {
        import      : APP_ROUTES.CD.TEAMS_IMPORT,
        archive     : APP_ROUTES.CD.TEAMS_ARCHIVE
    };
    $scope.filter               = {};

    $scope.clubHasUsavSanctioning = () => masterClubService.clubHasUsavSanctioning($scope.club);

    function loadTeams () {
        ClubTeamsService.teamsList()
        .success(function (data) {
            initialTeamsList = data.teams;
            $scope.refilterItems();
        });
    }

    function filterTeams (teamsList) {
        var search = $scope.filter.search.toLowerCase();
        return $filter('filter')(teamsList, function (team) {
            team.selected = false;
            var teamName = (team.team_name || '').toLowerCase(),
                teamCode = (team.organization_code || '').toLowerCase();

            return (teamName.indexOf(search) >= 0 || teamCode.indexOf(search) >= 0);
        });
    }

    $scope.getAgeLabel = function (age) {
        return age === 0 ? 'Adult' : age;
    }

    $scope.refilterItems = function () {
        $scope.utils.all_selected = false;
        $scope.selected_teams_count = 0;
        $scope.clubTeams = (
            _.isEmpty($scope.filter)
                ?initialTeamsList
                :filterTeams(initialTeamsList)
        );
    };

    $scope.refilterItems();

    $scope.checkAll = function() {
        $scope.selected_teams_count = 0;
        for(var i = 0 ; i < $scope.clubTeams.length; ++i) {
            $scope.clubTeams[i].selected = $scope.utils.all_selected;     
            if($scope.utils.all_selected) {
                $scope.selected_teams_count++;
            }       
        }
    };

    $scope.checkTeam = function(team) {
        if(team.selected) {
            $scope.selected_teams_count++;
        } else {
            if ($scope.selected_teams_count > 0) {
                $scope.selected_teams_count--;
            }
        }
        if($scope.clubTeams.length == $scope.selected_teams_count)
            $scope.utils.all_selected = true;
        else
            $scope.utils.all_selected = false;
    };

    var _openEnteredTeamsModal = function (response) {
        var teams = response && response.data.teams;
        return $q.when(
                    (teams && teams.length)
                    ?$uibModal.open({
                        templateUrl: 'club/teams/entered-list.html',
                        controller: 'Club.Teams.EnteredList',
                        controllerAs: 'data',
                        resolve: {
                            teamsList: function () { return teams; }
                        }
                    }).result
                    :null
                )
    }

    var _removeTeams = function () {
        var selected_teams = [];
        for(var i = 0, l = $scope.clubTeams.length; i < l; ++i) {
            if( $scope.clubTeams[i].selected ) {
                selected_teams.push($scope.clubTeams[i].id);
            }
        }
        if(!selected_teams.length) return;
        masterTeamService.findEnteredTeams(selected_teams)
        .then(_openEnteredTeamsModal)
        .then(function removeTeams (list) {               
            var resultList = (list)?_.difference(selected_teams, list.forbid):selected_teams;
            return $q.when((resultList.length)?masterTeamService.remove(resultList):null)
        }).then(function (response) {
            $scope.utils.all_selected = false;
            $scope.selected_teams_count = 0
            if(response) {
                loadTeams();
            } else {        
                $scope.checkAll();                       
            }
        })
    }

    $scope.removeTeams = function (confirmed) {
        if(confirmed) {
            _removeTeams();
        }
    }

    $scope.showRosterPanel = function (team) {
        $uibModal.open({
            templateUrl     : 'club/teams/edit-team-roster.html',
            controller      : 'EditTeamRosterController',
            resolve         : {
                club: function () {
                    return $scope.club;
                },
                team: function () {
                    return team.id
                }
            }
        }).result.then(function () {
        }, function (updatedTeam) {
            if (!angular.isObject(updatedTeam)) return;

            team.team_name          = updatedTeam.team_name;
            team.age                = updatedTeam.age;
            team.rank               = updatedTeam.rank;
            team.gender             = updatedTeam.gender;
            team.organization_code  = updatedTeam.organization_code;
            team.seasonality        = updatedTeam.seasonality;
        })
    };

    $scope.openCreateModal = function () {
        $uibModal.open({
            templateUrl: 'club/teams/create-team.html',
            controller: 'CreateTeamController',
            resolve: {
                club: function () {
                    return $scope.$parent.club;
                }
            }
        }).result.then(function () {
        }, function (created_team) {  
            if(created_team && created_team.master_team_id)
                $scope.clubTeams.push(created_team);     
        })
    }

    $scope.openResultReportModal = function () {
        $uibModal.open({
            templateUrl: 'club/teams/result-report.html',
            controller: 'ResultReportController',
        });
    }
}
