class ClubsClubInvoicesService {
    constructor ($http, $uibModal) {
        this.$http = $http;
        this.$uibModal = $uibModal;
    }

    getClubInvoicesList (event, params) {
        return this.$http.get(`/api/club/club-invoice`, { params: params })
            .then(response => response.data && response.data.clubInvoices);
    }

    createPaymentIntent (invoice) {
        return this.$http.post(`/api/club/club-invoice/invoice/${invoice}/init`)
            .then(response => response.data);
    }

    updatePayment (invoice, payment) {
        return this.$http.put(`/api/club/club-invoice/invoice/${invoice}`, { payment: payment });
    }

    updatePaymentIntent (invoice, payment) {
        return this.$http.put(`/api/club/club-invoice/invoice/${invoice}/payment-intent`, { payment: payment });
    }

    openPayModal (purchaseId) {
        return this.$uibModal.open({
            backdrop: 'static',
            size: 'md',
            template: `
                <div class="modal-body">
                    <club-invoices-payment purchase-id="purchaseId" on-close="$close()"/>
                </div>`,
            controller: ['$scope', function($scope) {
                $scope.purchaseId = purchaseId;
            }]
        }).result;
    }
}

ClubsClubInvoicesService.$inject = ['$http', '$uibModal'];

angular.module('SportWrench').service('clubsClubInvoicesService', ClubsClubInvoicesService);
