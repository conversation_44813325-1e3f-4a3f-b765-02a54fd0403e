angular.module('SportWrench').directive('genderDropdown', function () {
    return {
        restrict: 'E',
        scope: {
            gender: '='
        },
        templateUrl: 'club/dropdowns/gender-dropdown.html',
        replace: true,
        link: function (scope) {
            scope.$watch('gender', function () {
                scope.genderTitle = 
                    (!scope.gender || scope.gender === 'both')
                                ?'Gender'
                                :(scope.gender === 'female')
                                    ?'Female':'Male'
            });

            scope.setGender = function (g) {
                scope.gender = g;                
            }
        }
    }
})
