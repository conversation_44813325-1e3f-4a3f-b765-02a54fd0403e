angular.module('SportWrench').component('eventsDropdown', {
    templateUrl: 'club/dropdowns/events-dropdown/events-dropdown.html',
    bindings: {
        events  : '=',
        label   : '@',
        onUpdate: '&',
    },
    controller: [Component],
});

function Component() {
    this.isSelectedAllEvents      = false;
    this.countOfSelectedEvents    = 0;

    this.toggleSelectEvent = (event) => {
        if (event.selected) {
            this.countOfSelectedEvents += 1;
        } else {
            if (this.countOfSelectedEvents > 0 ) {
                this.countOfSelectedEvents -= 1;
            }
        }

        this.isSelectedAllEvents = this.countOfSelectedEvents === this.events.length;
        this.onChange();
    };

    this.onDropdownOpen = () => {
        this.countOfSelectedEvents = _.filter(this.events, { selected: true }).length;

        this.isSelectedAllEvents = this.countOfSelectedEvents === this.events.length;
    };

    this.toggleSelectAll = () => {
        _.forEach(this.events, event => {
            if (event.selected !== this.isSelectedAllEvents) {
                event.selected = this.isSelectedAllEvents;
            }
        });

        this.countOfSelectedEvents = this.isSelectedAllEvents ? this.events.length : 0;
        this.onChange();
    };

    this.onChange = () => {
        if (this.onUpdate) {
            this.onUpdate();
        }
    };
}
