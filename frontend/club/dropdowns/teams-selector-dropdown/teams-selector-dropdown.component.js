angular.module('SportWrench').component('teamsSelectorDropdown', {
    templateUrl: 'club/dropdowns/teams-selector-dropdown/teams-selector-dropdown.html',
    bindings: {
        teams   : '=',
        label   : '@',
        onUpdate: '&',
    },
    controller: [Component],
});

function Component() {
    this.selectedTeamId = this.teams[0] ? this.teams[0].id : null;
    this.selectedTeam = this.teams[0] || null;

    this.toggleSelectTeam = (team) => {
        this.selectedTeamId = team.id;
        this.selectedTeam = team;
        this.onChange();
    };

    this.onChange = () => {
        if (this.onUpdate) {
            this.onUpdate({team: this.selectedTeam});
        }
    };
}
