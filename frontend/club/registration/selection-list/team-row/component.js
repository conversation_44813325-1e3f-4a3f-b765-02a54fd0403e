
class Controller {
    constructor ($scope, StateService) {
        this.$scope = $scope;
        this.StateService = StateService;
    }

    $onInit () {
        this.team = angular.copy(this.data);

        this.__initSelection();

        this.$scope.$watch('$ctrl.data', (newVal, oldVal) => {
            if (_.isEqual(newVal, oldVal)) {
                return;
            }

            this.team = newVal;
        }, true);
    }

    onFilterChange () {
        this.onSelect({
            id: this.team.master_team_id,
            selection: this.selection,
            fee: this.__getFee()
        });
    }

    onDivisionChange () {
        this.onFilterChange();
    }

    showFailedRegistrationIcon () {
        return _.isBoolean(this.team.registered) && !this.team.registered && !this.selectionModeUsed;
    }

    showSuccessRegistrationIcon () {
        return _.isBoolean(this.team.registered) && this.team.registered && !this.selectionModeUsed;
    }

    __initSelection () {
        const stateSelection = this.StateService.getTeamState(this.eventId, this.data.master_team_id);

        if(!_.isEmpty(stateSelection)) {
            this.selection = stateSelection;

            this.onFilterChange();
        } else {
            this.selection = {
                selected: false,
                division: this.team.pre_selected_division_id
            }
        }
    }

    __getFee () {
        if(!this.selection.division) {
            return 0;
        }

        let division = this.team.divisions.find(item => item.id === this.selection.division);

        if(_.isEmpty(division)) {
            return 0;
        }

        return Number(division.fee);
    }
}

Controller.$inject = ['$scope', 'TeamsRegistrationStateService'];

angular.module('SportWrench').component('clubBulkRegistrationSelectionListTeamRow', {
    templateUrl: 'club/registration/selection-list/team-row/template.html',
    bindings: {
        data: '<',
        onSelect: '&',
        selectionModeUsed: '<',
        eventId: '<',
        divisionSelectionDisabled: '<'
    },
    controller: Controller
});
