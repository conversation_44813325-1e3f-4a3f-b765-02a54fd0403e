<div class="row row-space">
    <fieldset ng-disabled="$ctrl.loading">
        <club-bulk-registration-filters
            on-update="$ctrl.onFilterChanged(filters)"
            filters="$ctrl.filters"
            is-hidden="$ctrl.isRegistrationMode()"
        ></club-bulk-registration-filters>

        <div class="col-xs-12">
            <span copy-button
                  text="$ctrl.filtersLink"
                  title="'Copy filters link'"
                  ng-if="!$ctrl.loading && $ctrl.isSelectionMode()"></span>
        </div>

        <div class="col-xs-12 col-md-8 col-md-offset-2">
            <club-bulk-registration-selection-list
                list="$ctrl.list"
                on-list-update="$ctrl.onSelection(selection)"
                selection-mode-used="$ctrl.isSelectionMode()"
                back-to-specific-event="$ctrl.goToSpecificSelection(event_id)"
            ></club-bulk-registration-selection-list>

            <uib-alert type="info row-space" ng-if="!$ctrl.list.length && !$ctrl.loading">
                List Is Empty. Try to use different filters.
            </uib-alert>

            <button class="btn btn-info col-xs-12"
                    ng-disabled="!$ctrl.selectionNotEmpty"
                    ng-if="$ctrl.list.length && $ctrl.isSelectionMode()"
                    ng-click="$ctrl.goToRegistration()"
            >Select</button>

            <button class="btn btn-info col-xs-12"
                    ng-if="$ctrl.list.length && $ctrl.isRegistrationMode()"
                    ng-click="$ctrl.goToSelection()"
                    style="margin-bottom: 10px"
            >Back</button>

            <button class="btn btn-success col-xs-12"
                    ng-disabled="!$ctrl.selectionNotEmpty || $ctrl.registerCompleted"
                    ng-if="$ctrl.list.length && $ctrl.isRegistrationMode()"
                    ng-click="$ctrl.registerSelection()"
            >Register</button>
        </div>
        <div class="col-xs-12 text-center">
            <spinner active="$ctrl.loading"></spinner>
        </div>
    </fieldset>
</div>
