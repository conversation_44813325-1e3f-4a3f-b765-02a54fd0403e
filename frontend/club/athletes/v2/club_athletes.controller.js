angular.module('SportWrench').controller('Club.MasterAthletesController', MasterAthletesController);

function MasterAthletesController (
    $scope, $http, ngTableParams, $log, $timeout, $rootScope, $window, $interval, $uibModal, ATHLETE_MEMBER_TYPE,
    INTERNAL_ERROR_MSG, toastr, $stateParams, SANCTIONING_BODY, ClubWebpointService, SAFESPORT_STATUS_LABEL,
    masterClubService, loadClub, GENDER_VALUES, COUNTRY_CODES, athletesService
) {
    $scope.loadClub = loadClub;
    $scope.GENDER_VALUES = GENDER_VALUES;
    $scope.COUNTRY_CODES = COUNTRY_CODES;

    $scope.data = {
        athletes: [],
        checkboxes: { checked: false, items: {}, total: 0 },
        total: 0,
        inlineEdit: { jerseys: [], aau_jersey: [], positions: [] },
        positions: {},
        teams: [],
        age: [
            {checked: false, value: '8'},
            {checked: false, value: '9'},
            {checked: false, value: '10'},
            {checked: false, value: '11'},
            {checked: false, value: '12'},
            {checked: false, value: '13'},
            {checked: false, value: '14'},
            {checked: false, value: '15'},
            {checked: false, value: '16'},
            {checked: false, value: '17'},
            {checked: false, value: '18'},
            {checked: false, value: '19'},
            {checked: false, value: '20'}
        ],
        queue: {}
    };

    $scope.ATHLETE_MEMBER_TYPE = ATHLETE_MEMBER_TYPE;
    $scope.SANCTIONING_BODY = SANCTIONING_BODY;

    $scope.filters = {
        teams: {},
        age: {},
        limit: 100,
        offset: 0,
        seasonality: null,
        sanctionedBody: {},
    };

    $scope.utils = {
        search: '', useOffset: false,
        initialLoad: true,
        offset: 0,
        error: '',
        webpointLoading: {},
        webpointChecked: {},
    };

    var counter = 0,
        loadAthletesWithDebounce;

    let athletesPingInterval = null;
    let loadMoreInProgress = false;

    let executeDebounce = true;
    const WAIT = 800;

    loadAthletesWithDebounce = _.debounce(function ($defer, params) {
        if (!executeDebounce) {
            return false
        }

        var query_params = {};

        if(params.orderBy() && params.orderBy().length) {
            query_params.order = params.orderBy()[0].substr(1);
            query_params.revert = ((params.orderBy()[0].charAt(0) === '-')?true:false);
        }

        if(!_.isEmpty($scope.filters.teams)) {
            var _t = _.pick($scope.filters.teams, _picker);
            query_params['team[]'] = Object.keys(_t);
        } else if($scope.filters.assign !== undefined) {
            $log.debug('set assign');
            query_params.assign = $scope.filters.assign;
        }

        query_params.search = $scope.utils.search;
        var _age = _.pick($scope.filters.age, _picker);
        query_params.age = Object.keys(_age);
        query_params.gender = ($scope.filters.gender !== 'both')?$scope.filters.gender:undefined;
        query_params.seasonality = $scope.filters.seasonality;
        const sanctionedBody = _.pick($scope.filters.sanctionedBody, _picker);
        query_params.sanctionedBody = Object.keys(sanctionedBody);

        if($scope.filters.limit) query_params.limit = $scope.filters.limit;
        if($scope.utils.useOffset) query_params.offset = $scope.filters.offset;

        _loadAthletes(query_params, function (err, data) {
            // params.total(data.count);
            if(err) {
                $scope.utils.error = err;
                $defer.resolve($scope.data.athletes);
                return;
            }
            if($scope.utils.useOffset) {
                $scope.data.athletes = $scope.data.athletes.concat(data.athletes);
                $log.debug('Load More Call #', ++counter, data.athletes.length);
                $log.debug('LENGTH:', $scope.data.athletes.length);
                //$scope.data.total += $scope.filters.limit;
                $scope.utils.useOffset = false;
                $defer.resolve($scope.data.athletes);
            } else {
                $scope.data.total = data.count;
                $scope.utils.offset = 0;
                $defer.resolve($scope.data.athletes = data.athletes);
            }

            if($scope.data.checkboxes.checked) {
                _pickLoaded();
            }

            if($scope.utils.initialLoad) $scope.utils.initialLoad = false;
        });

        $log.debug('Positions', $scope.data.positions);

        if(_.isEmpty($scope.data.positions))
            _loadPositions();
    }, WAIT);

    $scope.athletesTableParams = new ngTableParams({
        page: 1,
        count: 1,
        filter: $scope.filters,
        sorting: {
            'last': 'desc'
        }
    }, {
        total: 0,
        counts: [],
        filterDelay: 0,
        getData: loadAthletesWithDebounce,
    });

    $scope.toggleAll = function () {
        toggleAll();
    };

    $scope.reloadAthletes = function () {
        reloadAthletes();
    };

    $scope.toggleAthlete = function (id) {
        if($scope.data.checkboxes.items[id]) {
            $scope.data.checkboxes.total++;
        } else {
            $scope.data.checkboxes.total--;
        }

        $scope.data.checkboxes.checked = ($scope.data.checkboxes.total === $scope.data.total);
    };

    function toggleAll () {
        var v           = $scope.data.checkboxes.checked,
            athletes    = $scope.data.athletes;

        for(var i = 0, l = athletes.length, id; i < l; ++i) {
            id = athletes[i].master_athlete_id;
            if(!!id) {
                $scope.data.checkboxes.items[id] = v;
            }
        }

        $scope.data.checkboxes.total = v?$scope.data.total:0;
    }

    function _pickLoaded () {
        for(var i = 0, l = $scope.data.athletes.length; i < l; ++i) {
            var _a = $scope.data.athletes[i];
            if(!$scope.data.checkboxes.items[_a.master_athlete_id])
                $scope.data.checkboxes.items[_a.master_athlete_id] = true;
        }
    }

    $scope.loadMore = function () {
        if (loadMoreInProgress) {
            return;
        }

        $log.debug('Load More Call .... ');
        if( $scope.utils.initialLoad ||
            $scope.athletesTableParams.settings().$loading ||
            $scope.data.athletes.length >= $scope.data.total ||
            $scope.data.total < ( $scope.utils.offset - $scope.filters.limit) )
            return;

        $scope.utils.useOffset = true;
        $scope.utils.offset += $scope.filters.limit;
        $scope.filters.offset = $scope.utils.offset;

        loadMoreInProgress = true;
    }

    $scope.showTeamsMenu = function () {
        if($scope.data.checkboxes.checked) return true;
        var keys = Object.keys($scope.data.checkboxes.items);
        for(var i = 0; i < keys.length; ++i) {
            if($scope.data.checkboxes.items[keys[i]]) return true;
        }
        return false;
    }

    function _loadAthletes (params, cb) {
        $scope.utils.error = '';

        athletesPingInterval = $interval(function __getAthletes() {
            $http.get('/api/v2/club/master_athlete', {
                params              : params,
                paramSerializer     : '$httpParamSerializerJQLike'
            }).success(function (data) {
                if(data.queue_data && data.queue_data.requested) {
                    $scope.data.queue.requested_time = data.queue_data.requested;
                } else {
                    $interval.cancel(athletesPingInterval);
                    $scope.data.queue.requested_time = undefined;
                    return cb(null, data);
                }
            }).error(function (data) {
                $interval.cancel(athletesPingInterval);
                return cb((data && data.error) || INTERNAL_ERROR_MSG);
            }).finally(() => {
                const needToUncheckAthletes = checkParams(params);
                loadMoreInProgress = false;

                if (needToUncheckAthletes) {
                    $scope.data.checkboxes.total = 0;
                    _unselectAthletes();
                }
            });

            return __getAthletes;
        }(), 3*1000)
    }

    function _loadPositions () {
        $http.get('/api/v2/club/athletes/positions').success(function (data) {
            $scope.data.positions = data.positions;
        })
    }

    function _getPickedAthletes () {
        var id = [], keys = Object.keys($scope.data.checkboxes.items);
        for(var i = 0, l = keys.length; i < l; ++i){
            if($scope.data.checkboxes.items[keys[i]])
                id.push(keys[i]);
        }
        return id;
    }

    $scope.updateJersey = function (a) {
        var _temp = $scope.data.inlineEdit.jerseys[a.master_athlete_id];
        $log.debug('_temp', _temp);

        if(!_.isNull(a.jersey) && a.jersey <= 0) {
            toastr.warning('Uniform must be a positive number');
            return;
        }
        if( _temp === a.jersey ) {
            $scope.data.inlineEdit.jerseys[a.master_athlete_id] = undefined;
            return;
        }

        __updateAthlete(a.master_athlete_id, { jersey: a.jersey})
        .success(function () {
            __onUpdateSuccess();
            $scope.data.inlineEdit.jerseys[a.master_athlete_id] = undefined;
        }).error(__onUpdateFailure);
    }

    $scope.updateAAUJersey = function (a) {
        const _temp = $scope.data.inlineEdit.aau_jersey[a.master_athlete_id];
        $log.debug('_temp', _temp);

        if(!_.isNull(a.aau_jersey) && a.aau_jersey <= 0) {
            toastr.warning('AAU Uniform must be a positive number');
            return;
        }
        if( _temp === a.aau_jersey ) {
            $scope.data.inlineEdit.aau_jersey[a.master_athlete_id] = undefined;
            return;
        }

        __updateAthlete(a.master_athlete_id, { aau_jersey: a.aau_jersey })
        .success(function () {
            __onUpdateSuccess();
            $scope.data.inlineEdit.aau_jersey[a.master_athlete_id] = undefined;
        }).error(__onUpdateFailure);
    }

    $scope.updatePosition = function (a) {
        $log.debug('updating position', a.sport_position_id);
        __updateAthlete(a.master_athlete_id, { sport_position_id: parseInt(a.sport_position_id, 10) })
        .success(__onUpdateSuccess)
        .error(__onUpdateFailure)
    }

    $scope.updateGradYear = function (a) {
        __updateAthlete(a.master_athlete_id, { gradyear: parseInt(a.gradyear, 10) })
        .success(__onUpdateSuccess)
        .error(__onUpdateFailure)
    }

    function __updateAthlete (id, data) {
        return $http.put('/api/v2/club/master_athlete/' + id + '/update?sync_all=true', { athlete: data })
    }

    function __onUpdateSuccess () {
        toastr.success('Successfully updated');
        $scope.$broadcast('inline-edit.close');
    }

    function __onUpdateFailure () {
        toastr.error('Not updated')
    }

    $scope.jerseyFocus = function (a) {
        $scope.data.inlineEdit.jerseys[a.master_athlete_id] = a.jersey;
    }

    $scope.aauJerseyFocus = function (a) {
        $scope.data.inlineEdit.aau_jersey[a.master_athlete_id] = a.aau_jersey;
    }

    $scope.positionFocus = function (a) {
        $scope.data.inlineEdit.positions[a.master_athlete_id] = a.sport_position_id;
    }

    $scope.countSelected = function () {
        var _count = 0;
        angular.forEach($scope.data.checkboxes.items, function (value) {
            if(value) _count++;
        })
        return _count;
    }

    $scope.search_keypress = function (e) {
        if($scope.athletesTableParams.settings().$loading) return;

        if($scope.utils.search_timeout) {
            $timeout.cancel( $scope.utils.search_timeout );
        }

        if(e.keyCode === 13) {
            $scope.athletesTableParams.reload();
            return;
        }

        $scope.utils.search_timeout = $timeout(function () {
            $scope.athletesTableParams.reload();
        }, 500);
    }

    $scope.findAssignedEvents = function (team_id) {
        var athletes = _getPickedAthletes();
        if(!athletes.length) return;

        _moveAthletes(team_id, athletes)
        .then(function success (resp) {
            $scope.data.move_response = resp.data && resp.data.msg;
            $scope.data.checkboxes.checked = false;
            reloadAthletes();
        }, function error (data) {
            // check dismiss and close at docs
            if(!data) {
               _unselectAthletes();
            } else {
                // show error to user
                console.error( data && data.validation);
            }
        })
    }

    function _moveAthletes (team_id, athletes) {
        return $http.post('/api/v2/club/master_athlete/move_to_team', {
            athletes        : athletes,
            master_team_id  : team_id
        })
    }

    function _unselectAthletes () {
        $scope.data.checkboxes.items = {};
        $scope.data.checkboxes.checked = false;
    }

    $scope.closeAlert = function () {
        $scope.data.move_response = null;
    }

    $scope.closeRemoveAlert = function () {
        $scope.data.remove_response = null;
    }

    $scope.sportEngineImport = function () {
        $uibModal.open({
            template: `<sport-engine-import close-modal="closeModal()"></sport-engine-import>`,
            controller: ['$scope', '$uibModalInstance', function (scope, $uibModalInstance) {
                scope.closeModal = function () {
                    $uibModalInstance.close();
                }
            }]
        })
    }

    $scope.aauImport = function () {
        $uibModal.open({
            template: `<aau-import close-modal="closeModal()"></aau-import>`,
            controller: ['$scope', '$uibModalInstance', function (scope, $uibModalInstance) {
                scope.closeModal = function () {
                    $uibModalInstance.close();
                }
            }]
        })
    }

    $scope.webpointImport = function () {
        $uibModal.open({
            templateUrl: 'club/athletes/import-athlete.html',
            controller: function ($scope, $uibModalInstance, masterClubService) {
                $scope.athleteData = {
                    username        : '',
                    password        : '',
                    agree           : null,
                    option          : 'insert'
                };

                $scope.queueData = {};

                $scope.blockedEvents = [];

                masterClubService.getWebpointData().then(function(resp) {
                    var data = resp.data;
                    if(data.webpoint_data && data.webpoint_data.username)
                        $scope.athleteData.username = data.webpoint_data.username;
                    if(data.webpoint_data && data.webpoint_data.agree)
                        $scope.athleteData.agree = data.webpoint_data.agree;
                    if(data.queue_data && data.queue_data.requested)
                        $scope.queueData.requested = data.queue_data.requested;
                    $scope.blockedEvents = data.blocked_events;
                });

                $scope.submit = function() {
                    if ($scope.importAthleteForm.$valid) {
                        $scope.isImportProcessing = true;
                        this.error = '';
                        if ($scope.importAthleteForm.$valid) {
                            masterClubService.importAthleteWebpoint($scope.athleteData).success(function () {
                                toastr.success('Import Started');
                                $timeout(function () {
                                    $uibModalInstance.close();
                                }, 1500);
                            }).error(function (data) {
                                if(data) {
                                    if(data.validation)
                                        $scope.error = data.validation;
                                    else if(data.access_errors)
                                        $scope.error = data.access_errors;
                                    else
                                        $scope.error = INTERNAL_ERROR_MSG;
                                } else {
                                    $scope.error = INTERNAL_ERROR_MSG;
                                }
                            }).finally(() => $scope.isImportProcessing = false)
                        }
                    }
                };

                $uibModalInstance.result.then(reloadAthletes, reloadAthletes);
            }
        });
    }

    $scope.xlsxImport = function () {
        $uibModal.open({
            templateUrl: 'components/xlsx/roster_import.html',
            controller: function ($scope, $uibModalInstance, fileUploadService) {
                $scope.submitted = false;

                $scope.submit = function () {
                    $scope.submitted = true;
                    var file = _.first(angular.element("[name='roster-list']")[0].files);

                    if(!file) {
                        toastr.warning('Choose a file to import');
                        $scope.submitted = false;
                        return;
                    }

                    const isFileInvalid = fileUploadService.xmlFileUploadValidation(file);
                    if(isFileInvalid) {
                        toastr.warning(isFileInvalid);
                        $scope.submitted = false;
                        return;
                    }

                    var fd = new FormData();
                    fd.append('roster-list', file);

                    $http.post('/api/club/roster-import', fd, {
                        withCredentials     : true,
                        headers             : { 'Content-Type': undefined },
                        transformRequest    : angular.identity
                    }).then(function () {
                        reloadAthletes();
                        $uibModalInstance.close();
                    }, function () {
                        $scope.submitted = false;
                    });
                };
            }
        });
    }

    $scope.openAthleteEditDialog = function (master_athlete_id) {
        if(!master_athlete_id) return;
        $uibModal.open({
            templateUrl: 'club/athletes/edit-athlete.html',
            controller: 'Club.EditMasterAthleteController',
            scope: $scope,
            resolve: {
                data: ['athletesService', '$q', function (athletesService, $q) {
                    let defer = $q.defer();

                    athletesService.getAthlete(master_athlete_id)
                        .then(data => defer.resolve(data))
                        .catch(err => defer.reject(err));

                    return defer.promise;
                }],
                athleteId: function () {
                    return master_athlete_id
                }
            }
        })
    }

    $scope.updateWebpointInfo = function(athlete) {
        setWebpointLoading(athlete.master_athlete_id);
        setWebpointChecked(athlete.master_athlete_id);

        ClubWebpointService.syncAthlete(athlete.master_athlete_id)
            .then(onUpdateWebpointInfoSuccessfully.bind(this, athlete))
            .catch(onUpdateWebpointInfoError.bind(this, athlete.master_athlete_id))
            .finally(onUpdateWebpointInfoFinally.bind(this, athlete.master_athlete_id))
    }

    $scope.showWebpointCheckButton = function(athlete) {
        return !$scope.utils.webpointChecked[athlete.master_athlete_id] && athlete.show_webpoint
            && false; // SW-2011
    }

    function _replaceUpdatedAthlete (athlete) {
        for(var i = 0, l = $scope.data.athletes.length; i < l; ++i) {
            if($scope.data.athletes[i].master_athlete_id === athlete.master_athlete_id) {
                $scope.data.athletes[i] = _.clone(athlete);
                return;
            }
        }
    }

    function reloadAthletes () {
        $scope.athletesTableParams.reload();
    }

    function _picker (value) {
        return value === true
    }

    function onUpdateWebpointInfoSuccessfully(athlete, { safesport_status }) {
        const safesportStatus = getFormattedSafeSportStatus(safesport_status);

        updateAthleteSafeSport(athlete, safesportStatus);

        toastr.success('Checked');
    }

    function onUpdateWebpointInfoError(athleteID) {
        setWebpointUnchecked(athleteID);
    }

    function onUpdateWebpointInfoFinally(athleteID) {
        setWebpointLoadingIsComplete(athleteID);
    }

    function getFormattedSafeSportStatus(status) {
        return status ? SAFESPORT_STATUS_LABEL.VALID : SAFESPORT_STATUS_LABEL.NOT_VALID;
    }

    function updateAthleteSafeSport(athlete, safesportStatus) {
        athlete.safesport_status = safesportStatus;
    }

    function setWebpointLoading(athleteID) {
        $scope.utils.webpointLoading[athleteID] = true;
    }

    function setWebpointLoadingIsComplete(athleteID) {
        $scope.utils.webpointLoading[athleteID] = false;
    }

    function setWebpointChecked(athleteID) {
        $scope.utils.webpointChecked[athleteID] = true;
    }

    function setWebpointUnchecked(athleteID) {
        $scope.utils.webpointChecked[athleteID] = false;
    }

    const checkParams = (params) => {
        if (params.offset) {
            return false;
        }

        const rules = [
            params['team[]'],
            params.age,
            params.gender,
            params.search,
        ];

        return rules.some(rule => rule);
    };

    $scope.$on('club.athletes.reload-list', function () {
        reloadAthletes();
    });

    $scope.$on('club.athletes.replace-athlete', function (e, athlete) {
        _replaceUpdatedAthlete(athlete);
    });

    if($stateParams.master_athlete_id) {
        $scope.openAthleteEditDialog($stateParams.master_athlete_id)
    }

    $scope.$on('$destroy', function() {
        // cancel debounce
        executeDebounce = false;

        $interval.cancel(athletesPingInterval);
    });

    $scope.showXLSXImportButton = function () {
        return $scope.club.allow_xlsx_import;
    };

    $scope.clubHasUsavSanctioning = masterClubService.clubHasUsavSanctioning($scope.club);
    $scope.clubHasUsavAndAauSanctioning = masterClubService.clubHasUsavAndAauSanctioning($scope.club);
    $scope.clubHasAauSanctioning = masterClubService.clubHasAauSanctioning($scope.club);
    $scope.clubHas9ManSanctioning = masterClubService.clubHas9ManSanctioning($scope.club);

    $scope.showImportFromSportEngineButton = function() {
        const rules = [
            [$scope.COUNTRY_CODES.UNITED_STATES, $scope.COUNTRY_CODES.CANADA].includes($scope.club.country),
            $scope.clubHasUsavSanctioning
        ];

        return rules.every(rule => rule);
    }

    $scope.showImportFromAauButton = function() {
        const rules = [
            $scope.club.aau_club_code,
            $scope.club.aau_primary_membership_id,
            $scope.club.aau_primary_zip,
            $scope.clubHasAauSanctioning,
        ];

        return rules.every(rule => rule);
    }

    $scope.showImportFromWebpointButton = function() {
        const rules = [
            !$scope.data.queue.requested_time,
            ($scope.club.country === 'US'),
            $scope.clubHasUsavSanctioning,
            false //SW-2011
        ];

        return rules.every(rule => rule);
    }

    $scope.athleteRowClass = function(athleteID) {
        return {
            'highlight-athlete': $scope.utils.webpointLoading[athleteID],
        }
    }

    $scope.removeFromClub = function(isConfirmed) {
        if (isConfirmed) {
            const selectedAthletes = _.map(_getPickedAthletes(), function (v) {
                return parseInt(v, 10);
            });

            if (!selectedAthletes.length) return;

            const assignedToTeamsAthletes = $scope.data.athletes.filter(a =>
                selectedAthletes.includes(a.master_athlete_id) && a.master_team_id);

            if (assignedToTeamsAthletes.length > 0) {
                toastr.error('Members cannot be removed from the club because they are assigned to teams. Please unassign the members from their teams first.');
                return;
            }

            athletesService.removeAthletesFromClub(selectedAthletes)
                .then(function success(response) {
                    $scope.data.remove_response = response.data && response.data.message;
                    $scope.data.checkboxes.checked = false;
                    reloadAthletes();
                }, function error(data) {
                    if(!data) {
                        _unselectAthletes();
                    }
                });
        }
    };
}
