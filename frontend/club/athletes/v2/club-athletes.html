<form class="form-inline row-space top-menu">
    <div class="form-group">
        <div class="search-box">
            <input
                type="search"
                name="all_teams_search"
                class="form-control search_teams"
                placeholder="Search ..."
                ng-model="utils.search"
                ng-keydown="search_keypress($event)"
                ng-readonly="athletesTableParams.settings().$loading">
            <span class="glyphicon glyphicon-search"></span>
        </div>
    </div>
    <gender-dropdown gender="filters.gender"></gender-dropdown>
    <teams-dropdown
        title="Team"
        btn-class="btn btn-default"
        checking="true"
        assigned="filters.assign"
        selection="filters.teams"
    ></teams-dropdown>
    <div class="btn-group" uib-dropdown>
        <button type="button" class="btn btn-default" uib-dropdown-toggle>
            Age <span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu" ng-click="$event.stopPropagation()">
            <li class="row rowm0 pointer" ng-click="filters.age = {}">
                <div class="col-sm-12">All</div>
            </li>
            <li class="row rowm0" ng-repeat="item in data.age">
                <div class="col-sm-12">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" ng-model="filters.age[item.value]">
                            {{item.value}}
                        </label>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <seasonality-dropdown ng-if="clubHasUsavSanctioning" seasonality="filters.seasonality"></seasonality-dropdown>
    <sanctioned-body-checkboxes
        ng-if="clubHasUsavAndAauSanctioning"
        sanctioned-body="filters.sanctionedBody"
    ></sanctioned-body-checkboxes>
    <a href="" class="btn btn-primary" ng-click="webpointImport()" ng-if="showImportFromWebpointButton()">
        Import from Webpoint
    </a>
    <a href="" class="btn btn-primary" ng-click="sportEngineImport()" ng-if="showImportFromSportEngineButton()">
        Import from SportEngine
    </a>
    <a href="" class="btn btn-primary" ng-click="aauImport()" ng-if="showImportFromAauButton()">
        Import from AAU
    </a>
    <a href="" class="btn btn-primary" ng-click="xlsxImport()" ng-if="showXLSXImportButton()">XLSX Import</a>
    <div class="form-group" ng-show="showTeamsMenu()"><!-- Do not change to ng-if !!! -->
        <label>With Selected:</label>
        <button class="btn btn-danger" ng-click="findAssignedEvents()">Remove from teams</button>
    </div>
    <teams-dropdown
        title="Add to Team"
        btn-class="btn btn-success"
        on-select="findAssignedEvents(id)"
        ng-show="showTeamsMenu()"
    ></teams-dropdown><!-- Do not change to ng-if !!! -->
    <div class="form-group">
        <club-personal-info-merge-button
            club="club"
            members-count="data.total"
            type="ATHLETE_MEMBER_TYPE"
            on-update="athletesTableParams.reload()"
        ></club-personal-info-merge-button>
    </div>
    <div class="form-group" ng-show="showTeamsMenu()">
        <button class="btn btn-danger"
                sw-confirm="Are you sure you want to remove the selected member(s) from the club?"
                sw-confirm-do="removeFromClub"
                sw-confirm-hide-no>Remove from club
        </button>
    </div>
</form>

<div class="row minus-margin">
    <div class="col-sm-2">
        <small>
            <a href="" class="q-ty" ng-if="data.total >= 0">{{data.athletes.length}} / {{data.total}}</a>
        </small>
    </div>
</div>

<div class="row" ng-if="data.move_response">
    <div class="col-sm-12">
        <uib-alert close="closeAlert()">{{data.move_response}}</uib-alert>
    </div>
</div>

<div class="row" ng-if="data.remove_response">
    <div class="col-sm-12">
        <uib-alert close="closeRemoveAlert()">{{data.remove_response}}</uib-alert>
    </div>
</div>

<div class="alert alert-warning" ng-if="data.queue.requested_time">
    <div class="row">
        <div class="col-xs-12 text-center">Import is still in progress, please wait...</div>
    </div>
</div>

<p ng-if="!data.queue.requested_time">
    <i class="fa fa-info-circle"></i> NOTES: inline edit changes only initial data. If you want to change data of athlete assigned to events open edit modal
</p>

<uib-alert type="danger" ng-if="utils.error">
    <div class="row">
        <div class="col-sm-12 text-center" ng-bind="utils.error"></div>
    </div>
</uib-alert>

<div loading-container="athletesTableParams.settings().$loading" ng-if="!data.queue.requested_time" editable-grid one-active="true">
        <table
            class="table table-condensed sw-adaptive-grid athletes-table highlihgt-athlete"
            ng-table="athletesTableParams"
            infinite-scroll="loadMore()"
            infinite-scroll-distance="2"
            infinite-scroll-disabled="athletesTableParams.settings().$loading"
            sticky-header
        >
        <tr
            ng-class="athleteRowClass(a.master_athlete_id)"
            ng-repeat="a in $data"
        > <!--   track by a.master_athlete_id -->
            <td width="55px" header="'club/athletes/headers/checkbox.html'">
                <input
                    type="checkbox"
                    ng-model="data.checkboxes.items[a.master_athlete_id]"
                    ng-change="toggleAthlete(a.master_athlete_id)"/>
            </td>
            <td>
                <span
                    class="glyphicon glyphicon-envelope big-icon text-dark athlete-contacts"
                    uib-popover-html="a.contacts"
                    popover-trigger="outsideClick"
                    popover-append-to-body="false"
                ></span>
            </td>
            <td data-title="'G'" sortable="'gender'" header-class="text-left" ng-click="openAthleteEditDialog(a.master_athlete_id)">
                <genders
                    m="a.gender === GENDER_VALUES.MALE"
                    f="a.gender === GENDER_VALUES.FEMALE"
                    nb="a.gender === GENDER_VALUES.NON_BINARY"
                ></genders>
            </td>
            <td data-title="'Team Name'" sortable="'team'" class="hiding-column-min" header-class="'hiding-column-min'" ng-click="openAthleteEditDialog(a.master_athlete_id)">
                {{a.team_name || '-'}}
            </td>
            <td data-title="'Uni'" sortable="'uni'" class="hiding-column-min" header-class="'hiding-column-min'">
                <inline-edit
                    edit-value="{{a.jersey === null ? 'N/A' : a.jersey}}">
                    <input
                        type="number"
                        class="form-control form-control-input-small number-without-arrows"
                        ng-model="a.jersey"
                        ng-blur="updateJersey(a)"
                        ng-focus="jerseyFocus(a)"
                        ng-pattern="/^\d+$/">
                </inline-edit>
            </td>
            <td ng-show="clubHasAauSanctioning" data-title="'AAU Uni'" sortable="'aau_uni'"
                class="hiding-column-min" header-class="'hiding-column-min'">
                <inline-edit
                    edit-value="{{a.aau_jersey === null ? 'N/A' : a.aau_jersey}}">
                    <input
                        type="number"
                        class="form-control form-control-input-small number-without-arrows"
                        ng-model="a.aau_jersey"
                        ng-blur="updateAAUJersey(a)"
                        ng-focus="aauJerseyFocus(a)"
                        ng-pattern="/^\d+$/">
                </inline-edit>
            </td>
            <td data-title="'Pos'" sortable="'pos'" class="hiding-column-min" header-class="'hiding-column-min'">
                <inline-edit
                    edit-value="{{data.positions[a.sport_position_id] || 'N/A'}}">
                    <select
                        ng-model="a.sport_position_id"
                        ng-options="key as value for (key , value) in data.positions"
                        class="form-control form-control-select-small"
                        ng-change="updatePosition(a)"
                    ></select>
                </inline-edit>
            </td>
            <td data-title="'Age'" sortable="'age'" class="hiding-column-min" header-class="'hiding-column-min'" ng-click="openAthleteEditDialog(a.master_athlete_id)">
                {{a.age}}
            </td>
            <td data-title="'First'" sortable="'first'" ng-click="openAthleteEditDialog(a.master_athlete_id)">
                {{a.first}}
            </td>
            <td data-title="'Last'" sortable="'last'" ng-click="openAthleteEditDialog(a.master_athlete_id)">
                {{a.last}}
            </td>
            <td data-title="'HS Grad Year'" sortable="'year'" class="hiding-column-min" header-class="'hiding-column-min'">
                <inline-edit edit-value="{{a.gradyear || 'N/A'}}">
                    <select
                        class="form-control form-control-select-small"
                        name="gradyear"
                        ng-model="a.gradyear"
                        ng-change="updateGradYear(a)">
                            <option value="2018">2018</option>
                            <option value="2019">2019</option>
                            <option value="2020">2020</option>
                            <option value="2021">2021</option>
                            <option value="2022">2022</option>
                            <option value="2023">2023</option>
                            <option value="2024">2024</option>
                            <option value="2025">2025</option>
                            <option value="2026">2026</option>
                            <option value="2027">2027</option>
                            <option value="2028">2028</option>
                            <option value="2029">2029</option>
                            <option value="2030">2030</option>
                            <option value="2031">2031</option>
                            <option value="2032">2032</option>
                            <option value="2033">2033</option>
                            <option value="2034">2034</option>
                            <option value="2035">2035</option>
                    </select>
                </inline-edit>
            </td>
            <td ng-show="clubHas9ManSanctioning" data-title="'Role'" sortable="'role'">
                <span ng-bind="a.role"></span>
            </td>
            <td ng-show="clubHasUsavSanctioning" data-title="'USAV Seasonality'" sortable="'seasonality'" class="hiding-column-min">
                <span ng-bind="a.seasonality"></span>
            </td>
            <td data-title="'SafeSport'" class="hiding-column-min">
                <span ng-if="a.show_webpoint">{{a.safesport_status}}</span>
            </td>
            <td data-title="'Sanctioned body'" class="hiding-column-min">
                <span ng-if="a.usav_number">USAV</span>
                <span ng-if="a.aau_membership_id" class="list-text-right">AAU</span>
            </td>
            <td data-title="'Webpoint'" class="hiding-column-min" ng-show="false"> <!-- SW-2011 -->
                <a ng-if="showWebpointCheckButton(a)" ng-click="updateWebpointInfo(a)" class="pointer">Check</a>
            </td>
        </tr>
        <tr
            ng-if="!athletesTableParams.settings().$loading && !$data.length" no-data-row cs="10"
            text="No available athletes found for current season."
        ></tr>
    </table>
</div>

<script type="text/ng-template" id="club/athletes/headers/checkbox.html">
    <input type="checkbox" ng-model="data.checkboxes.checked" ng-change="toggleAll()" ng-disabled="!$data.length" class="pull-left"/>
    <span class="badge badge-dark"><small>{{data.checkboxes.checked?data.total:data.checkboxes.total}}</small></span>
</script>
