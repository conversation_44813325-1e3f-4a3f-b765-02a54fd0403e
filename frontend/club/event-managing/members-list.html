<div class="row row-space">
    <div class="col-lg-5">
        <form class="form-inline">
            <div class="form-group" ng-if="teams.length">
                <label>Team:</label>
                <select
                    class="form-control"
                    ng-model="filters.team"
                    ng-options="t as t.name for t in teams"
                    ng-change="loadMembers()"
                >
                </select>
            </div>
            <button class="btn btn-success" ng-click="printRoster()" ng-disabled="!loading.members_loaded">Print Team Rosters</button>
        </form>
    </div>
    <div ng-if="team.locked" class="col-sm-2 spacer-mobile-sm-t panel-no-margins">
        <uib-alert type="danger alert-sm center-form-elem-v">
            <i class="fa fa-exclamation-triangle"></i> Team Roster Locked
        </uib-alert>
    </div>
    <div ng-if="loading.members_loaded && !empty(utils.checkinValidation)" ng-class="{ 'spacer-mobile-sm-t panel-no-margins': true, 'col-lg-5': !utils.accordOpen, 'col-lg-12 row-space': utils.accordOpen }">
        <uib-accordion>
            <uib-accordion-group class="{{valPanelClass()}}" is-open="utils.accordOpen">
                <uib-accordion-heading>
                    <i class="fa fa-exclamation-triangle"></i> Team Roster Validation Errors
                </uib-accordion-heading>
                <checkin-team-errors errors="utils.checkinValidation"></checkin-team-errors>
            </uib-accordion-group>
        </uib-accordion>
    </div>
    <div ng-if="empty(utils.checkinValidation)" class="col-sm-5 spacer-mobile-sm-t">
        <uib-alert type="success alert-sm center-form-elem-v">
            <i class="fa fa-check green"></i> Team Validation Passed
        </uib-alert>
    </div>
</div>

<uib-alert type="warning" ng-if="!teams.length">No teams have entered this event. Please assign teams to the event to see members list.</uib-alert>

<table
    class="table table-condensed table-hover members-list"
    ng-if="teams.length"
    editable-grid
    one-active="true"
    >
    <thead>
        <tr>
            <th>
                <input type="checkbox" ng-model="check.all" ng-change="check_all()">
            </th>
            <th></th>
            <th>
                <a ng-click="order('gender')" href="">G</a>
                <reverse-arrow show="{{order.column == 'gender'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('role')" href="">Role</a>
                <reverse-arrow show="{{order.column == 'role'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th ng-if="clubHas9ManSanctioning">
                <a ng-click="order('athlete_role')" href="">Athlete Role</a>
                <reverse-arrow show="{{order.column == 'athlete_role'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('first')" href="">First</a>
                <reverse-arrow show="{{order.column == 'first'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('last')" href="">Last</a>
                <reverse-arrow show="{{order.column == 'last'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th ng-if="!eventHasAAUSanctioning">
                <a ng-click="order('jersey')" href="">Uni</a>
                <reverse-arrow show="{{order.column == 'jersey'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th ng-if="eventHasAAUSanctioning">
                <a ng-click="order('aau_jersey')" href="">AAU Uni</a>
                <reverse-arrow show="{{order.column == 'aau_jersey'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('sport_position_id')" href="">Pos</a>
                <reverse-arrow show="{{order.column == 'sport_position_id'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('cert')" href="">Cert</a>
                <reverse-arrow show="{{order.column == 'cert'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('age')" href="">Age</a>
                <reverse-arrow show="{{order.column == 'age'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>
                <a ng-click="order('gradyear')" href="">HS Grad Year</a>
                <reverse-arrow show="{{order.column == 'gradyear'}}" reverse="order.reverse"></reverse-arrow>
            </th>
            <th>SafeSport</th>
            <th>BKG</th>
        </tr>
    </thead>
    <tbody class="pointer">
        <tr ng-repeat="m in members | orderBy:[role_order_value(), order_value()]"
            ng-class="{'text-grey striked-out removed-row': m.deleted_by_user}"
            member-removed="{{memberRemovedText(m)}}"
            ng-click="openMemberModal(m)"
            >
            <td ng-click="$event.stopPropagation()">
                <input type="checkbox" ng-model="m.checked" ng-change="athleteCheckedChanged(m)" ng-if="!m.deleted_by_user">
            </td>
            <td ng-click="$event.stopPropagation()">
                <span
                    class="big-icon glyphicon glyphicon-envelope text-dark"
                    uib-popover-html="m.contacts_html"
                    popover-trigger="outsideClick"
                    popover-append-to-body="false">
                </span>
            </td>
            <td>
                <genders
                    m="m.gender === GENDER_VALUES.MALE"
                    f="m.gender === GENDER_VALUES.FEMALE"
                    nb="m.gender === GENDER_VALUES.NON_BINARY"
                ></genders>
            </td>
            <td ng-bind="m.role"></td>
            <td ng-if="clubHas9ManSanctioning">{{m.athlete_role || '-'}}</td>
            <td><a href="" ng-bind="m.first"></a></td>
            <td><a href="" ng-bind="m.last"></a></td>

            <!-- Uniform column -->
            <td
                ng-if="m.is_athlete && !eventHasAAUSanctioning"
                ng-class="{ 'bg-danger': jerseyDups[m.id] }"
                title="{{jerseyDupWarn(m.id)}}"
                ng-click="!team.locked && $event.stopPropagation() && roleCellClick(m)">
                <jersey-editor
                    member="m"
                    event-id="{{eventId}}"
                    on-saved="findJerseyDuplicates()"
                    on-addon-click="openMemberModal(m)"
                    block="team.locked"
                    ng-if="!team.locked">
                </jersey-editor>
                <span ng-if="team.locked" ng-bind="m.jersey || m.default_jersey"></span>
            </td>
            <td ng-click="!team.locked && $event.stopPropagation() && positionCellClick(m)">
                <div ng-if="m.is_athlete">
                    <span ng-if="team.locked" ng-bind="m.sport_position_name || m.default_position_name"></span>
                    <position-editor
                        member="m"
                        event-id="{{eventId}}"
                        on-addon-click="openMemberModal(m)"
                        block="team.locked"
                        ng-if="!team.locked"
                    >
                    </position-editor>
                </div>
                <span class="label label-success" ng-if="m.primary && !m.is_athlete">Primary</span>
            </td>
            <!-- Uniform column end -->

            <!-- AAU Uniform column -->
            <td
                ng-if="m.is_athlete && eventHasAAUSanctioning"
                ng-class="{ 'bg-danger': aauJerseyDups[m.id] }"
                title="{{aauJerseyDupWarn(m.id)}}"
                ng-click="!team.locked && $event.stopPropagation() && roleCellClick(m)">
                <aau-jersey-editor
                    member="m"
                    event-id="{{eventId}}"
                    on-saved="findAAUJerseyDuplicates()"
                    on-addon-click="openMemberModal(m)"
                    block="team.locked"
                    ng-if="!team.locked">
                </aau-jersey-editor>
                <span ng-if="team.locked" ng-bind="m.aau_jersey || m.default_aau_jersey"></span>
            </td>
            <td ng-if="!m.is_athlete && eventHasAAUSanctioning" ng-click="!team.locked && $event.stopPropagation() && roleCellClick(m)">
                <span></span>
            </td>
            <!-- AAU Uniform column end -->

            <td ng-if="!m.is_athlete && !eventHasAAUSanctioning" ng-click="!team.locked && $event.stopPropagation() && roleCellClick(m)">
                <role-editor
                    member="m"
                    event-id="{{eventId}}"
                    on-addon-click="openMemberModal(m)"
                    block="team.locked"
                    ng-if="!team.locked"
                ></role-editor>
                <span ng-if="team.locked" ng-bind="m.role_name || m.default_role_name"></span>
            </td>

            <td ng-bind="m.cert"></td>
            <td ng-bind="m.age"></td>
            <td>{{(m.role === 'athlete')?m.gradyear:'-'}}</td>
            <td ng-bind="m.safesport_statusid"></td>
            <td ng-bind="m.bkg"></td>
        </tr>
        <tr ng-if="showTravelCoordinator()">
            <td></td>
            <td ng-click="$event.stopPropagation()">
                <span
                    class="big-icon glyphicon glyphicon-envelope text-dark"
                    uib-popover-html="travelCoordinatorContacts()"
                    popover-trigger="outsideClick"
                    popover-append-to-body="false">
                </span>
            </td>
            <td></td>
            <td>Travel Coordinator</td>
            <td>{{filters.team.ths_trav_coord_name.split(' ')[0] || ''}}</td>
            <td>{{filters.team.ths_trav_coord_name.split(' ')[1] || ''}}</td>
            <td colspan="7"></td>
        </tr>
        <tr ng-if="!members.length && !loading.members_loaded">
            <td colspan="14" class="text-center"><i class="fa fa-spinner fa-pulse fa-2x"></i></td>
        </tr>
        <tr ng-if="loading.members_loaded && !members.length" no-data-row cs="14" text="There are no Members for this Team assigned yet.">
        </tr>
    </tbody>
</table>


