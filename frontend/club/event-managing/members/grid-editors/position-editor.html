<inline-edit on-toggle="positionEditToggle(isEdit, m)" block-edit="block">
    <edit-value>
        <span 
            ng-class="{ 'text-grey striked-out red-line': isOverwritten() }"  
            uib-tooltip="Master Position">&nbsp;{{m.default_position_name}}&nbsp;</span>
        <span ng-bind="m.sport_position_name" class="font-bold" ng-if="isOverwritten()" uib-tooltip="Event Position"></span>
    </edit-value>
    <!-- Editor's markup -->
    <div class="inline-block pull-left">
        <span 
            ng-if="isOverwritten()"
            class="text-grey striked-out red-line"  
            uib-tooltip="Master Position">&nbsp;{{m.default_position_name}}&nbsp;</span>
    </div>
    <div class="inline-block pull-left">
        <div class="btn-group sw-btn-group-sm">
            <div class="btn-group" uib-dropdown>
                <button class="btn btn-default" uib-dropdown-toggle ng-disabled="inProgress">
                    {{inlinePositionEdit.name}} <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li role="menuitem" ng-repeat="p in positions">
                        <a 
                            href="" 
                            ng-bind="p.short_name" 
                            ng-click="savePositionChange(p)">
                        </a>
                    </li>
                </ul>
            </div>
            <edit-addon type="btn btn-default addon"></edit-addon>
        </div>
    </div>
    <div class="clearfix"></div>
    <!-- Editor's markup end -->
</inline-edit>