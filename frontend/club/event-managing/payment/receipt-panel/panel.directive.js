angular.module('SportWrench').component('receiptPanel', {
	templateUrl: 'club/event-managing/payment/receipt-panel/panel.html',
	bindings: {
		eventId 		: '<',
		paymentId 		: '<',
		amount 			: '<',
		receiptType 	: '@?',
		paymentType     : '<'
	},
	controller: ['ClubPaymentsService', 'purchaseService', function (ClubPaymentsService, purchaseService) {
		this.open = function () {
			if (this.receiptType === 'event') {
				purchaseService.openReceipt(this.paymentId);
			} else {
				ClubPaymentsService.openReceipt(this.paymentId);
			}
		};
	}]
});