angular.module('SportWrench').service('ClubCheckinService', ['$http', '$q', '$uibModal', '_', ClubCheckinService])

function ClubCheckinService ($http, $q, $uibModal, _) {
	this.$http 		= $http;
	this.$q 		= $q;
	this.$modal 	= $uibModal;
	this._ 			= _;
	this.urlPrefix 	= '/api/club/event/';
	this.resource 	= '/online-checkin/';
}

ClubCheckinService.prototype.teamsList = function (eventId) {
	return this.$http.get(this.urlPrefix + eventId + this.resource + 'teams').then(function (resp) {
		return resp && resp.data.teams;
	})
}

ClubCheckinService.prototype.eventStaffers = function (eventId) {
	return this.$http.get(this.urlPrefix + eventId + this.resource + 'staffers').then(function (resp) {
		return resp && resp.data.staffers;
	})
}

ClubCheckinService.prototype.applyOnlineCheckin = function (eventId, data) {
	return this.$http.post(this.urlPrefix + eventId + this.resource + 'apply', data)
}

ClubCheckinService.prototype.validateTeam = function (eventId, teamId) {
	return this.$http.get(this.urlPrefix + eventId + this.resource + 'validate/' + teamId);
}

ClubCheckinService.prototype.validateTeamsRosters = function (eventId, teamsList) {
	return this.$http.post(this.urlPrefix + eventId + '/teams/validate-roster', { teams: teamsList })
}

ClubCheckinService.prototype.getErrorsQty = function (errorsObj) {
	return Object.keys(errorsObj).reduce(function (qty, errorType) {
		return (qty + errorsObj[errorType].length);
	}, 0);
}

ClubCheckinService.prototype.resendPrimaryStaffEmail = function (teamID, staffID, data) {
    return this.$http.post(this.resource + staffID + '/' + teamID + '/resend', data);
}

ClubCheckinService.prototype.__filterTeamsWithErrors__ = function (teamsErrorsList, teamsList, findTeamObject) {

    if (!this._.isEmpty(teamsErrorsList)) {
         return teamsErrorsList.reduce((res, errorsObj, index) => {
            if (!this._.isEmpty(errorsObj)) {
                var teamID = teamsList[index];
                var team = findTeamObject(teamID);

                res.push({ errors: errorsObj, team: team });
            }

            return res;
        }, []); 
    } 

    return [];
}

ClubCheckinService.prototype.validateTeams = function (eventId, teamsList, getTeam) {
	return this.validateTeamsRosters(eventId, teamsList)
	.then(function (resp) {

		var teams = resp.data.teams || [];

		if (this._.isFunction(getTeam)) {
			return this.__filterTeamsWithErrors__(teams, teamsList, getTeam);
		} else {
			return teams;
		}

	}.bind(this))
}

ClubCheckinService.prototype.openValidationErrorsModal = function (errors) {
	return this.$modal.open({
		templateUrl: 'club/event-managing/checkin/validation-list.html',
		controller: ['$scope', function ($scope) {
			$scope.errors = errors
		}]
	}).result
}

ClubCheckinService.prototype.openEditStaffDataModal = function({ email, phonem, id }, onSend) {
    return this.$modal.open({
        template: `
                    <resend-barcode-staff-data-edit
                        email="email"
                        phone="phone"
                        staff-id="id"
                        on-send="onSend(id, data)"
                        on-close="$close()"  
                    >
                    </resend-barcode-staff-data-edit>
        `,
        controller: ['$scope', function ($scope) {
            $scope.email = email;
            $scope.phone = phonem;
            $scope.onSend = onSend;
            $scope.id = id;
        }]
    })
};
