
class Controller {
    constructor (CDPaymentCardService, toastr, $stateParams, STRIPE_PAYMENT_TYPE) {
        this.CDPaymentCardService = CDPaymentCardService;
        this.toastr = toastr;
        this.eventID = $stateParams.event;
        this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;
    }

    $onInit () {
        this.cards = [];
        this.isLoading = false;

        this.getCustomerPaymentMethods();
    }

    getLast4 (paymentMethod) {
        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.CARD) {
            return paymentMethod.card_last_4
        }

        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.ACH) {
            return paymentMethod.bank_account_last_4
        }

        return ''
    }

    getLabel (paymentMethod) {
        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.CARD) {
            return `${paymentMethod.card_exp_month}/${paymentMethod.card_exp_year}`
        }

        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.ACH) {
            return paymentMethod.bank_name
        }

        return ''
    }

    getCardBrandClass (cardBrand) {
        return this.CDPaymentCardService.getCardBrandClass(cardBrand);
    }

    getCustomerPaymentMethods () {
        this.isLoading = true;

        return this.CDPaymentCardService.getPaymentCards()
            .then(data => {
                if(!data || !data.paymentCards) {
                    this.cards = [];
                }

                this.cards = data.paymentCards;
            })
            .finally(() => this.isLoading = false);
    }

    removePaymentMethod (card) {
        if(card.disabledForRemove) {
            return;
        }

        card.disabledForRemove = true;

        return this.CDPaymentCardService.removePaymentCard(card.stripe_payment_method_id)
            .then(() => {
                this.cards = this.cards.filter(c => c.stripe_payment_method_id !== card.stripe_payment_method_id);

                this.toastr.success('Success');
            })
            .catch(err => {
                card.disabledForRemove = false;

                console.error(err);
            })
    }
}

Controller.$inject = ['CDPaymentCardService', 'toastr', '$stateParams', 'STRIPE_PAYMENT_TYPE'];

angular.module('SportWrench').component('cdPaymentCardsList', {
    templateUrl: 'club/stripe/payment-card/payment-cards-list/template.html',
    controller: Controller
});
