angular.module('SportWrench').service('CDPaymentCardService', [
    '$http', '$uibModal', 'STRIPE_PAYMENT_TYPE', '$stateParams', '$location', CDPaymentCardService
]);

function CDPaymentCardService ($http, $uibModal, STRIPE_PAYMENT_TYPE) {
    this._$http = $http;
    this._$uibModal = $uibModal;
    this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;
}

Object.defineProperty(CDPaymentCardService.prototype, 'CARD_BRAND_ICON', {
    value: {
        visa: 'fa-cc-visa',
        unionpay: 'fa-credit-card',
        mastercard: 'fa-cc-mastercard',
        jcb: 'fa-cc-jcb',
        discover: 'fa-cc-discover',
        diners_club: 'fa-cc-diners-club',
        cartes_bancaires: 'fa-credit-card',
        amex: 'fa-cc-amex'
    },
    writable        : false,
    configurable    : false
});

Object.defineProperty(CDPaymentCardService.prototype, 'UNKNOWN_CARD_BRAND_ICON', {
    value: 'fa-credit-card',
    writable: false,
    configurable: false
});

CDPaymentCardService.prototype.getCardBrandClass = function (cardBrand) {
    if(cardBrand && !Object.keys(this.CARD_BRAND_ICON).includes(cardBrand)) {
        return this.UNKNOWN_CARD_BRAND_ICON;
    }

    return this.CARD_BRAND_ICON[cardBrand];
}

CDPaymentCardService.prototype.getCardLabel = function (card) {
    if(_.isEmpty(card) || !_.isObject(card)) {
        return;
    }

    let { card_brand, card_last_4, card_exp_month, card_exp_year, holder_name, type, bank_name, bank_account_last_4 } = card;

    if(type === this.STRIPE_PAYMENT_TYPE.CARD) {
        return `${holder_name} - ${card_brand.toUpperCase()}  **** ${card_last_4} (${card_exp_month}/${card_exp_year})`;
    }

    if(type === this.STRIPE_PAYMENT_TYPE.ACH) {
        return `${holder_name} - ${bank_name}  **** ${bank_account_last_4}`;
    }
    
    return ''
}

CDPaymentCardService.prototype.openPaymentCardModal = function () {
    return this._$uibModal.open({
        template: '<modal-wrapper><cd-payment-card-modal active-tab="0"></cd-payment-card-modal></modal-wrapper>',
        controller: ['$scope', function ($scope) {
            $scope.modalTitle = '<h4>Payment Cards</h4>';
            $scope.modalShowClose = true;
        }]
    }).result;
}

CDPaymentCardService.prototype.getPaymentCards = function () {
    return this._$http.get('/api/club/v2/payment-card', {
        paramSerializer: '$httpParamSerializerJQLike'
    }).then(response => {
        return response.data;
    })
}

CDPaymentCardService.prototype.removePaymentCard = function (paymentMethodID) {
    return this._$http.delete('/api/club/v2/payment-card', { data: { payment_method_id: paymentMethodID } });
}

CDPaymentCardService.prototype.setDefaultPaymentCard = function (paymentMethodID) {
    return this._$http.put('/api/club/v2/payment-card', { payment_method_id: paymentMethodID });
}
