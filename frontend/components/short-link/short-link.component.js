angular.module('SportWrench').component('shortLink', {
        templateUrl: 'components/short-link/short-link.html',
        bindings: {
            href: '<',
            target: '@',
            title: '@',
            'class': '@',
        },
        controller: [
            function() {
                let a = document.createElement('a');
                const shortenLink = (link) => {
                    if(!link) {
                        return link;
                    }
                    a.href = link;
                    const more = a.pathname.length>1 || a.search;
                    return `${a.origin}${more?'/...':''}`;
                };

                this.$onInit = () => {
                    this.target = this.target || '_blank';
                };

                this.$onChanges = (changes) => {
                    if (changes.href) {
                        this.text = shortenLink(changes.href.currentValue);
                    }
                };
            }
        ],
    }
);
