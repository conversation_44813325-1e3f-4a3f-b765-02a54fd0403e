angular.module('SportWrench').directive('floatNumber', floatNumber)

function floatNumber() {
	return {
		require: 'ngModel',
        link: function (scope, elem, attrs, ngModel) {
            const isAllowNegative   = attrs.floatNumber === 'allow-negative'
            const regex             = isAllowNegative ? /^-?\d*\.?\d{0,2}$/ : /^\d*\.?\d{0,2}$/;

             ngModel.$parsers.push(function(value) {
                if (value === '') {
                    return 0;
                }

                const isAllow = regex.test(value);

                if (!isAllow) {
                    const _value = ngModel.$modelValue === 0 ? '' : ngModel.$modelValue;

                    ngModel.$setViewValue(_value);
                    ngModel.$render();

                    return ngModel.$modelValue;
                }

                ngModel.$setViewValue(value);
                ngModel.$render();

                return value;
             });
        }
	}
}
