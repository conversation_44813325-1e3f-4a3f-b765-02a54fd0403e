<div ng-class="{
    'form-group': true,
    'custom-form-validation-required': $ctrl.field.is_required,
    'has-error': $ctrl.fieldHasError({field: $ctrl.field}) }">
    <label class="col-sm-offset-1 col-sm-10" style="font-weight: normal">
        <input type="checkbox"
               ng-model="$ctrl.value"
               name="{{$ctrl.field.name}}"
               ng-required="$ctrl.field.is_required"
               aria-describedby="checkboxHelpBlock"/>
        <span class="control-label" ng-bind="$ctrl.field.label"></span>
    </label>
</div>
