<div ng-class="{
    'form-group': true,
    'custom-form-validation-required': $ctrl.field.is_required,
    'has-error': $ctrl.fieldHasError({field: $ctrl.field})}">
    <label class="control-label col-xs-4" data-ng-bind="$ctrl.field.label"></label>
    <div class="col-sm-7">
        <canvas id="{{$ctrl.getID()}}"
                class="signature-pad"
                name="{{$ctrl.field.name}}"
                height=200
                aria-describedby="signatureHelpBlock"
        ></canvas>
    </div>
    <div class="col-xs-7 col-xs-offset-4">
        <button class="btn btn-default btn-xs pull-left col-xs-2 signature-pad-clear"
                type="button"
                ng-click="$ctrl.clear()"
        >Clear</button>
    </div>
    <div class="col-xs-7 col-xs-offset-4">
        <span id="signatureHelpBlock"
              class="help-block"
              style="font-weight: normal"
              ng-show="$ctrl.field.help_text"
              ng-bind="$ctrl.field.help_text"></span>
    </div>
</div>
