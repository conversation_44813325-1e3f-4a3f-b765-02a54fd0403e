angular.module('SportWrench').component('eventApplicationBlock', {
    templateUrl: 'components/member-info/info-blocks/event-application/event-application-block.html',
    bindings: {
        member          : '<',
        paymentMethods  : '<',
        eventDates      : '<'
    },
    controller: ['TRAVEL_METHOD', 'PAYMENT_OPTIONS',  Component]
});

function Component(TRAVEL_METHOD, PAYMENT_OPTIONS) {
    this.DIRECT_DEPOSIT_METHOD  = 'direct_deposit';
    this.PAYMENT_OPTIONS = PAYMENT_OPTIONS;
    this._avCache               = {};

    this.getDates = () => {
        if (this.member.hotel_nights_required) {
            this.dates = _.map(this.member.hotel_nights_required.dates, function (value, date) {
                return {date: moment(date, 'MMDDYYYY').format('MM/DD', 'en'), decision: value?'Yes':'No'};
            })
        }
    };

    this.getDate = (date) => {
        return moment(date).toDate();
    };

    this.convertToUTC = (date) => {
        return moment.utc(date).toDate();
    };

    this.getAvailability = (dayIndex) => {

        dayIndex = dayIndex + 1;

        return this._avCache[dayIndex] ||
            (this._avCache[dayIndex] = (Number(this.member.schedule_availability['day_' + dayIndex]) === 1) ? 'Yes' : 'No');
    };

    this.isLastDayPicked = () => {
        return this.getAvailability(this.eventDates.length - 1) === 'Yes';
    };

    this.showBankAccountNumber = () => {
        return this.member.bank_account_number && this.member.payment_option === this.DIRECT_DEPOSIT_METHOD;
    };

    this.showBankAccountRouting = () => {
        return this.member.bank_account_routing && this.member.payment_option === this.DIRECT_DEPOSIT_METHOD;
    };

    this.showNeedHotelRoom = () => {
        return this.member.need_hotel_room != null;
    };

    this.showNeedHotelNights = () => {
        return this.member.need_hotel_room && this.member.hotel_nights_required != null;
    };

    this.showRoommatePreferences = () => {
      return this.member.roommate_preference &&   this.member.need_hotel_room;
    };

    this.showDepartureDatetime = () => {
        return this.member.departure_datetime;
    };

    this.getTravelMethodLabel = () => {
        return TRAVEL_METHOD[this.member.travel_method];
    };

    this.getNeedHotelRoomLabel = () => {
        return this.member.need_hotel_room ? 'YES' : 'NO';
    };

    this.isStaff = () => {
        return this.member.is_staff;
    };

    this.isOfficial = () => {
        return this.member.is_official;
    };

    this.showArrivalDatetime = () => {
        return this.member.arrival_datetime && this.isStaff();
    };

    this.showScheduleAvailability = () => {
        return this.member.schedule_availability && this.isOfficial();
    };

    this.showArbiterpayUsername = () => {
        return this.member.arbiterpay_username && this.member.payment_option === this.PAYMENT_OPTIONS.arbiterPay;
    };

    this.showArbiterpayAccountNumber = () => {
        return this.member.arbiterpay_account_number && this.member.payment_option === this.PAYMENT_OPTIONS.arbiterPay;
    };

    this.showRqPayUsername = () => {
        return this.member.rq_pay_username && this.member.payment_option === this.PAYMENT_OPTIONS.rqPay;
    };
}
