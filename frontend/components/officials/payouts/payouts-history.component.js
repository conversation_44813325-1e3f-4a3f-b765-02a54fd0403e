angular.module('SportWrench').component('payoutsHistory', {
    templateUrl     : 'components/officials/payouts/payouts-history.html',
    bindings        : {
        getPayoutsHistory 	: '&',
    	history 		 	: '<',
        total 				: '<'
    },
    controller      : ['$rootScope', payoutsHistoryContoller]
});

function payoutsHistoryContoller ($rootScope) {
    $rootScope.$on('new-payout-made', () => {
        this.getPayoutsHistory();
    });
}