<div class="panel panel-default">
    <div class="panel-body">
        <form name="$ctrl.eventOfficialForm" ng-submit="$ctrl.submit()">
            <fieldset ng-disabled="$ctrl.eventOfficialForm.$submitted">
            <div ng-if="!$ctrl.official.deleted" class="col-sm-offset-2 form-group">
                <member-work-status
                    member="$ctrl.official"
                    form="$ctrl.eventOfficialForm"
                    work-status-cache="$ctrl.officialCache.work_status"
                    on-changed="$ctrl.changesListener(isChanged)"
                    member-type="official"
                >
                </member-work-status>
            </div>
            <div class="form-group">
                <div class="row">
                    <div class="col-sm-6">
                        <label>Rank:</label>
                        <select 
                            class="form-control" 
                            name="rank" 
                            ng-model="$ctrl.official.rank" 
                            ng-options="r.id as r.name for r in $ctrl.ratings" 
                            ng-change="$ctrl.onRankChange()"
                            required>
                            <option value="">Select Rank ...</option>
                        </select>
                        <p class="help-block" ng-if="$ctrl.eventOfficialForm.rank.$modified">
                            <span>Click to restore: </span>
                            <span 
                                class="label label-warning pointer" 
                                ng-bind="$ctrl.officialCache.rank"
                                ng-click="$ctrl.restoreDefaultRank()">Warning</span>
                        </p>
                    </div>
                    <div class="col-sm-6">
                        <label>Schedule Name:</label>
                        <input 
                            type="text"
                            name="schedule_name" 
                            class="form-control" 
                            ng-model="$ctrl.official.schedule_name" 
                            ng-model-options="{ debounce: 500 }"
                            ng-change="$ctrl.onSchNameChange()"
                        >
                        <p class="help-block" ng-if="$ctrl.eventOfficialForm.schedule_name.$modified">
                            <span>Click to restore: </span>
                            <span 
                                class="label label-warning pointer" 
                                ng-bind="$ctrl.officialCache.schedule_name || 'N/A'"
                                ng-click="$ctrl.restoreDefaultSchName()">Warning</span>
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6" ng-if="$ctrl.eventOfficialAdditionalRoleEnabled && $ctrl.official.official_additional_role_id">
                        <label>Role:</label>
                        <select
                            class="form-control"
                            name="official_additional_role_id"
                            ng-model="$ctrl.official.official_additional_role_id"
                            ng-options="r.id as r.name for r in $ctrl.officialAdditionalRoles"
                            ng-change="$ctrl.onRoleChange()"
                            required>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group" ng-if="$ctrl.eventOfficialForm.$modified.length > 0">
                <div class="row">
                    <div class="col-sm-10">
                        <input 
                            type="text" 
                            class="form-control" 
                            placeholder="Add Notes that describe Changes ..." 
                            ng-model="$ctrl.changesNotes"
                            ng-disabled="true"
                            ng-if="false">
                    </div>
                    <div class="col-sm-2">
                        <button type="submit" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </div>
            </fieldset>
        </form>
    </div>
</div>
