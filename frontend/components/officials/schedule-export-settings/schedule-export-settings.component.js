angular.module('SportWrench').controller('scheduleExportSettings', [
    '$scope', '$stateParams', '$httpParamSerializer', '$window',
    scheduleExportSettingsController,
]);

function scheduleExportSettingsController ($scope, $stateParams, $httpParamSerializer, $window) {
    $scope.modalTitle = `<div class="text-left"><strong>What type of export do you need?</strong></div>`;
    this.exportTypes = [
        {
            label: 'with match code',
            query: {
                include_match_codes: 'true',
            },
        },
        {
            label: `without match code`,
            query: {
                include_match_codes: 'false',
            },
        },
    ];
    this.exportType = this.exportTypes[0];
    this.export = () => {
        $window.open(`/api/schedule/${$stateParams.event}/export?${$httpParamSerializer(this.exportType.query)}`, '_blank');
        $scope.$close();
    };
}
