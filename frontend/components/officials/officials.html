<form class="form form-inline row-space">
    <div class="form-group">
        <searchbox
            search="filters.search"
            status-changed="onFilterChange(status, filter)"
        ></searchbox>
    </div>
    <div class="form-group">  
        <work-status
            work-status="filters.work_status"
            status-changed="onFilterChange(status, filter)"
            show-profile-option="true"
            show-participation-confirm="true"
        ></work-status>
    </div>
    <div class="form-group">
        <hotel-filter
            hotel="filters.hotel"
            status-changed="onFilterChange(status, filter)"
        ></hotel-filter>
    </div>
    <div class="form-group" ng-if="eventDaysExists()">
        <label>Days:</label>
        <event-days-dd
            list-title="Available"
            selection="filters.daysSelection"
            event-days="eventDays"
        ></event-days-dd>
    </div>
    <div class="form-group" ng-if="eventDaysExists()">
        <event-days-dd
            list-title="Not Available"
            selection="filters.notAvailableDays"
            event-days="eventDays"
        ></event-days-dd>
    </div>
    <div class="form-group" ng-if="eventOfficialAdditionalRoleEnabled">
        <additional-role
            selection="filters.additionalRolesSelection"
            official-additional-roles="officialAdditionalRoles"
        ></additional-role>
    </div>
    <div class="form-group member-action">
        <member-actions actions="actions"></member-actions>
    </div>
</form>
<div ng-if="officials.length">
    <member-group-operations
        total-count="getLength()"
        selected-count="selection.total_checked"
        group-work-status="utils.group_work_status"
        show-withdrawn="showWithdrawn"
        on-save="changeWorkStatus()"
        on-send-email="openEmailDialog()"
        on-export="excelExport()"
    >
    </member-group-operations>
</div>
<table class="table table-condensed officials-table" sticky-header>
    <thead class="pointer">
        <tr>
            <th>
                <input type="checkbox" ng-model="selection.all_selected" ng-change="select_all()">
            </th>
            <th ng-click="changeOrder('first')">
                First <reverse-arrow reverse="order.reverse" show="{{order.column === 'first'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('last')">
                Last <reverse-arrow reverse="order.reverse" show="{{order.column === 'last'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('email')">
                Email <reverse-arrow reverse="order.reverse" show="{{order.column === 'email'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('state')">
                State <reverse-arrow reverse="order.reverse" show="{{order.column === 'state'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('need_hotel_room')">
                Hotel <reverse-arrow reverse="order.reverse" show="{{order.column === 'need_hotel_room'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('security_pin')">
                Pin <reverse-arrow reverse="order.reverse" show="{{order.column === 'security_pin'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('rank')">
                Rank <reverse-arrow reverse="order.reverse" show="{{order.column === 'rank'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('official_additional_role')" ng-if="eventOfficialAdditionalRoleEnabled">
                Role <reverse-arrow reverse="order.reverse" show="{{order.column === 'official_additional_role'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('work_status_cap')">
                Work Status <reverse-arrow reverse="order.reverse" show="{{order.column === 'work_status_cap'}}"></reverse-arrow>
            </th>
            <th class="text-center" ng-click="changeOrder('is_official_participation_confirmed')">
                Confirm <reverse-arrow reverse="order.reverse" show="{{order.column === 'is_official_participation_confirmed'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('usav')" ng-if="eventHasUSAVSanctioning()">
                USAV <reverse-arrow reverse="order.reverse" show="{{order.column === 'usav'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('aau')" ng-if="eventHasAAUSanctioning()">
                AAU <reverse-arrow reverse="order.reverse" show="{{order.column === 'aau'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('background_screening')" ng-if="eventHasUSAVSanctioning()">
                BKG
                <reverse-arrow reverse="order.reverse" show="{{order.column === 'background_screening'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('aau_bg_screening')" ng-if="eventHasAAUSanctioning()">
                BKG
                <reverse-arrow reverse="order.reverse" show="{{order.column === 'aau_bg_screening'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('safesport_status')" ng-if="eventHasUSAVSanctioning()">
                SS
                <reverse-arrow reverse="order.reverse" show="{{order.column === 'safesport_status'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('aau_safesport_status')" ng-if="eventHasAAUSanctioning()">
                SS
                <reverse-arrow reverse="order.reverse" show="{{order.column === 'aau_safesport_status'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('use_clinic')" ng-if="eventUseClinic">
                Clinic <reverse-arrow reverse="order.reverse" show="{{order.column === 'use_clinic'}}"></reverse-arrow>
            </th>
            <th ng-click="changeOrder('has_restrictions')" width="30">
                Comments <reverse-arrow reverse="order.reverse" show="{{order.column === 'has_restrictions'}}"></reverse-arrow>
            </th>
        </tr>
    </thead>
    <tbody ng-click="openInfoModal($event)">
        <tr ng-repeat="of in filteredOfficials | orderBy:['!is_email_contact_provider', '!is_email_notifications_receiver'] track by $index" 
            of-id="{{of.official_id}}"
            ng-class="{'pointer': isPerson(of), 'head-official': of.head_official, 'alert-disabled': !isPerson(of) || of.deleted}">
            <td ng-click="$event.stopPropagation();">
                <input type="checkbox" ng-model="of.checked" ng-change="toggleOfficialSelection(of)">
            </td>
            <td ng-if="isPerson(of)">
                <i class="fa fa-envelope-o" aria-hidden="true" ng-if="of.is_email_notifications_receiver">&nbsp;</i>
                <i class="fa fa-address-book-o" aria-hidden="true" ng-if="of.is_email_contact_provider"></i>
                {{of.first}}</td>
            <td ng-if="isPerson(of)">{{of.last}}
                <i ng-if="of.schedule_name_duplicates"
                   style="color: red"
                   uib-tooltip="Staff has schedule name duplicate"
                   class="fa fa-exclamation-triangle"
                   aria-hidden="true"></i>
            </td>
            <td ng-if="isPerson(of)">{{of.email}}</td>
            <td ng-if="isPerson(of)">{{of.state}}</td>
            <td colspan="4" class="text-center" ng-if="!isPerson(of)"><em>Auto generated pin</em></td> 
            <td><input type="checkbox" ng-checked="of.need_hotel_room" ng-disabled="true"></td>
            <td>{{of.security_pin || '-'}}</td>
            <td>{{of.rank}}</td>
            <td ng-if="eventOfficialAdditionalRoleEnabled">{{of.official_additional_role}}</td>
            <td><span ng-style="of.deleted?{ 'text-decoration': 'underline'}:''" uib-tooltip="{{of.deleted | UTCdate: 'MM/DD/YYYY h:mm a'}}">{{of.work_status_cap}}</span></td>
            <td>
                <div class="text-center" uib-tooltip="{{of.is_official_participation_confirmed}}">
                    <input type="checkbox" ng-checked="of.is_official_participation_confirmed" ng-disabled="true">
                </div>
            </td>
            <td ng-click="openSanctioningCheckUpdateModal($event, of)" ng-if="eventHasUSAVSanctioning()">
                <table-field-error profile-info="of" role="OFFICIAL_MEMBER_TYPE" field="MBR_FIELD"></table-field-error>
            </td>
            <td ng-click="openSanctioningCheckUpdateModal($event, of)" ng-if="eventHasAAUSanctioning()">
                <table-field-error profile-info="of" role="OFFICIAL_MEMBER_TYPE" field="AAU_FIELD"></table-field-error>
            </td>
            <td ng-click="openSanctioningCheckUpdateModal($event, of)" ng-if="eventHasUSAVSanctioning()">
                <table-field-error profile-info="of" role="OFFICIAL_MEMBER_TYPE" field="BG_FIELD"></table-field-error>
            </td>
            <td ng-click="openSanctioningCheckUpdateModal($event, of)" ng-if="eventHasAAUSanctioning()">
                <table-field-error profile-info="of" role="OFFICIAL_MEMBER_TYPE" field="AAU_BG_FIELD"></table-field-error>
            </td>
            <td ng-click="openSanctioningCheckUpdateModal($event, of)" ng-if="eventHasUSAVSanctioning()">
                <table-field-error profile-info="of" role="OFFICIAL_MEMBER_TYPE" field="SAFESPORT_FIELD"></table-field-error>
            </td>
            <td ng-click="openSanctioningCheckUpdateModal($event, of)" ng-if="eventHasAAUSanctioning()">
                <table-field-error profile-info="of" role="OFFICIAL_MEMBER_TYPE" field="AAU_SAFESPORT_FIELD"></table-field-error>
            </td>
            <td  ng-if="eventUseClinic"><input ng-click="$event.stopPropagation()" ng-change="updateOfficialIsClinic(of)" type="checkbox" ng-checked="of.use_clinic" ng-model="of.use_clinic"></td>
            <td ng-click="openEditModal($event, of)" class="additional-restriction-edit">
                <span uib-tooltip="{{of.additional_restrictions}}" class="content">{{ of.additional_restrictions }}</span>
                <i class="fa fa-pencil-square-o edit_button" aria-hidden="true"></i>
            </td>
        </tr>
    </tbody>
</table>
