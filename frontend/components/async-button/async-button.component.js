class AsyncButtonComponent {
    constructor() {
        this.SPINNER_WIDTH = 15;

        this.inProcess = false;
    }

    $onInit() {
        this.setButtonWidth();
    }

    setButtonWidth() {
        const btn = document.getElementById('async-button');

        const currentWidth = angular.element(btn)[0].clientWidth;
        const widthWithSpinner = currentWidth + this.SPINNER_WIDTH;

        btn.style.width = `${widthWithSpinner}px`;
    }

    onFinally() {
        this.inProcess = false;
    }

    click() {
        if (this.inProcess) {
            return;
        }

        const promise = this.onClick();

        if (!promise) {
            return;
        }

        this.inProcess = true;

        promise.finally(this.onFinally.bind(this));
    }
}

angular.module('SportWrench').component('asyncButton', {
    templateUrl: 'components/async-button/async-button.html',
    bindings: {
        onClick: '&',
        btnClass: '@',
        title: '@',
    },
    controller: [
        AsyncButtonComponent
    ]
});

