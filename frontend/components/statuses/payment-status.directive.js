angular.module('SportWrench').directive('paymentStatus', paymentStatus);

function paymentStatus () {
	var CLASSES = {
		pending 		: 'text-info',
		pending_card	: 'text-info',
		pending_check	: 'text-info',
		paid 			: 'text-success',
		canceled 		: 'text-danger'

	},
	DEFAULT_CLASS = 'text-warning';

	return {
		restrict	: 'E',
		scope 		: { 
			status: '@' 
		},
		templateUrl : 'components/statuses/payment-status.html',
		replace 	: true,
		link: function (scope) {
			scope.statusClass = function () {
				return CLASSES[scope.status] || DEFAULT_CLASS;
			}
		}
	}
}