<uib-alert type="danger" ng-if="$ctrl.loading.error">{{ $ctrl.loading.errorMessage }}</uib-alert>
<spinner active="!$ctrl.loading.success"></spinner>

<div ng-if="$ctrl.loading.success">
    <div class="modal-body">
        <div class="panel panel-default" ng-if="!$ctrl.staff.staff_deleted">
            <div class="panel-body">
                <form name="$ctrl.eventStaffForm" ng-submit="$ctrl.submit()">
                    <fieldset ng-disabled="$ctrl.eventStaffForm.$submitted">
                        <div class="col-sm-offset-2 form-group">
                            <member-work-status
                                member="$ctrl.staff"
                                form="$ctrl.eventStaffForm"
                                work-status-cache="$ctrl.workStatusCache"
                                on-changed="$ctrl.changesListener(isChanged)"
                                member-type="staff"
                            >
                            </member-work-status>
                        </div>
                        <div class="form-group" ng-if="$ctrl.eventStaffForm.$modified.length > 0">
                            <div class="row">
                                <div class="col-sm-2 col-sm-offset-10">
                                    <button type="submit" class="btn btn-primary">Save</button>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
        </div>
        <member-info
            member="$ctrl.staff"
            payment-methods="$ctrl.event.payment_methods"
            event-dates="$ctrl.event.days_dates"
            clothes="$ctrl.clothes"
        >
        </member-info>
    </div>

    <div class="modal-footer">
        <button class="btn btn-default" ng-click="$ctrl.onClose()">Close</button>
    </div>
</div>
