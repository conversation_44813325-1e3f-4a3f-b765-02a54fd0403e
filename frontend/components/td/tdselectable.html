<div ng-switch on="isShown">
    <div ng-switch-when="false">
        {{findName(val)}}
    </div>
    <div ng-switch-when="true">
        <select
            ng-show="divisions.length > 0" 
            class="form-control input-sm padding-sm padding-sm-vertical"
            ng-model="val"         
            ng-options=" d.id as _label(d) for d in divisions | filter:divisions_filter()"
            ng-change="changeValue(val, '{{item.division_id}}');"       
            >
        </select>
        <p ng-show="!divisions || !divisions.length">Loading...</p>
    </div>
</div>
