angular.module('SportWrench')
    .directive('fileChange', ['$timeout', fileChange]);

function fileChange($timeout) {
    return {
        restrict: 'A',
        require: 'ngModel',
        scope: {
            fileChange: '&'
        },
        link: function link(scope, element, attrs, ctrl) {
            element.on('change', onChange);

            scope.$on('destroy', function () {
                element.off('change', onChange);
            });

            function onChange() {
                $timeout(() => {
                    ctrl.$setViewValue(element[0].files[0]);
                    scope.fileChange();
                });
            }
        }
    };
}
