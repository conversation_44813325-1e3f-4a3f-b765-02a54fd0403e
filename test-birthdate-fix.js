const moment = require('moment');

const PARSE_DATE_FORMAT = 'MM/DD/YYYY';

function parseBirthdate(dateValue) {
    if (!dateValue) {
        return moment.utc(); // Invalid moment
    }
    
    // If it's a number (Excel serial date), convert to JavaScript Date first
    if (typeof dateValue === 'number') {
        // Excel serial date: days since January 1, 1900 (with leap year bug adjustment)
        // JavaScript Date: milliseconds since January 1, 1970
        const excelEpoch = new Date(1900, 0, 1); // January 1, 1900
        const jsDate = new Date(excelEpoch.getTime() + (dateValue - 2) * 24 * 60 * 60 * 1000);
        return moment.utc(jsDate);
    }
    
    // If it's a string, parse with the expected format
    if (typeof dateValue === 'string') {
        return moment.utc(dateValue, PARSE_DATE_FORMAT);
    }
    
    // Try to parse as-is for other cases
    return moment.utc(dateValue);
}

// Test cases
console.log('Testing birthdate parsing...');

// Test with Excel serial number (40522 should be 12/10/2010)
const excelSerialDate = 40522;
const parsedFromSerial = parseBirthdate(excelSerialDate);
console.log(`Excel serial ${excelSerialDate} -> ${parsedFromSerial.format('MM/DD/YYYY')} (valid: ${parsedFromSerial.isValid()})`);

// Test with string date
const stringDate = '12/10/2010';
const parsedFromString = parseBirthdate(stringDate);
console.log(`String date "${stringDate}" -> ${parsedFromString.format('MM/DD/YYYY')} (valid: ${parsedFromString.isValid()})`);

// Test with invalid input
const invalidDate = null;
const parsedInvalid = parseBirthdate(invalidDate);
console.log(`Invalid date ${invalidDate} -> valid: ${parsedInvalid.isValid()}`);

console.log('Test completed!');
