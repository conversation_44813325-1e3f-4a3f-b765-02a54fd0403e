module.exports = function(grunt) {

	grunt.config.set('ngtemplates', {
        SportWrench: {
            options: {
                concat  : 'frontend',
                append  : true,
                module  : 'SportWrench',
                htmlmin : {
                    collapseWhitespace          : true,
                    removeComments              : true,
                    //collapseInlineTagWhitespace : true,
                    decodeEntities              : true,
                    minifyCSS                   : true,
                    minifyJS                    : true,
                    processScripts              : ['text/ng-template']
                }                
            },
            cwd: 'frontend/',
            src: ['**/*.html'],
            dest: '.tmp/public/scripts/main.js'
        },
        EventsSportWrench: {
            options: {
                concat: 'frontend_event',
                append: true,
                module: 'SportWrench',
                htmlmin: {
                    collapseWhitespace: true,
                }
            },
            cwd: 'frontend_event/',
            src: ['**/*.html'],
            dest: '.tmp/public/scripts/main.js'
        },
        AdminSportWrench: {
            options: {
                concat: 'frontend_admin',
                append: true,
                module: 'SportWrench'
            },
            cwd: 'frontend_admin/',
            src: ['**/*.html'],
            dest: '.tmp/public/scripts/main.js'
        },
        tickets: {
            options: {
                concat: 'frontend_event',
                append: true,
                module: 'TicketsApp',
                htmlmin: {
                    collapseWhitespace: true,
                }
            },
            cwd: 'frontend_tickets/',
            src: ['**/*.html'],
            dest: '.tmp/public/scripts/main.js'
        },
        loc: {
            options: {
                concat: 'frontend',
                append: true,
                module: 'SportWrench'
            },
            cwd: 'frontend/',
            src: ['**/*.html'],
            dest: '.tmp/public/scripts/main.js'
        },


        SportWrench_dev: {
            options: {
                concat  : 'frontend',
                append  : true,
                module  : 'SportWrench',
                htmlmin : {
                    collapseWhitespace          : true,
                    removeComments              : true,
                    //collapseInlineTagWhitespace : true,
                    decodeEntities              : true,
                    minifyCSS                   : true,
                    minifyJS                    : true,
                    processScripts              : ['text/ng-template']
                }                
            },
            cwd: 'frontend/',
            src: ['**/*.html'],
            dest: '.tmp/public2/scripts/main.js'
        },
        EventsSportWrench_dev: {
            options: {
                concat: 'frontend_event',
                append: true,
                module: 'SportWrench',
                htmlmin: {
                    collapseWhitespace: true,
                }
            },
            cwd: 'frontend_event/',
            src: ['**/*.html'],
            dest: '.tmp/public2/scripts/main.js'
        },
        AdminSportWrench_dev: {
            options: {
                concat: 'frontend_admin',
                append: true,
                module: 'SportWrench'
            },
            cwd: 'frontend_admin/',
            src: ['**/*.html'],
            dest: '.tmp/public2/scripts/main.js'
        },

	});

	grunt.loadNpmTasks('grunt-angular-templates');
};
